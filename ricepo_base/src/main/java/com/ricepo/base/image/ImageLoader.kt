package com.ricepo.base.image

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.view.View
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.ricepo.base.tools.ActivityUtils
import java.io.File
import java.lang.Exception

//
// Created by <PERSON><PERSON> on 24/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object ImageLoader {

  const val PRIORITY_IMMEDIATE = 0

  fun load(imageView: ImageView?, url: String?, placeholderId: Int = 0) {
    val builder: RequestBuilder<Drawable>? = loadBuilder(imageView, url, placeholderId)
    imageView?.let {
      builder?.into(it)
    }
  }

  /**
   * load large image
   */
  fun loadLargeImage(
    imageView: ImageView?,
    url: String?,
    placeholderId: Int = 0,
    maxWidth: Int = 1920,
    maxHeight: Int = 1080,
    onSuccess: (() -> Unit)? = null,
    onError: ((Exception?) -> Unit)? = null
  ) {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      onError?.invoke(Exception("ImageView is null or activity is destroyed"))
      return
    }

    val builder = Glide.with(imageView)
      .load(url)
      .override(maxWidth, maxHeight)
      .downsample(DownsampleStrategy.AT_MOST)
      .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
      .skipMemoryCache(false)
      .dontAnimate()

    if (placeholderId > 0) {
      ContextCompat.getDrawable(imageView.context, placeholderId)?.let {
        builder.placeholder(it)
        builder.fallback(it)
      }
    }

    // Add error handling and success callback
    builder.listener(object : RequestListener<Drawable> {
      override fun onLoadFailed(
        e: GlideException?,
        model: Any?,
        target: Target<Drawable>?,
        isFirstResource: Boolean
      ): Boolean {
        onError?.invoke(e)
        return false // Let Glide handle the error display
      }

      override fun onResourceReady(
        resource: Drawable?,
        model: Any?,
        target: Target<Drawable>?,
        dataSource: DataSource?,
        isFirstResource: Boolean
      ): Boolean {
        onSuccess?.invoke()
        return false // Let Glide handle the display
      }
    })

    builder.into(imageView)
  }

  @SuppressLint("CheckResult")
  fun load(imageView: ImageView?, url: String?, width: Int, height: Int, placeholderId: Int = 0) {
    loadBuilder(imageView, url, placeholderId)
      ?.override(width, height)
      ?.let { builder ->
        imageView?.apply {
          builder.into(this)
        }
      }
  }

  /**
   * Enhanced load method with automatic large image detection and optimization
   */
  fun loadWithOptimization(
    imageView: ImageView?,
    url: String?,
    placeholderId: Int = 0,
    enableLargeImageOptimization: Boolean = true
  ) {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      return
    }

    val builder = Glide.with(imageView).load(url)

    if (enableLargeImageOptimization) {
      // Apply optimizations for potentially large images
      builder.override(1920, 1080) // Reasonable max size
      builder.downsample(DownsampleStrategy.AT_MOST)
      builder.diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
    }

    if (placeholderId > 0) {
      ContextCompat.getDrawable(imageView.context, placeholderId)?.let {
        builder.placeholder(it)
        builder.fallback(it)
      }
    }

    builder.skipMemoryCache(false)
    builder.dontAnimate()
    builder.into(imageView)
  }

  /**
   * load the network image
   */
  @SuppressLint("CheckResult")
  private fun loadBuilder(imageView: ImageView?, url: String?, placeholderId: Int = 0):
          RequestBuilder<Drawable>? {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      return null
    }

    val builder = Glide.with(imageView).load(url)
    if (placeholderId > 0) {
      ContextCompat.getDrawable(imageView.context, placeholderId).let {
        builder.placeholder(it)
        // the url null or empty
        builder.fallback(it)
      }
    }
    // case: group order refresh gallery
    builder.skipMemoryCache(false)
    builder.dontAnimate()
    return builder
  }

  fun download(imageView: ImageView?, url: String?): Bitmap? {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      return null
    }

    return Glide.with(imageView).asBitmap()
      .load(url).submit().get()

    // downloadOnly You must call #load() before calling #into()
  }

  fun downloadBitmap(context: Context, url: String, callback: (b: Bitmap?) -> Unit) {
    Glide.with(context).asBitmap().load(url).into(object : CustomTarget<Bitmap>() {
      override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
        callback(resource)
      }

      override fun onLoadCleared(placeholder: Drawable?) {
      }
    })
  }

  fun downloadFile(context: Context, url: String, callback: (b: File?) -> Unit) {
    Glide.with(context).downloadOnly()
      .load(url)
      .addListener(object : RequestListener<File> {
        override fun onLoadFailed(
          e: GlideException?,
          model: Any?,
          target: Target<File>?,
          isFirstResource: Boolean
        ): Boolean {
          callback(null)
          return false
        }

        override fun onResourceReady(
          resource: File?,
          model: Any?,
          target: Target<File>?,
          dataSource: DataSource?,
          isFirstResource: Boolean
        ): Boolean {
          callback(resource)
          return true
        }
      }).preload()
  }

  /**
   * load the network image
   */
  fun loadCallback(
    imageView: ImageView?,
    url: String?,
    placeholderDrawable: Drawable? = null,
    loadReady: () -> Unit = {},
    loadFailed: () -> Unit = {}
  ) {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      return
    }

    val builder = Glide.with(imageView).load(url)
    if (placeholderDrawable != null) {
      builder.placeholder(placeholderDrawable)
      // the url null or empty
      builder.fallback(placeholderDrawable)
    }
    // builder.addListener ...
    // You can't start or clear loads in RequestListener or Target callbacks.
    // If you're trying to start a fallback request when a load fails, use RequestBuilder#error(RequestBuilder).
    // Otherwise consider posting your into() or clear() calls to the main thread using a Handler instead.
    builder.addListener(object : RequestListener<Drawable> {
      override fun onLoadFailed(
        e: GlideException?,
        model: Any?,
        target: Target<Drawable>?,
        isFirstResource: Boolean
      ): Boolean {
        loadFailed()
        return true
      }

      override fun onResourceReady(
        resource: Drawable?,
        model: Any?,
        target: Target<Drawable>?,
        dataSource: DataSource?,
        isFirstResource: Boolean
      ): Boolean {
        if (isFirstResource) {
          loadReady()
        }
        return false
      }
    })
    // case: group order refresh gallery
    builder.skipMemoryCache(false)
    builder.dontAnimate()
//        builder.dontTransform()
    builder.into(imageView)
  }

  /**
   * gif maybe memory leak
   * clear() view cloud free up some memory
   * Glide uses a size limited LRU memory cache. Whenever an image is cleared
   * (either explicitly or because the context is destroyed),
   * the image gets moved into the LRU cache.
   * use The RequestManager with activity or fragment
   */
  fun load(
    imageView: ImageView?,
    drawableId: Int,
    isGif: Boolean = false,
    priority: Int? = null
  ) {
    if (imageView == null) {
      return
    }
    // the DecorContext
    val context = imageView.context
    if (context is Activity) {
      val builder = with(context)?.load(drawableId)
      if (isGif) {
        // DATA with glide3 is SOURCE
        builder?.diskCacheStrategy(DiskCacheStrategy.NONE)
      }
      priority?.let {
        // for load lucky refresh gif when restaurant load other images [Priority.IMMEDIATE]
        when (it) {
          PRIORITY_IMMEDIATE -> builder?.priority(Priority.IMMEDIATE)
          else -> {}
        }
      }

      builder?.into(imageView)
    } else if (context is Fragment) {
      val builder = with(context as Fragment)?.load(drawableId)
      if (isGif) {
        builder?.diskCacheStrategy(DiskCacheStrategy.NONE)
      }
      builder?.into(imageView)
    } else {
      Glide.with(imageView).load(drawableId)
        .diskCacheStrategy(DiskCacheStrategy.NONE)
        .into(imageView)
    }
  }

  fun load(imageView: ImageView?, uri: Uri) {
    if (imageView == null) {
      return
    }
    // the DecorContext
    val context = imageView.context
    if (context is Activity) {
      with(context)?.load(uri)
        ?.into(imageView)
    } else if (context is Fragment) {
      with(context as Fragment)?.load(uri)
        ?.into(imageView)
    } else {
      Glide.with(imageView).load(uri)
        .diskCacheStrategy(DiskCacheStrategy.NONE)
        .into(imageView)
    }
  }

  /**
   * load the image not use decor context
   */
  fun load(context: Context, imageView: ImageView?, drawableId: Int) {
    if (imageView == null) {
      return
    }
    when (context) {
      is Activity -> {
        with(context)?.load(drawableId)
          ?.into(imageView)
      }
      is Fragment -> {
        with(context as Fragment)?.load(drawableId)
          ?.into(imageView)
      }
      else -> {
        Glide.with(imageView).load(drawableId)
          .diskCacheStrategy(DiskCacheStrategy.NONE)
          .into(imageView)
      }
    }
  }

  /**
   * for restaurant group background to load
   */
  fun loadTarget(imageView: ImageView?, url: String?) {
    if (imageView == null) {
      return
    }

    // used to load image into custom views that contain some ImageView
    val target = BackgroundTarget(imageView)

    Glide.with(imageView)
      .asBitmap()
      .load(url)
      .into(target)
  }

  /**
   * for menu horizontal gallery background to load
   */
  fun loadTargetHorizontal(imageView: ImageView?, url: String?) {
    if (imageView == null) {
      return
    }

    // used to load image into custom views that contain some ImageView
    val target = BackgroundHorizontalTarget(imageView)

    try {
      Glide.with(imageView)
        .asBitmap()
        .load(url)
        .into(target)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  /**
   * for menu horizontal gallery background to load
   */
  fun loadTargetHorizontal(imageView: ImageView?, url: String?, circle: Int) {
    if (imageView == null) {
      return
    }

    // used to load image into custom views that contain some ImageView
    val target = BackgroundHorizontalTarget(imageView)

    val builder = Glide.with(imageView)
      .asBitmap()
    if (circle > 0) {
      builder.transform(RoundedCorners(circle))
    }

    builder.load(url)
      .into(target)
  }

  /**
   * load the network image with circle
   */
  fun loadCircle(imageView: ImageView?, url: String?, circle: Int = 0) {
    if (imageView == null || ActivityUtils.isActivityDestroy(imageView.context)) {
      return
    }

    val builder = Glide.with(imageView).load(url)

    if (circle > 0) {
      builder.transform(RoundedCorners(circle))
    }

    // case: group order refresh gallery
    builder.skipMemoryCache(false)
    builder.dontAnimate()

    builder.into(imageView)
  }

  /**
   * for menu horizontal gallery background to load
   */
  fun loadTargetHorizontal(imageView: ImageView?, drawableId: Int) {
    if (imageView == null) {
      return
    }

    // used to load image into custom views that contain some ImageView
    val target = BackgroundHorizontalTarget(imageView)

    Glide.with(imageView)
      .asBitmap()
      .load(drawableId)
      .into(target)
  }

  /**
   * for menu gallery background to load
   */
  fun loadTarget(imageView: ImageView?, drawableId: Int) {
    if (imageView == null) {
      return
    }

    // add default request listener
//        val listener: RequestListener<Any> = RatioRequestListener(imageView)
//
//        Glide.with(imageView)
//            .addDefaultRequestListener(listener)
//            .load(drawableId)
//            .into(imageView)

    // used to load image into custom views that contain some ImageView
    val target = BackgroundTarget(imageView)

    Glide.with(imageView)
      .asBitmap()
      .load(drawableId)
      .into(target)

//        Glide.with(imageView)
//            .load(drawableId)
//            .centerInside()
//            .into(imageView)
  }

  /**
   * return the request manager with view
   */
  fun with(view: View?): RequestManager? {
    return if (view == null) null else with(view.context)
  }

  /**
   * return the request manager if activity destroy is null
   */
  fun with(context: Context?): RequestManager? {
    return if (context != null && context is Activity && !isDestroy(context)) {
      Glide.with(context)
    } else {
      null
    }
  }

  /**
   * return the request manager if fragment detached is null
   */
  fun with(fragment: Fragment?): RequestManager? {
    return if (fragment != null && !isDetached(fragment)) {
      Glide.with(fragment)
    } else {
      null
    }
  }

  /**
   * clear the glide used memory
   */
  fun clearMemory(context: Context) {
    Glide.get(context).clearMemory()
  }

  /**
   * return the activity is weather destroy
   */
  private fun isDestroy(activity: Activity?): Boolean {
    return (
      activity == null || activity.isFinishing ||
        activity.isDestroyed
      )
  }

  /**
   * return the fragment is weather is detached
   */
  private fun isDetached(fragment: Fragment?): Boolean {
    return (fragment == null || fragment.isDetached)
  }
}
