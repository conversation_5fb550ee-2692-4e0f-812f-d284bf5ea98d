<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.CAMERA" /> <!-- <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" -->
    <!-- android:maxSdkVersion="28" /> -->
    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" -->
    <!-- android:maxSdkVersion="28" /> -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- todo -->
    <permission
        android:name="com.ricepo.app.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.ricepo.app.permission.C2D_MESSAGE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <application
        android:name="com.ricepo.app.RicepoApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="${app_name_rice}"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="false"
        android:theme="@style/AppTheme"
        tools:replace="android:allowBackup,android:supportsRtl,android:label">
        <activity
            android:name="com.ricepo.app.compose.ComposeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"
            android:exported="false" />
        <activity android:name="com.ricepo.app.features.menu.combo.MenuComboActivity" />
        <activity
            android:name="com.ricepo.app.SplashActivity"
            android:exported="true"
            android:theme="@style/AppTheme.Launcher">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.ricepo.app.MainActivity"
            android:configChanges="uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme.ActivityFadeIn">
            <intent-filter>
                <category android:name="android.intent.category.MONKEY" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- firebase link -->
                <data
                    android:host="ricerocks.page.link"
                    android:scheme="http" />
                <data
                    android:host="ricerocks.page.link"
                    android:scheme="https" />
                <!-- branch io -->
                <data
                    android:host="riceeuj.app.link"
                    android:scheme="https" />
                <data
                    android:host="riceeuj-alternate.app.link"
                    android:scheme="https" />
                <data
                    android:host="riceeuj.test-app.link"
                    android:scheme="https" />
                <data
                    android:host="riceeuj-alternate.test-app.link"
                    android:scheme="https" />

            </intent-filter>

            <!-- branch uri schema -->
            <intent-filter>
                <data
                    android:host="open"
                    android:scheme="rohan" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.ricepo.app.features.login.LoginActivity"
            android:theme="@style/AppTheme.ActivityBottomIn"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity android:name="com.ricepo.app.features.profile.ProfileActivity" />
        <activity
            android:name="com.ricepo.app.features.address.AddressActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.menu.MenuActivity"
            android:configChanges="uiMode">

            <!-- <intent-filter android:autoVerify="true"> -->
            <!-- <action android:name="android.intent.action.VIEW"/> -->
            <!-- <category android:name="android.intent.category.DEFAULT"/> -->
            <!-- <category android:name="android.intent.category.BROWSABLE"/> -->
            <!-- <data android:host="ricepo.page/link" android:scheme="http"/> -->
            <!-- <data android:host="ricepo.page/link" android:scheme="https"/> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name="com.ricepo.app.features.menu.options.OptionsActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.menu.feedback.MenuFeedbackActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity android:name="com.ricepo.app.features.settings.SettingsActivity" />
        <activity
            android:name="com.ricepo.app.features.checkout.CheckoutActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.checkout.comment.CommentActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.checkout.tips.TipsActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.checkout.tips.TipsEnterActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.coupon.CouponActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.payment.PaymentActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.payment.PaymentCardActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop">

            <!-- <intent-filter> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->
            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <data android:scheme="com.ricepo.app" /> -->
            <!-- </intent-filter> -->
        </activity> <!-- <activity android:name=".wxapi.WXEntryActivity" -->
        <!-- android:label="@string/app_name" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="${applicationId}" -->
        <!-- android:launchMode="singleTask" /> -->
        <activity android:name="com.ricepo.app.features.order.OrderActivity" />
        <activity
            android:name="com.ricepo.app.features.subscription.SubscriptionActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.subscription.SubscriptionUpdateActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.rating.RatingActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.support.OrderSupportActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.support.OrderSupportIssueActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.support.chat.ChatActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity android:name="com.ricepo.app.restaurant.search.RestaurantSearchActivity" />
        <activity
            android:name="com.ricepo.app.restaurant.search.RegionExploreActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity android:name="com.ricepo.app.features.bannerweb.BannerWebActivity" />
        <activity android:name="com.ricepo.app.features.bannerweb.HtmlWebActivity" />
        <activity android:name="com.ricepo.app.features.recommend.RecommendActivity" />
        <activity
            android:name="com.ricepo.app.features.settings.LanguageActivity"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme.ActivityFadeIn" />
        <activity android:name="com.ricepo.app.features.reward.RewardSummaryActivity" />
        <activity android:name="com.ricepo.app.restaurant.submore.RestaurantSubMoreActivity" />
        <activity android:name="com.ricepo.app.features.settings.FeedbackActivity" />
        <activity
            android:name="com.ricepo.app.features.luckymenu.LuckyRecommendActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.luckymenu.LuckyMenuActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.view.GlobalDialogFacade$GlobalDialogActivity"
            android:theme="@style/TransparentActivity" />
        <activity
            android:name="com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity"
            android:theme="@style/AppTheme.ImagePreview" />
        <activity
            android:name="com.ricepo.app.features.menu.preview.MenuMarketPreviewActivity"
            android:theme="@style/AppTheme.ImagePreview" />
        <activity
            android:name="com.ricepo.app.features.preview.ImagePreviewActivity"
            android:theme="@style/AppTheme.ImagePreview" />
        <activity android:name="com.ricepo.app.features.menu.submore.MenuSubMoreActivity" />
        <activity android:name="com.ricepo.app.features.menu.search.MenuSearchActivity" />
        <activity
            android:name="com.ricepo.app.features.refer.ReferActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity
            android:name="com.ricepo.app.features.refer.diary.DiaryImageActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />
        <activity android:name="com.ricepo.app.features.refer.diary.ReferWebActivity" />
        <activity android:name="com.ricepo.app.features.points.PointsSummaryActivity" />
        <activity
            android:name="com.ricepo.app.features.checkout.points.PointsActivity"
            android:theme="@style/AppTheme.ActivityBottomIn" />

        <receiver
            android:name="com.ricepo.tripartite.wechat.WeChatRegister"
            android:exported="true"
            android:permission="com.tencent.mm.plugin.permission.SEND">
            <intent-filter>
                <action android:name="com.tencent.mm.plugin.openapi.Intent.ACTION_REFRESH_WXAPP" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="com.ricepo.app.startup.RouterInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.MonitorInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.FirebaseInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.LifecycleInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.MapInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.StripeInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.TimeZoneInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.LeakInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
            <meta-data
                android:name="com.ricepo.app.startup.OneSingalInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service android:name="com.ricepo.app.features.menu.base.MenuService" /> <!-- google firebase messaging service -->
        <service
            android:name="com.ricepo.app.message.MessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_notification" />
        <!--
        Missing Default Notification Channel metadata in AndroidManifest.
        Default value will be used.
        -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" /> <!-- false prevent firebase auto initialization -->
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="true" />
<!--        <meta-data-->
<!--            android:name="io.sentry.dsn"-->
<!--            android:value="https://<EMAIL>/6202113" />-->
<!--        <meta-data-->
<!--            android:name="io.sentry.auto-init"-->
<!--            android:value="false" />-->
<!--        <meta-data-->
<!--            android:name="io.sentry.debug"-->
<!--            android:value="true" />-->
<!--        <meta-data-->
<!--            android:name="io.sentry.anr.enable"-->
<!--            android:value="false" />-->
<!--        <meta-data-->
<!--            android:name="io.sentry.anr.timeout-interval-mills"-->
<!--            android:value="10000" /> -->
        <!-- Enables the Google Payment API -->
        <meta-data
            android:name="com.google.android.gms.wallet.api.enabled"
            android:value="true" /> <!-- onesignal -->
        <meta-data
            android:name="com.onesignal.NotificationServiceExtension"
            android:value="com.ricepo.app.message.onesignal.NotificationServiceExtension" />
        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="false" /> <!-- Set to true to use Branch_Test_Key -->
        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_live_ml1ra8k5nxmrAFx8JK6SJgobCEg4f7EQ" />
        <meta-data
            android:name="io.branch.sdk.BranchKey.test"
            android:value="key_test_jc5we3hXdFpCrDtZT8mUxmagBvo1b9s8" />
    </application>

</manifest>