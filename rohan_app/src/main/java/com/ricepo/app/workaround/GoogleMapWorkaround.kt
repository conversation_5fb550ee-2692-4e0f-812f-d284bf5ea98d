package com.ricepo.app.workaround

import android.app.Application
import android.content.Context
import java.io.File

/**
 * https://issuetracker.google.com/issues/154855417?pli=1
 */

object GoogleMapWorkaround {

  fun workaround(
    application: Application
  ) {
    with(application) {
      try {
        val sharedPreferences = getSharedPreferences("google_bug_154855417", Context.MODE_PRIVATE)
        if (!sharedPreferences.contains("fixed")) {
          val corruptedZoomTables = File(filesDir, "ZoomTables.data")
          val corruptedSavedClientParameters = File(filesDir, "SavedClientParameters.data.cs")
          val corruptedClientParametersData = File(filesDir, "DATA_ServerControlledParametersManager.data.$packageName")
          val corruptedClientParametersDataV1 = File(filesDir, "DATA_ServerControlledParametersManager.data.v1.$packageName")
          corruptedZoomTables.delete()
          corruptedSavedClientParameters.delete()
          corruptedClientParametersData.delete()
          corruptedClientParametersDataV1.delete()
          sharedPreferences.edit().putBoolean("fixed", true).apply()
        }
      } catch (_: Exception) { }
    }
  }
}
