package com.ricepo.app

import android.app.Activity
import android.os.Bundle
import android.os.Handler
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.ActivitySplashBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.login.repository.AuthUseCase
import com.ricepo.app.model.UserInformation
import com.ricepo.base.BaseActivity
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.model.Customer
import com.ricepo.style.LocaleUtil
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 26/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
class SplashActivity : AppCompatActivity() {

  @Inject
  lateinit var useCase: AuthUseCase

  lateinit var binding: ActivitySplashBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivitySplashBinding.inflate(layoutInflater)
    setContentView(binding.root)

    // remove update flag in order to check update
    CommonPref.removeCheckUpdate(this)
    CommonPref.removeDriverRating(this)
    CommonPref.removeCheckPromo(this)
  }

  override fun onStart() {
    super.onStart()
    renewToken()
  }

  override fun onDestroy() {
    super.onDestroy()
  }

  override fun onWindowFocusChanged(hasFocus: Boolean) {
    super.onWindowFocusChanged(hasFocus)
    if (hasFocus) {
      hideSystemUIAndNavigation(this)
    }
  }

  private fun hideSystemUIAndNavigation(activity: Activity) {
    val decorView: View = activity.window.decorView
    decorView.systemUiVisibility =
      (
        View.SYSTEM_UI_FLAG_IMMERSIVE
          // Set the content to appear under the system bars so that the
          // content doesn't resize when the system bars hide and show.
          or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
          or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN // Hide the nav bar and status bar
          or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
  }

  private fun renewToken() {
    useCase.renewToken(
      this,
      object : DisposableSingleObserver<UserInformation>() {
        override fun onSuccess(t: UserInformation) {
          lifecycleScope.launch {
            if (t != null) {
              CustomerCache.saveToken(t.token)
              getCustomer(t.id)
            } else {
              Handler().postDelayed(
                {
                  startNavigation()
                },
                1000
              )
            }
          }
        }

        override fun onError(e: Throwable) {
          Handler().postDelayed(
            {
              startNavigation()
            },
            1000
          )
        }
      }
    )
  }

  fun getCustomer(customerId: String) {
    useCase.getCustomer(
      object : DisposableSingleObserver<Customer>() {
        override fun onSuccess(t: Customer) {
          startNavigation()
        }

        override fun onError(e: Throwable) {
          startNavigation()
        }
      },
      customerId
    )
  }

  /**
   * navigate to home or language page
   */
  fun startNavigation() {
    useCase.dispose()
    navigateToMainPage()
  }

  private fun navigateToMainPage() {
    if (LocaleUtil.getLanguage() == null) {
      // navigate to language page if not select language
      FeaturePageRouter.navigateLanguage(true)
    } else {
      navigateToHome()
    }
    finish()
  }

  /**
   * navigate to home with transition
   */
  private fun navigateToHome() {
    FeaturePageRouter.navigateHome(true)
  }
}
