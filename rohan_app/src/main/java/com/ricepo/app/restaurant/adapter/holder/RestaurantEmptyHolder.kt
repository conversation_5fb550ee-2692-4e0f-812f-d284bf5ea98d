package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.RestaurantItemEmptyBinding

//
// Created by <PERSON><PERSON> on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantEmptyHolder(private val binding: RestaurantItemEmptyBinding) :
  RecyclerView.ViewHolder(binding.root) {

  init {
  }

  fun bind(height: Int) {
    val params = binding.tvEmptyPlace.layoutParams
    if (params is LinearLayout.LayoutParams) {
      params.height = height
      binding.tvEmptyPlace.layoutParams = params
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantEmptyHolder {
      val binding = RestaurantItemEmptyBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      return RestaurantEmptyHolder(binding)
    }
  }
}
