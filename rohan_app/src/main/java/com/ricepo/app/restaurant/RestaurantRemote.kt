package com.ricepo.app.restaurant

import com.ricepo.app.model.Card
import com.ricepo.app.model.ChatModel
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.ImageFeedbackReq
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderReq
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.QuoteRequest
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.SupportReq
import com.ricepo.app.model.ValidateCouponReq
import com.ricepo.app.model.ValidateCouponRes
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.DriverTrace
import com.ricepo.base.model.Menu
import com.ricepo.base.model.MenuBean
import com.ricepo.base.model.RegionModel
import com.ricepo.base.model.Restaurant
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.core.Single

interface RestaurantRemote {
  // restaurant

  fun getRestaurants(loc: String?): Single<List<Restaurant>>

  fun getRegions(regionId: String): Single<RegionModel>

  fun getRestaurantById(restaurantId: String, loc: String?): Single<Restaurant>

  // menu

  fun getMenu(restaurantId: String): Single<Menu>

  /**
   * menu v2 api
   */
  fun getMenu(
    restaurantId: String,
    loc: String,
    search: String?,
    bundles: List<String>?,
    searches: List<String>?,
    filter: String?
  ): Single<MenuBean>

//    fun createGroupOrder(req: CreateOrderGroupReq): Single<OrderGroup>
//
//    fun updateGroupOrder(groupId: String, req: UpdateOrderGroupReq): Single<OrderGroup>
//
//    fun deleteGroupOrder(groupId: String, deviceId: String): Single<Response>
//
//    fun getGroupOrder(groupId: String, deviceId: String): Single<OrderGroup>

  fun imageFeedback(req: ImageFeedbackReq): Single<NetworkError>

  /**
   * get nearby orders
   */
  fun getQuote(restaurantId: String, body: QuoteRequest): Single<QuoteResponse>

  // coupon

  fun getCouponByCustomerId(customerId: String): Single<List<Coupon>>

  fun getCouponByRestId(restaurantId: String): Single<List<Coupon>>

  fun getCouponByRegionId(regionId: String): Single<List<Coupon>>

  fun validateCoupon(couponId: String, body: ValidateCouponReq): Single<ValidateCouponRes>

  fun getRecommendCoupons(body: ValidateCouponReq): Single<List<Coupon>>

  // payment

  fun getCards(customerId: String?, type: Array<String>): Single<List<Card>>

  fun deleteCard(customerId: String, cardId: String): Single<retrofit2.Response<Unit>>

  fun deleteBbvaCard(customerId: String, cardId: String): Single<retrofit2.Response<Unit>>

  fun createIntentPayment(orderId: String, paymentMethodId: String): Single<PaymentObj>

  fun createWechatPayment(orderId: String): Single<PaymentObj>

  fun createAlipayPayment(orderId: String): Single<PaymentObj>

  fun createUnionPayment(orderId: String): Single<PaymentObj>

  fun createPaypalPayment(orderId: String): Single<PaymentObj>

  fun createBBVAPayment(orderId: String, paymentId: String?): Single<PaymentObj>

  // order

  fun createOrder(restaurantId: String, body: OrderReq): Single<OrderResponse>

  fun getOrderById(orderId: String): Single<Order>

  /**
   * get the point of delivery driver
   */
  fun getPoint(orderId: String): Single<DriverPoint>

  /**
   * get the trace of delivery driver
   */
  fun getDriverTrace(orderId: String): Single<DriverTrace>

  /**
   * return recent or history orders
   */
  fun getOrders(
    customerId: String,
    type: CustomerOrderType,
    isBefore: String?
  ): Single<List<Order>>

  // subscription

  fun createSubscription(planId: String, paymentMethodId: String): Single<PaymentObj>

  fun cancelSubscription(subscriptionId: String, cancel: Boolean = true): Single<Void>

  fun updateSubscriptionPayment(subscriptionId: String, paymentMethodId: String): Single<Void>

  // order support

  fun createTicket(orderId: String, body: SupportReq): Single<Any>

  fun getSupportChatToken(): Single<ChatModel>

  fun uploadImage(imageData: ByteArray, imageType: String = "support-images"): Single<String>
}

enum class CustomerOrderType {
  recent,
  history
}
