package com.ricepo.app.restaurant.adapter.stick

import android.graphics.Canvas
import android.view.View
import androidx.core.view.forEachIndexed
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding

class StickyHeaderDecoration(
  private val listener: StickyListener,
  private val binding: () -> ViewBinding,
  private val shouldDraw: () -> Boolean
) : RecyclerView.ItemDecoration() {

  // lazily initialize the binding instance for the header view
  private val headerBinding by lazy {
    binding.invoke()
  }

  private val headerView by lazy {
    headerBinding.root
  }

  private var currentHeaderPos = -1
  private var isFirstBind = true
  override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
    super.onDrawOver(c, parent, state)
    val topChild = parent.getChildAt(0) ?: return
    val topChildPosition = parent.getChildAdapterPosition(topChild)
    if (
      topChildPosition == RecyclerView.NO_POSITION ||
      topChildPosition < listener.firstStickHeaderPosition() ||
      !shouldDraw()
    ) {
      headerView.isVisible = false
      return
    }
    val headerPos = listener.getHeaderPositionForItem(topChildPosition)
    headerBinding.apply {
      // only binding data when current header changes
      if (headerPos != currentHeaderPos || isFirstBind) {
        currentHeaderPos = headerPos
        headerView.isVisible = true
        listener.bindHeaderData(this, headerPos)
      }
    }

    if (!isFirstBind) {
      getChildInContact(
        parent,
        headerPos
      )?.let {
        moveHeader(c, it)
        return
      }
    }

    drawHeader(c)
    isFirstBind = false
  }

  private fun drawHeader(c: Canvas) {
    headerView.isVisible = true
    c.save()
    c.translate(0f, 0f)
    headerView.draw(c)
    c.restore()
  }

  private fun moveHeader(c: Canvas, nextHeader: View) {
    headerView.isVisible = false
    c.save()
    c.translate(0f, (nextHeader.top - headerView.height).toFloat())
    headerView.draw(c)
    c.restore()
  }

  private fun getChildInContact(
    parent: RecyclerView,
    currentHeaderPos: Int
  ): View? {
    var childInContact: View? = null
    parent.forEachIndexed { index, child ->
      if (
        currentHeaderPos != index &&
        listener.isStickHeader(parent.getChildAdapterPosition(child))
      ) {
        if (
          headerView.bottom in child.top..child.bottom
        ) {
          childInContact = child
          return@forEachIndexed
        }
      }
    }
    return childInContact
  }
}
