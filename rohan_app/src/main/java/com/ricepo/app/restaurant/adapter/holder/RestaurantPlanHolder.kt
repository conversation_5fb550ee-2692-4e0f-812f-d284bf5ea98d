package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.RestaurantItemPlanBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantPlanHolder(private val binding: RestaurantItemPlanBinding) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    binding.root.clickWithTrigger {
      // subscription plan
      CustomerCache.liveCustomer { customer ->
        if (!customer?.id.isNullOrEmpty()) {
          val subscription = it.tag ?: return@liveCustomer
          subscription as SubscriptionPlan
          FeaturePageRouter.navigateSubscription(it.context, subscription, true)
        } else {
          FeaturePageRouter.navigateLogin()
        }
      }
    }
  }

  fun bind(uiModel: RestaurantUiModel.RestaurantPlan) {
    val plan = uiModel.plan

    binding.root.tag = plan

    binding.tvMembershipTitle.text = plan.title?.localize()
    binding.tvMembershipSubtitle.text = plan.subtitle?.localize()
    binding.tvMembershipApply.text = plan.button?.localize()
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantPlanHolder {
      val binding = RestaurantItemPlanBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.restaurant_plan_height)
      )
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
//            params.topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_section_space)
      binding.root.layoutParams = params
      return RestaurantPlanHolder(binding)
    }
  }
}
