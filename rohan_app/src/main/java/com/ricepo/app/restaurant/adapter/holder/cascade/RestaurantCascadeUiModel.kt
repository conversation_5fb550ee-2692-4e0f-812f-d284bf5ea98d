package com.ricepo.app.restaurant.adapter.holder.cascade

import com.ricepo.base.model.RestaurantDishItem
import com.ricepo.base.model.RestaurantRemoteGroup

//
// Created by <PERSON><PERSON> on 2021/9/14.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

sealed class RestaurantCascadeUiModel {

  abstract val group: RestaurantRemoteGroup?

  data class CascadeTitle(
    override val group: RestaurantRemoteGroup?
  ) : RestaurantCascadeUiModel()

  data class CascadeItem(
    val dish: RestaurantDishItem,
    override val group: RestaurantRemoteGroup?,
    val groupIndex: Long?
  ) : RestaurantCascadeUiModel()
}
