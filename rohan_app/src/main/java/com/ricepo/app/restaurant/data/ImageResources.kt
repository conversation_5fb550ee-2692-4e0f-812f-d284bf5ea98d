package com.ricepo.app.restaurant.data

import com.ricepo.base.model.I18n

//
// Created by <PERSON><PERSON> on 22/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// todo this impl is so bad
class BusyImage : I18n {
  override val enUS: String?
    get() = "ic_busy_en"
  override val zhCN: String?
    get() = "ic_busy_cn"
  override val zhHK: String?
    get() = "ic_busy_cn"
  override val es: String?
    get() = "ic_busy_es"
}

// todo this impl is so bad
class OptionsModify : I18n {
  override val enUS: String?
    get() = "ic_modify_en"
  override val zhCN: String?
    get() = "ic_modify_cn"
  override val zhHK: String?
    get() = "ic_modify_cn"
  override val es: String?
    get() = "ic_modify_es"
}

// todo this impl is so bad
class HotImage : I18n {
  override val enUS: String?
    get() = "ic_hot_en"
  override val zhCN: String?
    get() = "ic_hot_sc"
  override val zhHK: String?
    get() = "ic_hot_tc"
  override val es: String?
    get() = "ic_hot_es"
}
