package com.ricepo.app.restaurant.adapter.holder.cascade

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantCascadeTitleBinding
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantCascadeTitleHolder(
  private val binding: RestaurantCascadeTitleBinding
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantCascadeUiModel.CascadeTitle, position: Int) {
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      var width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_135dp)
      val leftMargin = if (position < 2) {
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
      } else {
        0
      }
      width += leftMargin
      params.width = width
      params.leftMargin = leftMargin
      binding.root.layoutParams = params
    }

    // set the group title
    binding.tvTitleName.text = uiModel.group?.name?.localize()
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantCascadeTitleHolder {
      val binding = RestaurantCascadeTitleBinding.inflate(LayoutInflater.from(parent.context))
      // width wrap content to adoption value
      val width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_135dp)
      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_184dp)
      // the width is invalid and set the sub view with and margin
      val params = RecyclerView.LayoutParams(width, height)
      binding.root.layoutParams = params

      return RestaurantCascadeTitleHolder(binding)
    }
  }
}
