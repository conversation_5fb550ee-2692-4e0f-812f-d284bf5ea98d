package com.ricepo.app.restaurant.adapter.holder

import android.text.SpannableStringBuilder
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantHorizontalBigImageItemBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.localize
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.placeVisible

class RestaurantHorizontalBigImageItemViewHolder(
  val binding: RestaurantHorizontalBigImageItemBinding,
  private val clickItem: FunRestaurantHorizontalMenuClick?
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    binding.root.apply {
      clickWithTrigger {
        navigateMenuForCheck(it)
      }
    }
  }

  private fun navigateMenuForCheck(view: View, foodName: String? = null) {
    val data = view.tag
    if (data is Restaurant) {
      logRestaurantSelectedEvent(view, data, foodName)
      if (clickItem != null) {
        clickItem.invoke(
          RestaurantHorizontalUiModel
            .RestaurantItem(data, RestaurantRemoteGroup(), 0),
          foodName
        )
      } else {
        navigateMenu(data, view, foodName)
      }
    }
  }

  private fun navigateMenu(data: Restaurant, view: View, foodName: String?) {
    val searches = foodName?.let {
      arrayListOf(it)
    }
    FeaturePageRouter.navigateMenuForBusy(binding.root.context, data, searches)
  }

  private fun logRestaurantSelectedEvent(view: View, data: Restaurant, foodName: String?) {
    // firebase event select restaurant
    val event = view.getTag(com.ricepo.base.R.id.tag_firebase_event)
    if (event is FirebaseRestaurantEvent) {
      if (data.pool != null) {
        event.rPoolTime = SimpleDateUtils.toDateDiffSeconds(data.pool?.expiresAt)
      }
      foodName?.let {
        event.rOnFood = true
      }
    }

    view.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
    AnalyticsFacade.logEvent(view, FirebaseEventName.rSelectRestaurant)
  }

  fun bind(
    data: RestaurantHorizontalUiModel,
    position: Int,
    size: Int,
    isBackground: Boolean = true,
    isNotPickup: Boolean = true
  ) {
    val item = if (data is RestaurantHorizontalUiModel.RestaurantBigImageItem) {
      data.restaurant
    } else {
      return
    }

    // set margin right
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      // horizontal scroll aligned left margin
      var leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      var rightMargin = 0
      if (position == (size - 1)) {
        rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      } else if (position == 0) {
        leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      }
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params
    }

    // set the restaurant name margin top
    val restaurantNameParams = binding.inRestaurantInfo.layRestaurantName.layoutParams
    if (restaurantNameParams is LinearLayout.LayoutParams) {
      restaurantNameParams.topMargin = if (isNotPickup) {
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.dip_3)
      } else {
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_15dp)
      }
      binding.inRestaurantInfo.layRestaurantName.layoutParams = restaurantNameParams
    }
    val paddingLeft = if (isNotPickup) {
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_0dp)
    } else {
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_10dp)
    }
    binding.inRestaurantInfo.root.setPadding(paddingLeft, 0, 0, 0)

    ImageLoader.load(binding.bigImage, item.images?.landing)

    setTag(binding.root, item, data, position)

    val bindMapper = RestaurantBindMapper(item, binding.root.context)

    // restaurant name and max 1 line
    binding.inRestaurantInfo.tvRestaurantName.text = bindMapper.bindName(isNotPickup)
    binding.inRestaurantInfo.tvRestaurantName.maxLines = 1

    // todo deal last order later
    bindRestaurantInfo(bindMapper, item)

    // bind the closed status
    val pairClosed = bindMapper.bindClosed()
    bindRestaurantClosed(pairClosed)

    // set the default background
    if (isBackground) {
      binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
    } else {
      binding.root.background = null
    }
  }

  private fun setTag(
    view: View,
    item: Restaurant,
    data: RestaurantHorizontalUiModel.RestaurantBigImageItem,
    position: Int
  ) {
    view.tag = item
    view.setTag(
      com.ricepo.base.R.id.tag_firebase_event,
      FirebaseRestaurantEvent(
        rGroupId = data.group.groupId,
        rGroupType = data.group.type,
        rGroupIndex = data.groupIndex,
        rRestaurantIndex = position.toLong(),
      )
    )
  }

  private fun bindRestaurantInfo(bindMapper: RestaurantBindMapper, item: Restaurant) {
    // restaurant info and promotion
    val pairInfo = bindMapper.bindInfo(isShowDelivery = false, needShowTag = false)
    binding.inRestaurantInfo.tvRestaurantInfo.text = pairInfo.first
    binding.inRestaurantInfo.tvRestaurantInfo.setTextColor(pairInfo.second)

    binding.inRestaurantInfo.tvRestaurantTags.text = bindMapper.bindTags()

//    val promotion = bindMapper.bindCombinePromotion()
//
//    // auto move up one line
//    if (binding.inRestaurantInfo.tvRestaurantInfo.text.isNullOrEmpty()) {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantInfo, promotion)
//    } else {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantSubInfo, promotion)
//    }

    // show the pool layout
    binding.rtvPool.isVisible = (item.pool != null)

//    bindMapper.bindRating(binding.inRestaurantInfo.tvRating)

    with(binding.tvFeatureMotd) {
      val bindMotd = bindMapper.bindMotd(
        featureTint = com.ricepo.style.R.color.rating_color,
        motdTint = com.ricepo.style.R.color.w
      )
      placeVisible(bindMotd.isNotBlank())
      text = bindMotd
      isSelected = true
    }

    bindPool(item.pool)
  }

  private fun bindRestaurantSubInfo(
    tvView: TextView,
    promotion: SpannableStringBuilder?
  ) {
    tvView.text = promotion
    tvView.setTextColor(
      ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, binding.root.context)
    )
  }

  private fun bindRestaurantClosed(pairClosed: Pair<SpannableStringBuilder?, Int>) {
    // fixed the restaurant name layout
    if (pairClosed.first.isNullOrEmpty()) {
      binding.inRestaurantClosed.tvRestaurantClosed.visibility = View.INVISIBLE
    } else {
      binding.inRestaurantClosed.tvRestaurantClosed.visibility = View.VISIBLE
    }

    pairClosed.first?.let {
      binding.inRestaurantClosed.tvRestaurantClosed.text = it
      binding.inRestaurantClosed.tvRestaurantClosed.setTextColor(pairClosed.second)
    }
  }

  private fun bindPool(pool: RestaurantPool?) {
    if (pool == null) {
      return
    }
    val params = binding.rtvPool.layoutParams
    if (params is ViewGroup.LayoutParams) {
      params.height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.dip_40)
      binding.rtvPool.layoutParams = params
    }
    binding.rtvPool.setMessage(pool.message?.localize())
    binding.rtvPool.setExpireAt(pool.expiresAt)
  }
}
