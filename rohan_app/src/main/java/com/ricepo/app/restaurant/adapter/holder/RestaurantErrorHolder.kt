package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.databinding.LayoutErrorContainerBinding
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantErrorHolder(
  private val binding: LayoutErrorContainerBinding,
  private val refreshView: (uiModel: RestaurantUiModel) -> Unit = {},
  private val retry: (uiModel: RestaurantUiModel) -> Unit = {}
) :
  RecyclerView.ViewHolder(binding.root) {

  init {

    binding.btnErrorOperator.clickWithTrigger {
      val model = it.tag
      if (model is RestaurantUiModel.RestaurantErrorSection) {
        retry(model)
      }
    }
  }

  fun bind(model: RestaurantUiModel.RestaurantErrorSection) {

    binding.tvErrorMessage.text = model.errorMessage
    binding.btnErrorOperator.text = ResourcesUtil.getString(com.ricepo.style.R.string.retry)
    binding.btnErrorOperator.tag = model

    refreshView(model)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      refreshView: (uiModel: RestaurantUiModel) -> Unit,
      retry: (uiModel: RestaurantUiModel) -> Unit
    ): RestaurantErrorHolder {
      val binding = LayoutErrorContainerBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val height = (DisplayUtil.getContentHeight() * 0.5f).toInt()
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        height
      )
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_150dp)
      binding.root.layoutParams = params
      return RestaurantErrorHolder(binding, refreshView, retry)
    }
  }
}
