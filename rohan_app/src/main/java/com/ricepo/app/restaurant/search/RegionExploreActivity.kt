package com.ricepo.app.restaurant.search

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityRegionExploreBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.map.model.FormatUserAddress
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 15/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_REGION_EXPLORE)
class RegionExploreActivity : BaseActivity() {

  private lateinit var binding: ActivityRegionExploreBinding

  val viewModel: RestaurantSearchViewModel by viewModels()

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityRegionExploreBinding.inflate(layoutInflater)
    setContentView(binding.root)

    bindExploreAddress()
  }

  private fun bindExploreAddress() {
    lifecycleScope.launchWhenCreated {
      viewModel.getExploreAddress()
        .collect {
          showExploreView(it)
        }
    }
  }

  private fun showExploreView(datas: List<FormatUserAddress>) {
    val adapter = BindListAdapter(
      datas, com.ricepo.base.R.layout.view_string_divider,
      object : OnBindViewListener<View, FormatUserAddress> {
        override fun onBindView(view: View, value: FormatUserAddress?, position: Int) {
          val tv = view.findViewById<TextView>(com.ricepo.base.R.id.tv_item_string)
          tv?.apply {
            text = value?.name
          }

          view.clickWithTrigger {
            viewModel.saveExploreAddress(value) {
              lifecycleScope.launch(Dispatchers.Main) {
                onBackPressed()
              }
            }
          }
        }
      }
    )
    binding.recyclerExploreList.isNestedScrollingEnabled = false
    binding.recyclerExploreList.adapter = adapter
  }
}
