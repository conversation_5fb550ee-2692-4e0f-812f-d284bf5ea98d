package com.ricepo.app.restaurant.adapter

import android.util.ArrayMap
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantBannerBinding
import com.ricepo.app.databinding.RestaurantItemGroupBinding
import com.ricepo.app.features.menu.bundle.RestaurantBigImageBundleHolder
import com.ricepo.app.features.menu.bundle.RestaurantBundleHolder
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.holder.FunRestaurantMenuClick
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalHolder
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantBannerHolder
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantGroupHolder
import com.ricepo.app.restaurant.search.adapter.RestaurantSearchPromptHolder
import com.ricepo.base.adapter.EmptyViewHolder

//
// Created by <PERSON><PERSON> on 21/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantAdapter(
  private var dataList: List<RestaurantUiModel>?,
  private val lifecycleOwner: LifecycleOwner,
  private val itemClick: FunRestaurantMenuClick = { _, _ -> }
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  /**
   * the count of list
   */
  private fun itemCount(): Int {
    return dataList?.size ?: 0
  }

  /**
   * set the datas and notify changed
   */
  fun setData(data: List<RestaurantUiModel>?) {
    dataList = data
    notifyDataSetChanged()
  }

  /**
   * the item object by position
   */
  private fun getItem(position: Int): RestaurantUiModel? {
    return dataList?.getOrNull(position)
  }

  override fun getItemCount() = itemCount()

  override fun getItemViewType(position: Int): Int {
    if (position >= itemCount()) return 0
    val mode = getItem(position)?.toBigImageBundleOrThis()
    return when (mode) {
      is RestaurantUiModel.RestaurantVertical -> R.layout.restaurant_item_vertical_motion
      is RestaurantUiModel.RestaurantCategory -> R.layout.restaurant_item_group
      is RestaurantUiModel.RestaurantBanner -> R.layout.restaurant_item_banner
      is RestaurantUiModel.RestaurantBundle -> R.layout.restaurant_item_bundle
      is RestaurantUiModel.RestaurantSearchPrompt -> R.layout.restaurant_item_search_prompt
      is RestaurantUiModel.RestaurantBigImageBundle -> R.layout.restaurant_big_image_item_vertical
      else -> 0
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.restaurant_item_vertical_motion -> RestaurantVerticalHolder.create(parent, true, itemClick)
      R.layout.restaurant_item_banner -> {
        val binding = RestaurantBannerBinding.inflate(LayoutInflater.from(parent.context))
        val params = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.MATCH_PARENT,
          RecyclerView.LayoutParams.WRAP_CONTENT
        )
        binding.root.layoutParams = params
        RestaurantBannerHolder(binding, lifecycleOwner)
      }
      R.layout.restaurant_item_group -> {
        val binding = RestaurantItemGroupBinding.inflate(LayoutInflater.from(parent.context))
        val params = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.MATCH_PARENT,
          RecyclerView.LayoutParams.WRAP_CONTENT
        )
//                params.topMargin = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.restaurant_group_margin)
        binding.root.layoutParams = params
        RestaurantGroupHolder(binding)
      }
      R.layout.restaurant_item_bundle -> {
        RestaurantBundleHolder.create(parent, bundleCacheSelected, lifecycleOwner, itemClick)
      }
      R.layout.restaurant_big_image_item_vertical -> {
        RestaurantBigImageBundleHolder.create(parent, bundleCacheSelected, lifecycleOwner, itemClick)
      }
      R.layout.restaurant_item_search_prompt -> {
        RestaurantSearchPromptHolder.create(parent)
      }
      else -> EmptyViewHolder.create(parent)
    }
  }

  fun setBundles(bundles: List<String>?) {
    bundleCacheSelected.clear()
    bundles?.forEach {
      bundleCacheSelected[it] = true
    }
  }

  private val bundleCacheSelected = ArrayMap<String, Boolean>()

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = getItem(position)?.toBigImageBundleOrThis()
    uiModel?.let {
      when (uiModel) {
        is RestaurantUiModel.RestaurantVertical -> (holder as RestaurantVerticalHolder)
          .bind(uiModel)
        is RestaurantUiModel.RestaurantBanner -> (holder as RestaurantBannerHolder).bindBanner(uiModel)
        is RestaurantUiModel.RestaurantCategory ->
          (holder as RestaurantGroupHolder).bind(uiModel)
        is RestaurantUiModel.RestaurantBundle -> (holder as RestaurantBundleHolder).bind(
          uiModel, position
        )
        is RestaurantUiModel.RestaurantBigImageBundle -> (holder as RestaurantBigImageBundleHolder).bind(
          uiModel, position
        )
        is RestaurantUiModel.RestaurantSearchPrompt -> (holder as RestaurantSearchPromptHolder).bind(uiModel.label)
        else -> {}
      }
    }
  }
}
