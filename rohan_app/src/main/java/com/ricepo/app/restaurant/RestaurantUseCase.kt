package com.ricepo.app.restaurant

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.datasource.RestaurantDataSource
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.base.model.Banner
import com.ricepo.base.model.Cuisine
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.LuckRecommendBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantDishItem
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.base.model.RestaurantRecommendData
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.RestaurantRemoteSection
import com.ricepo.base.model.RestaurantSort
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.executor.SingleUseCase
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.disposables.Disposable
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class RestaurantUseCase @Inject constructor(
  private val dataSource: CombineRestApi,
  private val mapper: RestaurantMapper,
  posExecutionThread: PostExecutionThread
) :
  SingleUseCase<List<Any>, RestaurantUseCase.Params>(posExecutionThread) {

  data class Params(val loc: String?)

  /**
   * dispose the restaurant async request
   */
  private var disposeRestaurant: Disposable? = null

  /**
   * dispose the region async request
   */
  private var disposeRegion: Disposable? = null

  fun getNearRestaurants(
    request: RestaurantRequest,
    lastModels: MutableList<RestaurantUiModel> = mutableListOf(),
    groupModel: RestaurantUiModel? = null
  ):
    Flow<PagingData<RestaurantUiModel>> {
    val restaurantDataSource = RestaurantDataSource(
      dataSource, mapper,
      request, lastModels, groupModel
    )

    return Pager(
      config = PagingConfig(
        10,
        enablePlaceholders = false,
        //  Placeholders and prefetch are the only ways to trigger loading of more data in
        //  PagingData, so either placeholders must be enabled,
        //  or prefetch distance must be > 0
        prefetchDistance = 1,
        initialLoadSize = 10
      ),
      pagingSourceFactory = { restaurantDataSource }
    ).flow
  }

  suspend fun getPickupRestaurants(loc: String?, search: String? = null): RestaurantRemoteSection? {
//        val distance = RestaurantPickupFragment.DEFAULT_MILES
    val params = hashMapOf<String, String>()
    loc?.let {
      params.put("location", loc)
    }
    search?.let {
      params.put("search", search)
    }
    params.put("type", RestaurantTab.TYPE_PICKUP)
    return dataSource.getRestaurantSection(params)
  }

  /**
   * dispose when the context destroy
   */
  override fun dispose() {
    super.dispose()
    disposeRestaurant?.dispose()
    disposeRegion?.dispose()
  }

  override fun buildUseCaseObservable(params: Params?): Single<List<Any>> {
    return Single.create {
    }
  }

  fun isClosed(item: Restaurant): Boolean {
    return (item.closed != null) || mapper.mapClosed(item) != null
  }
}

sealed class RestaurantUiModel(
  open val restaurant: Restaurant? = null
) {

  fun isVertical() = this.javaClass in listOf(
    RestaurantVertical::class.java,
    RestaurantBigImageVertical::class.java
  )

  fun getVerticalRestaurant() = if (isVertical()) restaurant else null

  /**
   * the restaurant of search by keyword and location
   */
  data class RestaurantVertical(
    override val restaurant: Restaurant,
    val position: Int,
    val size: Int,
    val group: RestaurantGroup? = null,
    var selectedPosition: Int? = null
  ) : RestaurantUiModel()

  fun toVerticalBigImageOrThis(): RestaurantUiModel {
    (this as? RestaurantVertical)?.let {
      if (it.restaurant.images?.landing?.isNotBlank() == true) {
        return RestaurantBigImageVertical(
          it.restaurant,
          it.position,
          it.size,
          it.group,
          it.selectedPosition
        )
      }
    }
    return this
  }

  /**
   * the restaurant of search by keyword and location
   */
  data class RestaurantBigImageVertical(
    override val restaurant: Restaurant,
    val position: Int,
    val size: Int,
    val group: RestaurantGroup? = null,
    var selectedPosition: Int? = null
  ) : RestaurantUiModel()

  /**
   * the vertical restaurant show more button
   */
  data class RestaurantVerticalMore(
    val group: RestaurantRemoteGroup,
    val groupIndex: Long?
  ) : RestaurantUiModel()

  /**
   * the restaurant region banner
   */
  data class RestaurantBanner(
    val banners: List<Banner>?,
    val updateDate: Long
  ) : RestaurantUiModel()

  /**
   * the restaurant group
   */
  data class RestaurantCategory(
    val group: RestaurantGroup?,
    val position: Int,
    val groupIndex: Int? = null,
    val jumpGroups: List<RestaurantGroup>? = null,
    val isSubMore: Boolean = false,
    var sorts: List<RestaurantSort>? = null,
    var sorted: RestaurantSort? = null,
    var models: MutableList<RestaurantUiModel>? = null
  ) : RestaurantUiModel()

  data class RestaurantHorizontal(
    val restaurants: List<Restaurant>,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long? = null
  ) : RestaurantUiModel()

  data class RestaurantPlan(val plan: SubscriptionPlan) : RestaurantUiModel()

  data class RestaurantCuisine(val cuisines: List<Cuisine>) : RestaurantUiModel()

  data class RestaurantRecommend(
    val recommend: RestaurantRecommendData?,
    val top: RestaurantRecommendData?
  ) : RestaurantUiModel()

  data class RestaurantLucky(val name: String, val button: String) : RestaurantUiModel()

  data class RestaurantDish(
    val dishes: List<RestaurantDishItem>?,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long? = null
  ) : RestaurantUiModel()

  /**
   * cuisine tag ui model
   */
  data class RestaurantCuisineTag(val cuisine: Cuisine) : RestaurantUiModel()

  /**
   * load more error ui model empty layout
   */
  data class RestaurantLoadMoreError(val key: Int?) : RestaurantUiModel()

  /**
   * menu restaurant bundle
   */
  data class RestaurantBundle(
    override val restaurant: Restaurant,
    val hostRestaurant: Restaurant?,
    var bundles: ArrayList<String>? = null,
    val refreshMenu: () -> Unit = {}
  ) : RestaurantUiModel()

  fun toBigImageBundleOrThis() = (this as? RestaurantBundle)?.let {
    if (it.restaurant.images?.landing?.isNotBlank() == true) {
      RestaurantBigImageBundle(
        it.restaurant,
        it.hostRestaurant,
        it.bundles,
        it.refreshMenu
      )
    } else {
      this
    }
  } ?: this

  data class RestaurantBigImageBundle(
    override val restaurant: Restaurant,
    val hostRestaurant: Restaurant?,
    var bundles: ArrayList<String>? = null,
    val refreshMenu: () -> Unit = {}
  ) : RestaurantUiModel()

  /**
   * feeling lucky v2
   */
  data class RestaurantLuckyCombine(
    var name: String,
    var position: Int? = null,
    var cards: List<LuckRecommendBean>? = null
  ) : RestaurantUiModel()

  /**
   * restaurant search prompt label
   */
  data class RestaurantSearchPrompt(var label: String?) : RestaurantUiModel()

  /**
   * restaurant motd
   */
  data class RestaurantMotd(var motd: InternationalizationContent?) : RestaurantUiModel()

  /**
   * restaurant tab section
   */
  data class RestaurantTabSection(var tabs: List<RestaurantTab>?) : RestaurantUiModel()

  /**
   * restaurant bottom empty placeholder (for restaurant list page bottom tab）
   */
  data class RestaurantEmptyPlace(var height: Int) : RestaurantUiModel()

  /**
   * restaurant sort error section
   */
  data class RestaurantErrorSection(
    var request: RestaurantRequest?,
    val lastModels: MutableList<RestaurantUiModel> = mutableListOf(),
    val groupModel: RestaurantUiModel?,
    val position: Int? = null,
    var errorMessage: String?
  ) : RestaurantUiModel()

  /**
   * menu food cascade
   */
  data class RestaurantCascade(
    var dishes: List<RestaurantDishItem>?,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long? = null
  ) : RestaurantUiModel()
}
