package com.ricepo.app.restaurant

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.ricepo.app.R
import com.ricepo.style.ResourcesUtil

//
// Created by Mark on 26/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class SwitchView : FrameLayout {

  private lateinit var view: View
  private lateinit var tv_pickup: TextView
  private lateinit var tv_delivery: TextView

  private var switchChangeListener: SwitchChangeListener? = null

  companion object {
    const val PICK_UP = 1
    const val DELIVERY = 2
  }

  constructor(context: Context) : super(context) {
    init(context)
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    init(context)
  }

  private fun init(context: Context) {
    view = LayoutInflater.from(context).inflate(R.layout.restaurant_switch_view, this)
    tv_pickup = view.findViewById(R.id.tv_pickup)
    tv_delivery = view.findViewById(R.id.tv_delivery)

    tv_pickup.setOnClickListener {
      tv_pickup.setBackgroundResource(com.ricepo.style.R.drawable.pickup_switch)
      tv_pickup.text = ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_pickup)
      tv_pickup.setTextColor(context.getColor(com.ricepo.style.R.color.mainText))
      tv_delivery.setTextColor(context.getColor(com.ricepo.style.R.color.subText))
      tv_delivery.setBackgroundColor(Color.argb(0, 0, 0, 0))
      switchChangeListener?.let {
        it.onSwitchChange(PICK_UP)
      }
    }

    tv_delivery.setOnClickListener {
      tv_delivery.setBackgroundResource(com.ricepo.style.R.drawable.pickup_switch)
      tv_delivery.text = ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_delivery)
      tv_delivery.setTextColor(context.getColor(com.ricepo.style.R.color.mainText))
      tv_pickup.setTextColor(context.getColor(com.ricepo.style.R.color.subText))
      tv_pickup.setBackgroundColor(Color.argb(0, 0, 0, 0))
      switchChangeListener?.let {
        it.onSwitchChange(DELIVERY)
      }
    }
  }

  fun reloadSwitchText() {
    tv_pickup.text = ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_pickup)
    tv_delivery.text = ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_delivery)
  }

  fun setOnSwitchChangeListener(listener: SwitchChangeListener) {
    this.switchChangeListener = listener
  }

  interface SwitchChangeListener {
    fun onSwitchChange(index: Int)
  }
}
