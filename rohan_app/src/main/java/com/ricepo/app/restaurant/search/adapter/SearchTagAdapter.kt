package com.ricepo.app.restaurant.search.adapter

import android.app.Activity
import android.content.Context
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import com.ricepo.app.R
import com.ricepo.app.databinding.ItemSearchTagBinding
import com.ricepo.app.model.SearchTag
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.tag.TagsAdapter

//
// Created by <PERSON><PERSON> on 20/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class SearchTagAdapter(private val tags: List<SearchTag>) : TagsAdapter() {

  override val count: Int
    get() = tags.size

  override fun getItem(position: Int): Any? {
    return tags[position]
  }

  override fun getPopularity(popularity: Int): Int {
    return 1
  }

  override fun getView(context: Context?, position: Int, parent: ViewGroup?): View? {
    if (context is Activity) else return null
    val binding = ItemSearchTagBinding.inflate(context.layoutInflater, parent, false)
    val searchTag = getItem(position) as SearchTag
    setTagView(binding, searchTag)
    return binding.root
  }

  override fun onThemeColorChanged(child: View?, progress: Int) {
  }

  private fun setTagView(tagBinding: ItemSearchTagBinding, searchTag: SearchTag) {
    // tag title
    tagBinding.tvSearchTag.text = searchTag.keyword
    // tag size
    val maxCount = tags.map { it.count ?: 1 }.maxOrNull() ?: 1
    val minCount = tags.map { it.count ?: 1 }.maxOrNull() ?: 1
    val deltaSize = maxCount - minCount
    val count = searchTag.count ?: 1
    val percentFont = if (deltaSize == 0) 1f else (count - minCount).div(deltaSize.toFloat())
    val maxFont = 52
    val minFont = 20
    val deltaFont = (maxFont - minFont).toFloat()
    val fontSize = (minFont + deltaFont.times(percentFont))
    tagBinding.tvSearchTag.setTextSize(TypedValue.COMPLEX_UNIT_SP, fontSize)

    // tag color
    if (searchTag.highlight == true) {
      tagBinding.tvSearchTag.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.rest_address_fill, tagBinding.root
        )
      )
    } else {
      tagBinding.tvSearchTag.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.mainText, tagBinding.root
        )
      )
    }
  }
}
