package com.ricepo.app.restaurant.usecase

import com.ricepo.app.model.PopUp
import com.ricepo.app.restapi.RiceApi
import com.ricepo.base.data.pref.PrefDataSource
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import kotlinx.coroutines.flow.MutableSharedFlow
import javax.inject.Inject

class PopupUseCase @Inject constructor(
  private val prefDataSource: PrefDataSource,
  private val riceApi: RiceApi
) {

  val popupData = MutableSharedFlow<List<PopUp>>(replay = 1)

  val checkRating = MutableSharedFlow<Boolean>(replay = 1)

  suspend fun fetchPopup(
    regionId: String?,
    isRating: Boolean,
  ) {
    regionId?.let {
      riceApi.fetchPopup(region_id = it).onSuccess {
        val existIds = prefDataSource.getPopups()
        data.filter {
          !existIds.contains(it._id)
        }.takeIf {
          it.isNotEmpty()
        }?.let {
          popupData.tryEmit(it)
          prefDataSource.cachePopups(
            it.map { pop ->
              pop._id
            }.toSet()
          )
        }
      }.onError {
        checkRating.tryEmit(isRating)
      }.onException {
        checkRating.tryEmit(isRating)
      }
    }
  }
}
