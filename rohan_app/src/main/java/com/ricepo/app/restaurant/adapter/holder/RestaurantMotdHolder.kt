package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.RestaurantItemMotdBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.model.localize

//
// Created by <PERSON><PERSON> on 5/30/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantMotdHolder(private val binding: RestaurantItemMotdBinding) :
  RecyclerView.ViewHolder(binding.root) {

  init {
  }

  fun bind(model: RestaurantUiModel.RestaurantMotd) {
    // motd info
    if (! model?.motd?.localize().isNullOrEmpty()) {
      binding.tvMotdTitle.text = model.motd?.localize()
      binding.tvMotdTitle.visibility = View.VISIBLE
    } else {
      binding.tvMotdTitle.visibility = View.GONE
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantMotdHolder {
      val binding = RestaurantItemMotdBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return RestaurantMotdHolder(binding)
    }
  }
}
