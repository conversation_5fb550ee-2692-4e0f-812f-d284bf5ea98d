package com.ricepo.app.restaurant.adapter.holder

import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantBigImageItemVerticalBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.dp
import com.ricepo.style.view.placeVisible

class RestaurantVerticalBigImageHolder(
  private val binding: RestaurantBigImageItemVerticalBinding,
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(
    item: RestaurantUiModel.RestaurantBigImageVertical,
    isShowCoin: Boolean = true,
  ) {
    // set tag
    binding.root.tag = item

    val event = FirebaseRestaurantEvent(
      rGroupId = item.group?.groupId,
      rGroupType = item.group?.type,
      rGroupIndex = item.group?.index?.toLong(),
      rRestaurantIndex = item.position.toLong()
    )
    binding.root.setTag(com.ricepo.base.R.id.tag_firebase_event, event)

    bind(item, event, isShowCoin)
  }

  private fun bind(
    item: RestaurantUiModel.RestaurantBigImageVertical,
    event: FirebaseRestaurantEvent,
    isShowCoin: Boolean
  ) {

    val restaurant = item.restaurant

    val bindMapper = RestaurantBindMapper(
      restaurant,
      binding.root.context
    )

    // restaurant name
    binding.inRestaurantInfo.tvRestaurantName.text = bindMapper.bindName(isShowCoin)
    binding.inRestaurantInfo.tvRestaurantName.maxLines = 1

//    // ratio 2.04
//    val height = 600
//    val width = 1000
    ImageLoader.load(binding.bigImage, item.restaurant.images?.landing)

    // todo deal the last order logic later
    bindRestaurantInfo(bindMapper, restaurant)

    // bind the closed status
    val pairClosed = bindMapper.bindClosed()
    bindRestaurantClosed(pairClosed)
  }

  private fun bindRestaurantInfo(bindMapper: RestaurantBindMapper, item: Restaurant) {
    // restaurant info and promotion
    val pairInfo = bindMapper.bindInfo(needShowTag = false)
    binding.inRestaurantInfo.tvRestaurantInfo.text = pairInfo.first
    binding.inRestaurantInfo.tvRestaurantInfo.setTextColor(pairInfo.second)

    binding.inRestaurantInfo.tvRestaurantTags.text = bindMapper.bindTags()

//    val promotion = bindMapper.bindCombinePromotion()
//
//    // auto move up one line
//    if (binding.inRestaurantInfo.tvRestaurantInfo.text.isNullOrEmpty()) {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantInfo, promotion)
//    } else {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantSubInfo, promotion)
//    }

    // show the pool layout
    binding.rtvPool.isVisible = (item.pool != null)

//    bindMapper.bindRating(binding.inRestaurantInfo.tvRating)

    with(binding.tvFeatureMotd) {
      val bindMotd = bindMapper.bindMotd(
        featureTint = com.ricepo.style.R.color.rating_color,
        motdTint = com.ricepo.style.R.color.w
      )
      placeVisible(bindMotd.isNotBlank())
      text = bindMotd
      isSelected = true
    }

    bindPool(item.pool)
  }

  private fun bindRestaurantSubInfo(
    tvView: TextView,
    promotion: SpannableStringBuilder?
  ) {
    tvView.text = promotion
    tvView.setTextColor(
      ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, binding.root.context)
    )
  }

  private fun bindRestaurantClosed(pairClosed: Pair<SpannableStringBuilder?, Int>) {
    binding.inRestaurantClosed.tvRestaurantClosed.isVisible = !pairClosed.first.isNullOrEmpty()
    pairClosed.first?.let {
      binding.inRestaurantClosed.tvRestaurantClosed.text = it
      binding.inRestaurantClosed.tvRestaurantClosed.setTextColor(pairClosed.second)
    }
  }

  private fun bindPool(pool: RestaurantPool?) {
    if (pool == null) {
      return
    }
    binding.rtvPool.setMessage(pool.message?.localize())
    binding.rtvPool.setExpireAt(pool.expiresAt)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      isClickMenu: Boolean = true,
      itemClick: FunRestaurantMenuClick
    ): RestaurantVerticalBigImageHolder {
      val binding = RestaurantBigImageItemVerticalBinding.inflate(
        LayoutInflater.from(parent.context)
      )

      initRestaurantVerticalBinding(binding, isClickMenu) { model, name ->
        itemClick(model, name)
      }

      return RestaurantVerticalBigImageHolder(binding)
    }

    public fun initRestaurantVerticalBinding(
      binding: RestaurantBigImageItemVerticalBinding,
      isClickMenu: Boolean = true,
      itemClick: FunRestaurantMenuClick
    ) {
      val bundleParams = binding.root.layoutParams
      val params = if (bundleParams is FrameLayout.LayoutParams) {
        // layout in the FrameLayout of bundle holder
        FrameLayout.LayoutParams(
          ViewGroup.LayoutParams.MATCH_PARENT,
          ViewGroup.LayoutParams.WRAP_CONTENT
        )
      } else {
        RecyclerView.LayoutParams(
          ViewGroup.LayoutParams.MATCH_PARENT,
          ViewGroup.LayoutParams.WRAP_CONTENT
        )
      }
      params.bottomMargin =
        ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_section_space)
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)

      binding.root.layoutParams = params

      initListener(binding, isClickMenu, itemClick)
    }

    private fun initListener(
      binding: RestaurantBigImageItemVerticalBinding,
      isClickMenu: Boolean,
      itemClick: FunRestaurantMenuClick
    ) {

      if (isClickMenu) {
        // bind listener
        binding.root.clickWithTrigger { view ->
          navigateMenu(view, itemClick)
        }
      }
    }

    private fun navigateMenu(view: View, itemClick: FunRestaurantMenuClick) {
      val item = view.tag
      if (item is RestaurantUiModel.RestaurantBigImageVertical) {
        itemClick(item, null)
        val event = view.getTag(com.ricepo.base.R.id.tag_firebase_event)
        if (event is FirebaseRestaurantEvent && item.restaurant.pool != null) {
          event.rPoolTime = SimpleDateUtils.toDateDiffSeconds(item.restaurant.pool?.expiresAt)
        }
        view.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
        AnalyticsFacade.logEvent(view, FirebaseEventName.rSelectRestaurant)
      }
    }
  }
}
