package com.ricepo.app.restaurant.submore

import android.os.Bundle
import android.text.SpannableStringBuilder
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.firebase.analytics.FirebaseAnalytics
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.ActivityRestaurantSubmoreBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.restaurant.adapter.RestaurantGroupAdapter
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.FooterLoadStateAdapter
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.simulateClickListener
import com.ricepo.base.model.localize
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest

//
// Created by Thomsen on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_RESTAURANT_SUBMORE)
class RestaurantSubMoreActivity : BaseActivity() {

  val viewModel: RestaurantSubmoreViewModel by viewModels()

  private lateinit var binding: ActivityRestaurantSubmoreBinding

  private var adapter: RestaurantGroupAdapter? = null

  private var scrollPosition = 0

  private var isNotAlreadyScroll = true

  private lateinit var menuMapper: MenuMapper

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityRestaurantSubmoreBinding.inflate(layoutInflater)
    setContentView(binding.root)

    val restaurantRequest: RestaurantRequest? = intent.extras?.getParcelable(
      FeaturePageConst.PARAM_RESTAURANT_SUBMORE_REQUEST
    )
    scrollPosition = intent.getIntExtra(
      FeaturePageConst.PARAM_RESTAURANT_SUBMORE_SCROLL_POSITION, 0
    )

    menuMapper = MenuMapper()

    setupRecyclerView()

    getSubmoreData(restaurantRequest)
  }

  override fun onStart() {
    super.onStart()
    checkMenuCart()
  }

  private fun getSubmoreData(request: RestaurantRequest?) {
    val req = request ?: return

    viewModel.getSubmoreRestaurants(req)
  }

  private fun setupRecyclerView() {
    // isRestaurantHome for top margin to group category
    val isRestaurantHome = false
    adapter = RestaurantGroupAdapter(
      this,
      isRestaurantHome = isRestaurantHome
    ) { model, foodName ->
      model.getVerticalRestaurant()?.let { restaurant ->
        val searches = foodName?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenuForBusy(
          this@RestaurantSubMoreActivity,
          restaurant, searches
        )
        firebaseAnalytics.logEvent(
          FirebaseAnalytics.Event.SELECT_ITEM,
          bundleOf(
            Pair(FirebaseAnalytics.Param.ITEM_ID, restaurant.id),
            Pair(FirebaseAnalytics.Param.ITEM_NAME, restaurant.name?.localize())
          )
        )
      }
    }

    adapter?.addLoadStateListener { loadState ->
      if (loadState.refresh is LoadState.Loading) {
        Loading.showLoading(this)
        clearErrorView(binding.flRestaurantPage)
      } else {
        Loading.hideLoading()

        if (loadState.refresh is LoadState.Error) {
          val state = loadState.refresh as LoadState.Error
          showErrorNetworkView(state.error.message)
        } else {
          if (scrollPosition != 0 && isNotAlreadyScroll) {
            val layoutManager =
              (binding.rvRestaurantSubmore.layoutManager as LinearLayoutManager)
            // item on top
            layoutManager.stackFromEnd = true
            binding.rvRestaurantSubmore.post {
              val target = layoutManager.findViewByPosition(scrollPosition)
              // scrollPosition + 2 is group item + footer load state
              if (target != null) {
                val offset = binding.rvRestaurantSubmore.measuredHeight - target.measuredHeight
                layoutManager.scrollToPositionWithOffset(scrollPosition + 2, offset)
              } else {
                layoutManager.scrollToPosition(scrollPosition + 2)
              }
            }
          }
        }
      }
    }

    // recycler scroll listener
    binding.rvRestaurantSubmore.addOnScrollListener(object : OnScrollListener() {
      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        isNotAlreadyScroll = false
      }
    })

    binding.rvRestaurantSubmore.itemAnimator = null
    binding.rvRestaurantSubmore.adapter = adapter?.withLoadStateFooter(
      FooterLoadStateAdapter {
        adapter?.retry()
      }
    )
    binding.rvRestaurantSubmore.addOnScrollListener(PromotionMarqueeListener())
    binding.rvRestaurantSubmore.post {
      PromotionMarqueeListener.setPromotionMarquee(binding.rvRestaurantSubmore)
    }
    lifecycleScope.launchWhenCreated {
      viewModel.restaurants.collectLatest {
        adapter?.submitData(it)
      }
    }
  }

  /**
   * check the restaurant cart
   */
  private fun checkMenuCart() {
    lifecycleScope.launchWhenStarted {
      // the newest cart
      val cart = RestaurantCartCache.getRestaurantCartSuspend(null, true)
      val restaurant = cart?.restaurant
      if (cart != null && restaurant != null && cart.cartList?.isNotEmpty() == true) {
        val allCarts = GroupOrderCache.getAllCartsSuspend(cart.cartList, restaurant)
        showCart(true)
        val triple = menuMapper.mapCartUiModel(allCarts, cart)
        val uiModels = mutableListOf<CartUiModel>()
        restaurant?.name?.let {
          val name = SpannableStringBuilder(it.localize())
          uiModels.add(CartUiModel.CartMenuInfoUiModel(name))
        }
        uiModels.addAll(triple.first)
        val cartLayoutManager = LinearLayoutManager(this@RestaurantSubMoreActivity)
        cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
        val cartAdapter = CartAdapter(uiModels)
        binding.inMenuCart.rcvMenuCart.apply {
          layoutManager = cartLayoutManager
          adapter = cartAdapter
        }
        // simulation of external click events
        binding.inMenuCart.rcvMenuCart.simulateClickListener {
          binding.inMenuCart.clMenuCart.performClick()
        }
        binding.inMenuCart.clMenuCart.clickWithTrigger { _ ->
          FeaturePageRouter.navigateCheckoutByEntrance(
            this@RestaurantSubMoreActivity,
            restaurant, FeaturePageConst.PAGE_RESTAURANT_SUBMORE
          )
        }
      } else {
        showCart(false)
      }
    }
  }

  /**
   * show the menu cart
   */
  private fun showCart(isShow: Boolean) {
    val params = binding.inMenuCart.clMenuCart.layoutParams
    if (params is LinearLayout.LayoutParams) {
      if (isShow) {
        binding.inMenuCart.tvDelivery.isVisible = false
        params.height = LinearLayout.LayoutParams.WRAP_CONTENT
        binding.inMenuCart.clMenuCart.isVisible = true
      } else {
        params.height = 0
        binding.inMenuCart.clMenuCart.isVisible = false
      }
      binding.inMenuCart.clMenuCart.layoutParams = params
    }
  }

  private fun showErrorNetworkView(message: String?) {
    showErrorView(
      binding.flRestaurantPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      listener = {
        adapter?.refresh()
      }
    )
  }
}
