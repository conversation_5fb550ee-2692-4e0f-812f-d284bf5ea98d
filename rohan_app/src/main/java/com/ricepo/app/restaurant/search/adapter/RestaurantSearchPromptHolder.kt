package com.ricepo.app.restaurant.search.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantItemSearchPromptBinding
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionHolder

//
// Created by <PERSON><PERSON> on 20/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class RestaurantSearchPromptHolder(private val binding: RestaurantItemSearchPromptBinding) :
  SectionHolder(binding.root) {

  fun bind(label: String?) {
    // set tag
    binding.tvSearchPrompt.text = label
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantSearchPromptHolder {
      val binding = RestaurantItemSearchPromptBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_15)
      binding.root.layoutParams = params
      return RestaurantSearchPromptHolder(binding)
    }
  }
}
