package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantVerticalMoreBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent

//
// Created by <PERSON><PERSON> on 20/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantVerticalMoreHolder(val binding: RestaurantVerticalMoreBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantUiModel.RestaurantVerticalMore) {
    binding.root.clickWithTrigger {
      val group = uiModel.group
      val request = RestaurantRequest(
        groupId = group.groupId,
        cursor = group.cursor, restaurants = group.restaurants,
        groupName = group.name, groupImage = group.image,
        groupDesc = group.description
      )
      val scrollPosition = (group.restaurants?.size ?: 0) - 1
      FeaturePageRouter.navigateRestaurantSubmore(request, scrollPosition)
      it.setTag(
        com.ricepo.base.R.id.tag_firebase_event,
        FirebaseRestaurantEvent(
          rGroupId = group.groupId,
          rGroupIndex = uiModel.groupIndex,
          rGroupType = group.type,
        )
      )
      AnalyticsFacade.logEvent(it, FirebaseEventName.rShowMore)
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantVerticalMoreHolder {
      val binding = RestaurantVerticalMoreBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )

      return RestaurantVerticalMoreHolder(binding)
    }
  }
}
