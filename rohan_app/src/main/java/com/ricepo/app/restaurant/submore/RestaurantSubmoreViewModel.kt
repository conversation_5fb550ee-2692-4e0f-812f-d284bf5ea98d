package com.ricepo.app.restaurant.submore

import androidx.lifecycle.viewModelScope
import com.ricepo.app.restaurant.RestaurantUseCase
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 15/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class RestaurantSubmoreViewModel @Inject constructor(
  private val useCase: RestaurantUseCase
) : BaseViewModel() {

  private val requestChannel = MutableSharedFlow<RestaurantRequest?>(1)

  @OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
  val restaurants = flowOf(
    requestChannel
      .filterNotNull()
      .flatMapLatest {
        useCase.getNearRestaurants(it)
      }
  ).flatMapMerge { it }

  fun getSubmoreRestaurants(request: RestaurantRequest) {
    viewModelScope.launch {
      requestChannel.emit(request)
    }
  }
}
