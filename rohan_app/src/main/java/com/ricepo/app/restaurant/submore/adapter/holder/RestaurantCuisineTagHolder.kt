package com.ricepo.app.restaurant.submore.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.RestaurantCuisineTagBinding
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize

//
// Created by <PERSON><PERSON> on 15/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantCuisineTagHolder(private val binding: RestaurantCuisineTagBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantUiModel.RestaurantCuisineTag) {

    val data = uiModel.cuisine

    ImageLoader.load(binding.ivCuisine, data.image?.localize())
    binding.tvCuisineLabel.text = data.name?.localize()
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantCuisineTagHolder {
      val binding = RestaurantCuisineTagBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return RestaurantCuisineTagHolder(binding)
    }
  }
}
