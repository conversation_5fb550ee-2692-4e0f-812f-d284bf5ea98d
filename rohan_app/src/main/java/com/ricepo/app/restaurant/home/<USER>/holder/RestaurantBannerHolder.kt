package com.ricepo.app.restaurant.home.adapter.holder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.RestaurantBannerBinding
import com.ricepo.app.databinding.RestaurantItemBannerBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.recommend.RecommendType
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.view.CircleShadowIndicator
import com.ricepo.app.view.ShareRicepoUtil
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Banner
import com.ricepo.base.model.BannerType
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.network.EnvNetwork
import com.ricepo.style.ResourcesUtil
import com.youth.banner.adapter.BannerAdapter

//
// Created by Thomsen on 4/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantBannerHolder(
  private val binding: RestaurantBannerBinding,
  lifecycleOwner: LifecycleOwner
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    // manage the lifecycle that banner itself
    binding.bannerRestaurant.addBannerLifecycleObserver(lifecycleOwner)
  }

  fun bindBanner(model: RestaurantUiModel.RestaurantBanner) {

    val banners = model.banners
    // banner info
    if (banners?.isNullOrEmpty() == false) {
      binding.bannerRestaurant.visibility = View.VISIBLE

      val adp = RestaurantBanner2Adapter(binding, banners ?: listOf())
      binding.bannerRestaurant.setAdapter(adp)

      binding.bannerRestaurant.indicator = CircleShadowIndicator(binding.root.context)
    } else {
      binding.bannerRestaurant.visibility = View.GONE
    }
  }

  companion object {
    fun create(parent: ViewGroup, lifecycleOwner: LifecycleOwner): RestaurantBannerHolder {
      val binding = RestaurantBannerBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_153dp)
      )
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_section_space)
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)

      binding.root.layoutParams = params

      return RestaurantBannerHolder(binding, lifecycleOwner)
    }
  }
}

/**
 * banner item adapter
 */
class RestaurantBanner2Adapter constructor(
  private val bindingLayout: RestaurantBannerBinding,
  banners: List<Banner> = listOf()
) :
  BannerAdapter<Banner, RestaurantBanner2Adapter.ViewHolder>(banners) {

  override fun onBindView(holder: ViewHolder, data: Banner, position: Int, size: Int) {
    val currentPosition = position
    holder.bind(data, currentPosition, position)
  }

  override fun onCreateHolder(parent: ViewGroup, viewType: Int): ViewHolder {
    val binding = RestaurantItemBannerBinding.inflate(LayoutInflater.from(parent.context))
    // Pages must fill the whole ViewPager2 (use match_parent)
    val params = ConstraintLayout.LayoutParams(
      ViewGroup.LayoutParams.MATCH_PARENT,
      ViewGroup.LayoutParams.MATCH_PARENT
    )
    binding.root.layoutParams = params
    return ViewHolder(binding)
  }

  class ViewHolder internal constructor(private val binding: RestaurantItemBannerBinding) :
    RecyclerView.ViewHolder(binding.root) {

    fun bind(banner: Banner, currentPosition: Int, position: Int) {

      // set banner to tag
      binding.root.tag = banner

      binding.root.clickWithTrigger { view ->
        val banner = view.tag as Banner
        handleBannerNavigate(view, banner)
      }

      binding.tvBannerTitle.text = getTitle(banner.title?.localize())
      binding.tvBannerButton.text = banner.button?.localize()

      binding.tvBannerButton.isVisible = (binding.tvBannerButton.text.isNotEmpty())

      ImageLoader.load(binding.ivBannerIcon, banner.image?.localize())
    }

    private fun getTitle(title: String?): String? {
      var text = title?.replace("￥", "¥") ?: return null
      text.takeIf { it.isNotEmpty() }?.last()?.takeIf { it == '\n' }
        ?.let {
          text = text.substring(0, text.length - 1)
        }
      return text
    }

    private fun handleBannerNavigate(view: View, banner: Banner) {
      when (banner.type) {
        BannerType.web -> {
          // show webview
          var url = banner.url ?: EnvNetwork.RICEPO_WEBSITE
          FeaturePageRouter.navigateBannerWeb(url, banner)
        }
        BannerType.refer -> {
//          FeaturePageRouter.navigateRefer(view.context)
          ShareRicepoUtil.checkAndShowActionSheet(view.context) {
          }
        }
        BannerType.plan -> {
          // subscription plan
          // TODO getRefer
          CustomerCache.liveCustomer { customer ->
            if (!customer?.id.isNullOrEmpty()) {
              val subscription = banner.data?.plan ?: return@liveCustomer
              FeaturePageRouter.navigateSubscription(view.context, subscription, true)
            } else {
              FeaturePageRouter.navigateLogin()
            }
          }
        }
        BannerType.rank -> {
          // recommend food with rank
          FeaturePageRouter.navigateRecommend(RecommendType.TYPE_RANK)
        }
        BannerType.recommendation -> {
          // recommend food
          CustomerCache.liveCustomer { customer ->
            if (!customer?.id.isNullOrEmpty()) {
              FeaturePageRouter.navigateRecommend(RecommendType.TYPE_RECOMMEND)
            } else {
              FeaturePageRouter.navigateLogin()
            }
          }
        }
        BannerType.restaurant -> {
          banner.data?.restaurantId?.let {
            val restaurant = Restaurant(id = it)
            FeaturePageRouter.navigateMenu(restaurant)
          }
        }
        else -> {}
      }
    }
  }
}
