package com.ricepo.app.restaurant

import com.ricepo.app.R
import com.ricepo.base.model.DeliveryZoneFees
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantScore
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.tools.tilde.Tilde
import com.ricepo.network.RemoteMapper
import com.ricepo.style.ResourcesUtil
import org.threeten.bp.Instant
import org.threeten.bp.ZoneId
import org.threeten.bp.ZonedDateTime
import org.threeten.bp.format.DateTimeFormatter
import javax.inject.Inject
import kotlin.math.floor

/**
 * to handle restaurant data for local using
 */
open class RestaurantMapper @Inject constructor() : BaseMapper(), RemoteMapper<Restaurant, Restaurant> {

  /**
   * map to local from remote restaurant
   * @param remote the network response object
   */
  override fun mapFromRemote(remote: Restaurant): Restaurant {
    var local = remote

    // get the restaurant open status
    local.isClosed = mapClosed(remote)
    // get the restaurant score
    local.score = mapScore(remote)

    return local
  }

  fun mapClosed(restaurant: Restaurant): String? {
    // Get current time based on rest timezone or NewYork time
    val timeZone = restaurant.timezone ?: "America/New_York"
    val date = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(timeZone))

    // convert time to utc time
//        val date = dateZone.withZoneSameInstant(ZoneId.of("UTC"))

    // the pair of closed and the time that opened and closed
    var pairClosed = compareHour(restaurant)

    var result: String? = null

    val isClosed = pairClosed.first
    val temp = pairClosed.second

    if (isClosed) {
      val scheduled = restaurant.delivery?.scheduled
      when {
        temp["start"] != null -> {
          val start = temp["start"] ?: 0
          val startDate = date.withHour(floor(start / 60.0).toInt())
            .withMinute(start % 60)
          val startTime = startDate.format(DateTimeFormatter.ofPattern("h:mm a"))
          // use resources util to get string
          result = if (scheduled == true) {
            ResourcesUtil.getString(com.ricepo.style.R.string.opens_at_scheduled, startTime)
          } else {
            ResourcesUtil.getString(com.ricepo.style.R.string.opens_at, startTime)
          }
        }
        temp["end"] != null -> {
          val end = temp["end"] ?: 0
          val endDate = date.withHour(floor(end / 60.0).toInt())
            .withMinute(end % 60)
          val endTime = endDate.format(DateTimeFormatter.ofPattern("h:mm a"))
          // the restaurant closed at time
          result = if (scheduled == true) {
            ResourcesUtil.getString(com.ricepo.style.R.string.closed_at_scheduled, endTime)
          } else {
            ResourcesUtil.getString(com.ricepo.style.R.string.closed_at, endTime)
          }
        }
        else -> {
          result = ResourcesUtil.getString(com.ricepo.style.R.string.closed_today)
        }
      }
    }

    return result
  }

  /**
   * compare current time with opened and closed time to confirm restaurant business or not
   * @param restaurant
   * @return Pair<Boolean, Map<String, Int>>  the tuple type
   */
  private fun compareHour(restaurant: Restaurant): Pair<Boolean, Map<String, Int>> {

    val timeZone = restaurant.timezone ?: "America/New_York"
    val dateZone = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(timeZone))
    // convert time to utc time
//        val date = dateZone.withZoneSameInstant(ZoneId.of("UTC"))

    // current minute of restaurant from zero to show restaurant open time or closed time
    val currentMinute = dateZone.minute + dateZone.hour * 60
    // the day of week utc DayOfWeek.of(dow0 + 1)  1 == MONDAY
    val dow = dateZone.dayOfWeek.value % 7

    var temp = mutableMapOf<String, Int>()

    // find is new add extension method
    val open = Tilde.find(restaurant.hours ?: listOf()) {
      var result = true

      when {
        // check if it is day of week [0, 6]
        dow != it.dayOfWeek -> {
          result = false
        }
        // match start time
        currentMinute < it.start -> {
          if (temp["start"] == null || it.start < temp["start"]!!) {
            temp["start"] = it.start
          }
          result = false
        }
        // match end time
        currentMinute > it.end -> {
          if (temp["end"] == null || it.end > temp["end"]!!) {
            temp["end"] = it.end
          }
          result = false
        }
      }

      result
    }

    return Pair(open == null, temp)
  }

  private fun mapScore(remote: Restaurant, restId: String? = null): RestaurantScore? {
    val score = remote.score ?: RestaurantScore()

    val fees = computeFreeDelivery(remote.delivery?.fees)

    if (remote.fake) score.fake = 0.0
    // closed of restaurant
    if (remote.closed != null) score.closed = 0.0
    // hour close of restaurant
    if (remote.isClosed != null) score.isClosedVal = 0.0
    // Most recent ordered restaurant on the top, if opened
    if (remote.id == restId) score.id = 1.0

    // search restaurant score
    if (remote.match?.name != null) score.matchName = 1.0
    if (remote.match?.tags != null) score.matchTags = 1.0
    if (remote.match?.food != null) score.matchFood = 1.0

    // featured rest on the top
    if (remote.featured == true) score.featured = 1.0

    // less minimum on the top
    if (fees != null) {
      if (fees?.minimum == null) {
        score.minimun = 0.toDouble()
      }
      if (fees?.minimum == 0.0) {
        score.minimun = 2.toDouble()
      } else if (fees?.minimum != 0.0) {
        score.minimun = (1 / fees!!.minimum).toDouble()
      }
    }

    // balance score
    if (remote.reward != null) score.balance = remote.reward?.balance?.toDouble() ?: 0.0

    return score
  }

  // Get the minimum free delivery with matching subtotal
  fun computeFreeDelivery(
    fees: List<DeliveryZoneFees?>?,
    subTotal: Int? = null
  ): DeliveryZoneFees? {
    // / Safe check
    if (fees == null) return null

    // Match the minimum of which grade the current subtotal belongs to
    if (subTotal != null) {
      return fees.filter { subTotal!! >= (it?.minimum ?: 0.0) }.maxByOrNull {
        it?.minimum ?: 0.0
      }
    }
    // No subtotal check, just get the first delivery (smallest minimum)
    return fees.filter { it?.adjustments?.customer ?: 0 > 0 }.minByOrNull {
      it?.minimum ?: 0.0
    }
  }
}
