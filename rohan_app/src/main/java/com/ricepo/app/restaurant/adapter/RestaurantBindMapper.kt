package com.ricepo.app.restaurant.adapter

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.SpannedString
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.StrikethroughSpan
import android.widget.TextView
import androidx.core.text.buildSpannedString
import androidx.core.view.isVisible
import com.ricepo.app.R
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.data.BusyImage
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantInfo
import com.ricepo.base.model.localize
import com.ricepo.base.tools.compressSpace
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan
import com.ricepo.style.view.CenterAlignImageSpan

//
// Created by <PERSON><PERSON> on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantBindMapper(
  private val item: Restaurant,
  private val context: Context? = null
) {

  fun bindName(isShowCoin: Boolean = true): SpannableStringBuilder {
    // coin label for reward
    val coinSpan = getCoinSpan(item)
    // busy label for delivery for right
//        val busySpan = getBusySpan(item)
    val busySpan = null

    return if (busySpan != null) {
      // busy label show only when busy is true
      val spanText = SpannableStringBuilder(item.name?.localize() ?: "")
      spanText.append("  ")
      spanText.setSpan(busySpan, spanText.length - 1, spanText.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
      spanText
    } else if (coinSpan != null && isShowCoin) {
      val spanText = SpannableStringBuilder(item.name?.localize() ?: "")
      spanText.append(" ")
      spanText.setSpan(coinSpan, spanText.length - 1, spanText.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
      spanText
    } else {
      SpannableStringBuilder(item.name?.localize() ?: "")
    }
  }

  private fun getCoinSpan(item: Restaurant): ImageSpan? {
    if (item.reward?.enabled != true) return null
    val coinImage = getCoinDrawable(item.reward?.balance ?: 0)
    return if (coinImage != null) {
      val left = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_4dp)
      val right = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_44dp)
      val bottom = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_22dp)
      coinImage.setBounds(left, 0, right, bottom)
      CenteredImageSpan(coinImage, ImageSpan.ALIGN_BOTTOM)
    } else {
      null
    }
  }

  private fun getBusySpan(item: Restaurant): ImageSpan? {
    if (item.delivery?.busy != true) return null
    val busyImage = ResourcesUtil.getDrawable(BusyImage().localize(), context)
    return if (busyImage != null) {
      val right = DisplayUtil.density.times(30).toInt()
      val top = DisplayUtil.density.times(0).toInt()
      val bottom = DisplayUtil.density.times(18).toInt()
      busyImage.setBounds(0, top, right, bottom)
      CenterAlignImageSpan(busyImage, ImageSpan.ALIGN_CENTER)
    } else {
      null
    }
  }

  private fun getCoinDrawable(balance: Int): Drawable? {
    val points = balance / 100
    return when {
      points >= 10 -> {
        ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_coin_3, context)
      }
      points >= 5 -> {
        ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_coin_2, context)
      }
      points >= 1 -> {
        ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_coin_1, context)
      }
      points >= 0 -> {
        ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_coin_0, context)
      }
      else -> {
        null
      }
    }
  }

  fun bindRestaurantInfo(info: RestaurantInfo): SpannableStringBuilder {
    val drawable = when (info.type) {
      RestaurantInfo.TYPE_PROMOTION -> ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_promotion, context)
      RestaurantInfo.TYPE_VIP -> ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_crown, context)
      RestaurantInfo.TYPE_INFO -> ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_info, context)
      RestaurantInfo.TYPE_TIME -> ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_menu_clock, context)
      RestaurantInfo.TYPE_FEATURE -> ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_star, context)
      else -> null
    }
    val size = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_17sp)
    drawable?.setBounds(0, 0, size, size)

    val builder = SpannableStringBuilder("  ")
    builder.append(info.content?.localize())
    if (drawable != null) {
      val imageSpan = CenterAlignImageSpan(drawable)
      builder.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
    }
    return builder
  }

  /**
   * combine promotion and vip promotion
   */
  fun bindCombinePromotion(): SpannableStringBuilder? {
    val builder = SpannableStringBuilder()
    bindVipPromotion()?.let {
      builder.append(it)
      builder.append(" ")
    }
    bindPromotion()?.let {
      builder.append(it)
    }
    return builder
  }

  private fun bindImage(
    text: String?,
    icon: Drawable,
    textColor: ForegroundColorSpan,
    iconSize: Int = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_17sp)
  ): SpannableStringBuilder? {
    return if (text != null) {
      icon.setBounds(0, 0, iconSize, iconSize)
      val imageSpan = CenterAlignImageSpan(icon)
      val spanText = SpannableStringBuilder("  ")
      spanText.append(text)
      spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
      spanText.setSpan(textColor, 1, spanText.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
      spanText
    } else {
      null
    }
  }

  fun bindVipPromotion(): SpannableStringBuilder? {
    return bindImage(
      item.vipPromotion?.localize(),
      ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_crown, context),
      ForegroundColorSpan(ResourcesUtil.getColor(com.ricepo.style.R.color.goldSubText, context))
    )
  }

  private fun bindPromotion(): SpannableStringBuilder? {
    return bindImage(
      item.promotion?.localize(),
      ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_promotion, context),
      ForegroundColorSpan(ResourcesUtil.getColor(com.ricepo.style.R.color.mr, context))
    )
  }

  fun bindClosed(): Pair<SpannableStringBuilder?, Int> {
    // re calculate closed
    item.isClosed = RestaurantMapper().mapClosed(item)
    val closed = if (item.closed != null) item.closed?.localize() else item.isClosed
    val builder = if (closed != null) {
      SpannableStringBuilder(closed)
    } else {
      null
    }
    return Pair(builder, ResourcesUtil.getColor(com.ricepo.style.R.color.white, context))
  }

  fun bindTags() = getTags().joinToString(separator = " · ")

  private fun getTags(): List<String> {
    return item.tags?.filter {
      it.isNotBlank()
    }?.map {
      ResourcesUtil.getString("tags_$it")
    }?.filter {
      it.isNotBlank()
    }?.let {
      if (it.size <= 2) {
        it
      } else {
        it.take(2)
      }
    } ?: emptyList()
  }

  fun bindInfo(
    isShowDelivery: Boolean = true,
    isOnlyShowTag: Boolean = false,
    needShowTag: Boolean = true
  ): Pair<SpannableStringBuilder, Int> {
    var text = ""

    if (needShowTag) {
      getTags().forEach {
        text += it
        if (!isOnlyShowTag && text.isNotEmpty()) {
          text += " ⋅ "
        }
      }
    }

    var originalFee: String? = null
    if (isOnlyShowTag) {
      // pass
    } else {
      val mapper = RestaurantMapper()
      val free = mapper.computeFreeDelivery(item.delivery?.fees)

      var countryCode = "US"
      if (item.address is AddressObj) {
        val adr = item.address as AddressObj
        countryCode = adr.country ?: "US"
      }

      val minimum = mapper.formatPrice(
        item.delivery?.minimum ?: 0,
        countryCode,
        null
      ).let {
        "${ResourcesUtil.getString("delivery_min")}$it"
      }


      if (free != null) {
        val freeFee = mapper.formatPrice(
          free.delivery - (free.adjustments?.customer ?: 0),
          countryCode,
          null
        )
        // todo hide originalFee 2022-05-05
        // originalFee = mapper.formatPrice(free.delivery, countryCode, null)
        val freeMin = mapper.formatPrice(free.minimum.toInt(), countryCode, null)
        text += ResourcesUtil.getString("free_delivery", " $freeMin ", " $freeFee ")
        text = "$minimum ⋅ $text"
      } else {
        val flat = mapper.formatPrice(item.delivery?.fee?.flat ?: 0, countryCode, null)
        text += "${ResourcesUtil.getString("delivery_fee")}$flat ⋅ "
        text += minimum
      }
      getEstimate(isShowDelivery)?.let {
        text += " ⋅ $it"
      }
    }

    val spanText = SpannableStringBuilder(text.compressSpace())

    originalFee?.let {
      val strikethroughSpan = StrikethroughSpan()
      spanText.append(" $it")
      spanText.setSpan(
        strikethroughSpan,
        spanText.length - it.length,
        spanText.length,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
      )
    }

    // set the busy icon
    val busySpan = getBusySpan(item)
    busySpan?.let {
      spanText.append("  ")
      spanText.setSpan(busySpan, spanText.length - 1, spanText.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
    }

    if (item.delivery?.provider != null && isShowDelivery) {
      spanText.append("  ")
      val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_delivery)
      drawable.setBounds(0, -6, 80, 30)
      val imageSpan = CenterAlignImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
      spanText.setSpan(
        imageSpan,
        spanText.length - 1,
        spanText.length,
        Spannable.SPAN_INCLUSIVE_EXCLUSIVE
      )
    }

    return Pair(spanText, ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, context))
  }

  fun getEstimate(isShowDelivery: Boolean): String? {
    // horizontal item restaurant info don't show delivery estimate
    val estimate = item.delivery?.estimate
    if (estimate != null && isShowDelivery) {
      val estimateMin = estimate.min ?: 0.0
      val estimateMax = estimate.max ?: 0.0

      val sum = estimateMin + estimateMax
      val avg = (estimate.avg ?: (sum / 2)).toInt()

      val avgSix = if (avg > 60) "60+" else avg.toString()
      return "$avgSix${ResourcesUtil.getString("min")}"
    }
    return null
  }

  fun bindRating(
    tv: TextView
  ) {
    val text = item.customerRating?.let {
      val rating = it.score ?: 0.0
      val ratingText = if (rating > 0) {
        "$rating"
      } else {
        ""
      }
      val ratingCountText = if (
        !it.message.isNullOrBlank()
      ) {
        " (${it.message})"
      } else {
        ""
      }
      "$ratingText $ratingCountText"
    }
    tv.isVisible = !text.isNullOrEmpty()
    tv.setTextColor(tv.resources.getColor(com.ricepo.style.R.color.rating_text_color, null))
    tv.setCompoundDrawablesWithIntrinsicBounds(
      ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_star_small_select),
      null,
      null,
      null
    )
    tv.text = text
  }

  fun bindMotd(
    featureTint: Int,
    motdTint: Int,
    iconSize: Int = ResourcesUtil.getDimenPixelOffset(context?.resources, com.ricepo.style.R.dimen.sw_13dp)
  ): SpannedString {
    return buildSpannedString {
      bindImage(
        text = item.feature?.localize(),
        icon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_feature, context).apply {
          setTint(ResourcesUtil.getColor(featureTint))
        },
        textColor = ForegroundColorSpan(ResourcesUtil.getColor(featureTint, context)),
        iconSize = iconSize
      )?.let {
        append(
          it
        )
        append("  ")
      }
      bindImage(
        text = item.motd?.localize(),
        icon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_motd, context).apply {
          setTint(ResourcesUtil.getColor(motdTint))
        },
        textColor = ForegroundColorSpan(ResourcesUtil.getColor(motdTint, context)),
        iconSize = iconSize
      )?.let {
        append(
          it
        )
      }
    }
  }
}
