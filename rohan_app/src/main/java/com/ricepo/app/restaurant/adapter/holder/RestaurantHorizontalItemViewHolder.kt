package com.ricepo.app.restaurant.adapter.holder

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.RestaurantHorizontalItemBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Food
import com.ricepo.base.model.LastOrder
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.localize
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.CenterAlignImageSpan
import com.ricepo.style.view.placeVisible

class RestaurantHorizontalItemViewHolder(
  val binding: RestaurantHorizontalItemBinding,
  private val showTag: Boolean = true,
  private val clickItem: FunRestaurantHorizontalMenuClick?
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    binding.root.clickWithTrigger {
      navigateMenuForCheck(it)
    }
  }

  private fun navigateMenuForCheck(view: View, foodName: String? = null) {
    val data = view.tag
    if (data is Restaurant) {
      logRestaurantSelectedEvent(view, data, foodName)
      if (clickItem != null) {
        clickItem.invoke(
          RestaurantHorizontalUiModel
            .RestaurantItem(data, RestaurantRemoteGroup(), 0),
          foodName
        )
      } else {
        navigateMenu(data, view, foodName)
      }
    }
  }

  private fun navigateMenu(data: Restaurant, view: View, foodName: String?) {
    val searches = foodName?.let {
      arrayListOf(it)
    }
    FeaturePageRouter.navigateMenuForBusy(binding.root.context, data, searches)
  }

  private fun logRestaurantSelectedEvent(view: View, data: Restaurant, foodName: String?) {
    // firebase event select restaurant
    val event = view.getTag(com.ricepo.base.R.id.tag_firebase_event)
    if (event is FirebaseRestaurantEvent) {
      if (data.pool != null) {
        event.rPoolTime = SimpleDateUtils.toDateDiffSeconds(data.pool?.expiresAt)
      }
      foodName?.let {
        event.rOnFood = true
      }
    }

    view.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
    AnalyticsFacade.logEvent(view, FirebaseEventName.rSelectRestaurant)
  }

  fun bind(
    data: RestaurantHorizontalUiModel,
    position: Int,
    size: Int,
    isBackground: Boolean = true,
    isNotPickup: Boolean = true
  ) {
    val item = if (data is RestaurantHorizontalUiModel.RestaurantItem) {
      data.restaurant
    } else {
      return
    }

    // set margin right
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      // horizontal scroll aligned left margin
      var leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      var rightMargin = 0
      if (position == (size - 1)) {
        rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      } else if (position == 0) {
        leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      }
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params
    }

    val paddingLeft = if (isNotPickup) {
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_0dp)
    } else {
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_10dp)
    }
    binding.inRestaurantInfo.root.setPadding(paddingLeft, 0, 0, 0)

    setTag(binding.root, item, data, position)
    setTag(binding.ivLeftBg, item, data, position)
    setTag(binding.ivMiddleBg, item, data, position)

    val bindMapper = RestaurantBindMapper(item, binding.root.context)

    updateView(item.items, item)

    // restaurant name and max 1 line
    binding.inRestaurantInfo.tvRestaurantName.text = bindMapper.bindName(isNotPickup)
    binding.inRestaurantInfo.tvRestaurantName.maxLines = 1

    if (item.lastOrder != null) {
      // re-order section
      bindOrder(item.lastOrder)
    } else {
      bindRestaurantInfo(bindMapper, item)
    }

//    bindMapper.bindRating(binding.inRestaurantInfo.tvRating)

    // bind the closed status
    val pairClosed = bindMapper.bindClosed()
    bindRestaurantClosed(pairClosed)

    with(binding.tvFeatureMotd) {
      val bindMotd = bindMapper.bindMotd(
        featureTint = com.ricepo.style.R.color.rating_color,
        motdTint = com.ricepo.style.R.color.mr
      )
      placeVisible(bindMotd.isNotBlank())
      text = bindMotd
      isSelected = true
    }

    // set the default background
    if (isBackground) {
      binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
    } else {
      binding.root.background = null
    }
  }

  private fun setTag(
    view: View,
    item: Restaurant,
    data: RestaurantHorizontalUiModel.RestaurantItem,
    position: Int
  ) {
    view.tag = item
    view.setTag(
      com.ricepo.base.R.id.tag_firebase_event,
      FirebaseRestaurantEvent(
        rGroupId = data.group.groupId,
        rGroupType = data.group.type,
        rGroupIndex = data.groupIndex,
        rRestaurantIndex = position.toLong(),
      )
    )
  }

  private fun bindOrder(lastOrder: LastOrder?) {
    val order = lastOrder ?: return
    binding.inRestaurantInfo.tvRestaurantInfo.text = order.items?.localize()

    val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_clock, binding.root.context)
    drawable.setBounds(
      0, 0, DisplayUtil.dp2PxOffset(12f),
      DisplayUtil.dp2PxOffset(12f)
    )
    val imageSpan = CenterAlignImageSpan(drawable)
    val spanText = SpannableStringBuilder("  ")
    spanText.append(order.time?.localize())
    spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
    binding.inRestaurantInfo.tvRestaurantSubInfo.text = spanText
  }

  private fun bindRestaurantInfo(bindMapper: RestaurantBindMapper, item: Restaurant) {
    // restaurant info and promotion
    val pairInfo = bindMapper.bindInfo(isShowDelivery = false, needShowTag = false)
    binding.inRestaurantInfo.tvRestaurantInfo.text = pairInfo.first
    binding.inRestaurantInfo.tvRestaurantInfo.setTextColor(pairInfo.second)

//    val promotion = bindMapper.bindCombinePromotion()
//
//    // auto move up one line
//    if (binding.inRestaurantInfo.tvRestaurantInfo.text.isNullOrEmpty()) {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantInfo, promotion)
//    } else {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantSubInfo, promotion)
//    }

    // show the pool layout
    binding.rtvPool.placeVisible((item.pool != null))

    binding.inRestaurantInfo.tvRestaurantTags.isVisible = showTag
    binding.inRestaurantInfo.tvRestaurantTags.text = bindMapper.bindTags()

    // set the background for pool and normal
    if (item.pool != null) {
      // rice pool
      bindPool(item.pool)
    }
  }

  private fun bindRestaurantSubInfo(
    tvView: TextView,
    promotion: SpannableStringBuilder?
  ) {
    tvView.text = promotion
    tvView.setTextColor(
      ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, binding.root.context)
    )
  }

  private fun bindRestaurantClosed(pairClosed: Pair<SpannableStringBuilder?, Int>) {
    // fixed the restaurant name layout
    if (pairClosed.first.isNullOrEmpty()) {
      binding.inRestaurantClosed.tvRestaurantClosed.visibility = View.INVISIBLE
    } else {
      binding.inRestaurantClosed.tvRestaurantClosed.visibility = View.VISIBLE
    }

    pairClosed.first?.let {
      binding.inRestaurantClosed.tvRestaurantClosed.text = it
      binding.inRestaurantClosed.tvRestaurantClosed.setTextColor(pairClosed.second)
    }
  }

  private fun bindPool(pool: RestaurantPool?) {
    if (pool == null) {
      return
    }
    binding.rtvPool.setMessage(pool.message?.localize())
    binding.rtvPool.setExpireAt(pool.expiresAt)
  }

  private fun updateView(foods: List<Food>?, item: Restaurant) {

    val leftFood: Food? = foods?.getOrNull(0)
    val middleFood: Food? = foods?.getOrNull(1)

    binding.tvFoodNameLeft.text = leftFood?.name?.localize()
    binding.tvFoodNameMiddle.text = middleFood?.name?.localize()
    binding.ivLeftBg.clickWithTrigger {
      navigateMenuForCheck(it, leftFood?.name?.localize())
    }
    binding.ivMiddleBg.clickWithTrigger {
      navigateMenuForCheck(it, middleFood?.name?.localize())
    }

    RestViewUtils.setFoodViewSize(FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT, binding.ivLeft, binding.ivLeftBg)
    RestViewUtils.setFoodViewSize(FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT, binding.ivMiddle, binding.ivMiddleBg)
    RestViewUtils.setFoodImage(leftFood?.image, item, binding.ivLeft, binding.ivLeftBg)
    RestViewUtils.setFoodImage(middleFood?.image, item, binding.ivMiddle, binding.ivMiddleBg)
  }
}
