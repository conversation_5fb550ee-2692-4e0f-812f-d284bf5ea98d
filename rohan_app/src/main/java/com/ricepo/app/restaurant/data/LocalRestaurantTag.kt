package com.ricepo.app.restaurant.data

//
// Created by <PERSON><PERSON> on 22/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// todo replace this resource with rice when ready
// Tag Struct
data class LocalRestaurantTag(
  val background: String,
  val images: List<String>
)

val localRestaurantTags: Map<String, LocalRestaurantTag> = mapOf(
// 烧烤
  "bbq" to LocalRestaurantTag(
    background = "bbq",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuafw2u.png", // 牛肉串
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua3mal.png", // 烤鱼豆腐
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnb1ajyi5yiqm.png"
    )
  ), // 烤黄喉
// 火锅
  "hotpot" to LocalRestaurantTag(
    background = "hotpot",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9da5q.png", // 咕咚经典有锅套餐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9d4s4.png", // 咕咚特色虾滑
      "https://s3.amazonaws.com/ricepo-food/99a9f681-a02a-41f6-9367-e308d0882ed3"
    )
  ), // 安格斯牛肉
// 火锅
  "hotpot_a" to LocalRestaurantTag(
    background = "hotpot",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9da5q.png", // 咕咚经典有锅套餐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9d4s4.png", // 咕咚特色虾滑
      "https://s3.amazonaws.com/ricepo-food/99a9f681-a02a-41f6-9367-e308d0882ed3"
    )
  ), // 安格斯牛肉
// 麻辣香锅
  "mala_pot" to LocalRestaurantTag(
    background = "mala",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbmn.png",
      "https://s3.amazonaws.com/ricepo-food/image-foj231mkfjznmf3dn.png"
    )
  ), // 麻辣烫
  "mala_soup" to LocalRestaurantTag(
    background = "mala",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-o1bt3qsjz0fhek2.png", // 海鲜麻辣烫
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuagemz.png", // 肥牛麻辣烫
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9d0at.png"
    )
  ), // 台式麻辣锅
// 川菜
  "sichuan" to LocalRestaurantTag(
    background = "sichuan",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9bpe9.png", // 口水鸡
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6us.png", // 麻婆豆腐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu935r4.png"
    )
  ), // 重庆辣子鸡
// 川菜
  "sichuan_a" to LocalRestaurantTag(
    background = "sichuan",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9bpe9.png", // 口水鸡
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6us.png", // 麻婆豆腐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu935r4.png"
    )
  ), // 重庆辣子鸡
// 川菜
  "sichuan_b" to LocalRestaurantTag(
    background = "sichuan",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9bpe9.png", // 口水鸡
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6us.png", // 麻婆豆腐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu935r4.png"
    )
  ), // 重庆辣子鸡
// 湖南
  "hunan" to LocalRestaurantTag(
    background = "sichuan",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuavk9e.png", // 辣椒炒肉
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6us.png", // 麻婆豆腐
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuawag6.png"
    )
  ), // 辣炒白菜
// 便当
  "bento" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9kd8p.png", // 小龙虾盖饭
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua4tyc.png", // 照烧鸡肉盖饭
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6q5.png"
    )
  ), // 卤鸡腿盖饭
// 面
  "noodle" to LocalRestaurantTag(
    background = "noodle",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuadl8w.png", // 红烧牛肉面
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuayoma.png", // 酸辣粉
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9ygr5.png"
    )
  ), // 海鲜面
// 上海菜
  "shanghai" to LocalRestaurantTag(
    background = "shanghai",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/腌笃鲜.png",
      "https://s3.amazonaws.com/ricepo-food-images/蟹粉湯包.png",
      "https://s3.amazonaws.com/ricepo-food-images/饺子.png"
    )
  ), // 韩餐
  "korean" to LocalRestaurantTag(
    background = "korean",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/韩式拌饭.png",
      "https://s3.amazonaws.com/ricepo-food-images/海鲜面.png",
      "https://s3.amazonaws.com/ricepo-food-images/辣白菜豆腐炒饭.png"
    )
  ), // 寿司
  "sushi" to LocalRestaurantTag(
    background = "sushi",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/东部卷.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua557n.png", // 牛油果寿司
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub65yj.png"
    )
  ), // 卷
// 北京菜
  "beijing" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua1swz.png", // 炸酱面
      "https://s3.amazonaws.com/ricepo-food-images/酸辣汤.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu999xt.png"
    )
  ), // 北京烤鸭
// 家常菜
  "home" to LocalRestaurantTag(
    background = "home",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua7qhf.png", // 番茄炒蛋
      "https://s3.amazonaws.com/ricepo-food/image-935ein5k08e0cqu.png", // 咸蛋黄玉米粒
      "https://s3.amazonaws.com/ricepo-food/e7a0c0d3-62d9-4a3f-98e0-6b9f98c93977"
    )
  ), // 担担面
// 家常菜
  "home_a" to LocalRestaurantTag(
    background = "home",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua7qhf.png", // 番茄炒蛋
      "https://s3.amazonaws.com/ricepo-food/image-935ein5k08e0cqu.png", // 咸蛋黄玉米粒
      "https://s3.amazonaws.com/ricepo-food/e7a0c0d3-62d9-4a3f-98e0-6b9f98c93977"
    )
  ), // 担担面
// 家常菜
  "home_b" to LocalRestaurantTag(
    background = "home",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua7qhf.png", // 番茄炒蛋
      "https://s3.amazonaws.com/ricepo-food/image-935ein5k08e0cqu.png", // 咸蛋黄玉米粒
      "https://s3.amazonaws.com/ricepo-food/e7a0c0d3-62d9-4a3f-98e0-6b9f98c93977"
    )
  ), // 担担面
// 奶茶
  "cafe" to LocalRestaurantTag(
    background = "cafe",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-foj23mj0jzd8tac5.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbm8.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nblu.png"
    )
  ), // 东北菜
  "dongbei" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua6cm7.png",
      "https://s3.amazonaws.com/ricepo-food-images/韭菜盒子.png",
      "https://s3.amazonaws.com/ricepo-food-images/锅包肉.png"
    )
  ), // 泰国菜
  "thai" to LocalRestaurantTag(
    background = "thai",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/黄咖喱.png",
      "https://s3.amazonaws.com/ricepo-food-images/泰式特色虾球炒米粉.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9xebi.png"
    )
  ), // 冬阴功汤
// 越南菜
  "vietnamese" to LocalRestaurantTag(
    background = "vietnamese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/生牛腿肉牛腱百叶汤面-1.png",
      "https://s3.amazonaws.com/ricepo-food-images/泰式特色虾球炒米粉.png",
      "https://s3.amazonaws.com/ricepo-food-images/素春卷.png"
    )
  ), // 煎饼
  "crepe" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/煎饼果子.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnb85jyi7jdxp.png", // 火腿+午餐肉煎饼果子
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbld.png"
    )
  ), // 培根+午餐肉煎饼果子
// 点心
  "dim_sum" to LocalRestaurantTag(
    background = "cantonese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/水晶虾饺.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua40gq.png", // 烧卖
      "https://s3.amazonaws.com/ricepo-food-images/皮蛋瘦肉粥.png"
    )
  ), // 海鲜
  "seafood" to LocalRestaurantTag(
    background = "seafood",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/炸生蚝篮.png",
      "https://s3.amazonaws.com/ricepo-food-images/龙虾.png",
      "https://s3.amazonaws.com/ricepo-food-images/虾.png"
    )
  ), // 海鲜
  "seafood_a" to LocalRestaurantTag(
    background = "seafood",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/炸生蚝篮.png",
      "https://s3.amazonaws.com/ricepo-food-images/龙虾.png",
      "https://s3.amazonaws.com/ricepo-food-images/虾.png"
    )
  ), // 粤菜
  "cantonese" to LocalRestaurantTag(
    background = "cantonese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/牛肉炒河粉.png",
      "https://s3.amazonaws.com/ricepo-food-images/皮蛋瘦肉粥.png",
      "https://s3.amazonaws.com/ricepo-food-images/扬州炒饭.png"
    )
  ), // 台湾菜
  "taiwanese" to LocalRestaurantTag(
    background = "snack",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/炸鸡块.png",
      "https://s3.amazonaws.com/ricepo-food-images/照烧鸡肉盖饭.png",
      "https://s3.amazonaws.com/ricepo-food-images/章鱼小丸子.png"
    )
  ), // 甜点
  "dessert" to LocalRestaurantTag(
    background = "cafe",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-foj231k3pjzncl2qz.png",
      "https://s3.amazonaws.com/ricepo-food-images/提拉米苏.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbmr.png"
    )
  ), // 白雪黑糯米
// 铁板烧
  "teppanyaki" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-foj231mkfjznmf3eg.png", // 牛仔骨
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuazpa5.png", // 铁板w三文鱼
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuazrgr.png"
    )
  ), // 铁板牛
// 煲仔饭
  "casserole_rice" to LocalRestaurantTag(
    background = "cantonese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuagigb.png", // 腊味煲仔饭
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbn6.png", // 腊味黄鳝煲仔饭
      "https://s3.amazonaws.com/ricepo-food/image-foj23mj0jzd8tack.png"
    )
  ), // 烧鸭煲仔饭"
// 冒菜锅
  "take_food" to LocalRestaurantTag(
    background = "mala",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/冒菜锅.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu97xlx.png", // 冒菜肥牛
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub47hz.png"
    )
  ), // 海鲜冒菜
// 烧腊
  "siu_mei" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua4ajn.png", // 双拼饭
      "https://s3.amazonaws.com/ricepo-food/image-foj23mj0jzd8tacl.png", // 三拼饭
      "https://s3.amazonaws.com/ricepo-food-images/腊肉香肠饭.png"
    )
  ), // 卤味
  "braised_dishes" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9nll6.png", // 鸭头
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnb1ajyi5yisw.png", // 麻辣鸭翅
      "https://s3.amazonaws.com/ricepo-food-images/酱牛肉.png"
    )
  ), // 小吃
  "snack" to LocalRestaurantTag(
    background = "snack",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/炸鸡块.png",
      "https://s3.amazonaws.com/ricepo-food/image-935ein5k08e0cr0.png", // 洋葱圈
      "https://s3.amazonaws.com/ricepo-food-images/章鱼小丸子.png"
    )
  ), // 茶餐厅
  "hongkong_cafe" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/水晶虾饺.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua40gq.png", // 烧卖
      "https://s3.amazonaws.com/ricepo-food-images/皮蛋瘦肉粥.png"
    )
  ), // 创新菜
  "asian_fusion" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  ), // 福建菜
  "fujian" to LocalRestaurantTag(
    background = "cantonese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png",
      "https://s3.amazonaws.com/ricepo-food-images/牛肉炒河粉.png",
      "https://s3.amazonaws.com/ricepo-food-images/皮蛋瘦肉粥.png"
    )
  ), // 混沌
  "wonton" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/馄饨汤.png",
      "https://s3.amazonaws.com/ricepo-food/image-1k7h5tqjyp7e6r0.png", // 猪肉馄饨
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu973g8.png"
    )
  ), // 云吞汤面
// 粥
  "congee" to LocalRestaurantTag(
    background = "cantonese",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/皮蛋瘦肉粥.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9dzyg.png", // 咸鸡粥
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua7e6h.png"
    )
  ), // 生蚝粥
// 马来西亚
  "malaysia" to LocalRestaurantTag(
    background = "thai",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub9mku.png", // 黄咖喱
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu96h5d.png",
      "https://s3.amazonaws.com/ricepo-food/f936f18c-db2b-4e63-bdd9-2f64a9fdc857"
    )
  ), // 春卷
// 拉面
  "ramen" to LocalRestaurantTag(
    background = "sushi",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9d2aj.png", // 拉面
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9qgly.png", // 豚骨拉面
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbfi.png"
    )
  ), // 海鲜拉面
// 拉面
  "default" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  ), // 西安菜
  "xian" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  ), // 西北菜
  "xibei" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  ), // 天津菜
  "tianjin" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  ), // 江浙菜
  "jiangzhe" to LocalRestaurantTag(
    background = "shanghai",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/腌笃鲜.png",
      "https://s3.amazonaws.com/ricepo-food-images/蟹粉湯包.png",
      "https://s3.amazonaws.com/ricepo-food-images/饺子.png"
    )
  ), // 云贵菜
  "yungui" to LocalRestaurantTag(
    background = "home",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua7qhf.png", // 番茄炒蛋
      "https://s3.amazonaws.com/ricepo-food/image-935ein5k08e0cqu.png", // 咸蛋黄玉米粒
      "https://s3.amazonaws.com/ricepo-food/e7a0c0d3-62d9-4a3f-98e0-6b9f98c93977"
    )
  ), // 担担面
// 米粉
  "vermicelli" to LocalRestaurantTag(
    background = "noodle",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuadl8w.png", // 红烧牛肉面
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuayoma.png", // 酸辣粉
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvu9ygr5.png"
    )
  ), // 海鲜面
// Poke
  "poke" to LocalRestaurantTag(
    background = "sushi",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food-images/东部卷.png",
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua557n.png", // 牛油果寿司
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub65yj.png"
    )
  ), // 卷
// 面包
  "bakery" to LocalRestaurantTag(
    background = "cafe",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-foj23mj0jzd8tac5.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbm8.png",
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nblu.png"
    )
  ), // 烤鱼
  "grill_fish" to LocalRestaurantTag(
    background = "bbq",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvuafw2u.png", // 牛肉串
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvua3mal.png", // 烤鱼豆腐
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnb1ajyi5yiqm.png"
    )
  ), // 烤黄喉
// 清蒸菜
  "halal" to LocalRestaurantTag(
    background = "default",
    images = listOf(
      "https://s3.amazonaws.com/ricepo-food/image-5b7jxyy2jvub90ob.png", // 麻辣香锅
      "https://s3.amazonaws.com/ricepo-food/image-3r7hnd04jyk7nbls.png", // 干锅鸡
      "https://s3.amazonaws.com/ricepo-food-images/港式豉油王蒜香炒面.png"
    )
  )
)
