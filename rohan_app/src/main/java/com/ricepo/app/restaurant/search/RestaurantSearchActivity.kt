package com.ricepo.app.restaurant.search

import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityRestaurantSearchBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.model.RestaurantPrediction
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantAdapter
import com.ricepo.app.restaurant.search.adapter.SearchTagAdapter
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.consts.TabMode
import com.ricepo.base.databinding.ViewStringItemBinding
import com.ricepo.base.extension.searchAction
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.tag.TagCloudView
import com.ricepo.style.view.StateNestedScrollView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

//
// Created by Thomsen on 15/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_RESTAURANT_SEARCH)
class RestaurantSearchActivity : BaseActivity() {

  private lateinit var binding: ActivityRestaurantSearchBinding

  val viewModel: RestaurantSearchViewModel by viewModels()

  private var isSetKeyworkd = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityRestaurantSearchBinding.inflate(layoutInflater)
    setContentView(binding.root)
    setTitle(com.ricepo.style.R.string.search)

    viewModel.regionId = intent.getStringExtra(FeaturePageConst.PARAM_SEARCH_REGION_ID)
    setTagView(viewModel.regionId)

    val miles = intent.getIntExtra(FeaturePageConst.PARAM_SEARCH_MILES, -1)
    if (miles > 0) {
      viewModel.miles = miles
    }
    viewModel.location = intent.getStringExtra(FeaturePageConst.PARAM_SEARCH_LOCATION)

    setupListener()
  }

  private fun setupListener() {
    binding.etRestaurantSearch.searchAction { text ->
      if (text.isNotEmpty()) {
        searchRestaurant(text)
      }
    }

    binding.etRestaurantSearch.doAfterTextChanged {
      if (it.isNullOrEmpty()) {
        binding.recyclerRestaurantList.isVisible = false
        binding.tvSearchEmpty.isVisible = false
        binding.ivSearchTagBg.alpha = 1f
        binding.tclSearchTag.alpha = 1f
      } else {
        binding.ivSearchTagBg.alpha = 0f
        binding.tclSearchTag.alpha = 0f
      }
      searchPrediction(it.toString())
    }

    // recycler don't listener when recyclerview inside nestedscrollview
    binding.nsvRestaurantList.setOnScrollListener(object : StateNestedScrollView.OnScrollListener {

      /**
       * refresh closed status once time
       * otherwise affects sliding performance
       */
      private var isRefreshClosed = false

      override fun onScrollStateChanged(view: StateNestedScrollView?, scrollState: Int) {
        if (scrollState == StateNestedScrollView.OnScrollListener.SCROLL_STATE_IDLE) {
          isRefreshClosed = true
          setNestedPromotionMarquee(binding.recyclerRestaurantList)
        }
      }

      override fun onScroll(
        view: StateNestedScrollView?,
        isTouchScroll: Boolean,
        l: Int,
        t: Int,
        oldl: Int,
        oldt: Int
      ) {
        if (kotlin.math.abs(t - oldt) > 20) {
          isRefreshClosed = false
          setNestedPromotionMarquee(binding.recyclerRestaurantList)
        }
      }

      private fun setNestedPromotionMarquee(recyclerView: RecyclerView) {
        val scrollBounds = Rect()
        if (recyclerView?.adapter != null &&
          recyclerView.adapter?.itemCount != null &&
          recyclerView.adapter?.itemCount!! > 0
        ) {
          for (i in 0 until recyclerView.adapter?.itemCount!!) {
            var view = recyclerView?.findViewHolderForAdapterPosition(i)?.itemView
            val promotionView = view?.findViewById<TextView>(R.id.tv_restaurant_sub_info)

            if (view?.getLocalVisibleRect(scrollBounds) != true && isRefreshClosed) {
              // refresh the closed status
              binding.recyclerRestaurantList.adapter?.notifyItemChanged(i)
              isRefreshClosed = false
            }

            val isSelected = if (promotionView?.getLocalVisibleRect(scrollBounds) == true) {
              // bottom = 0 is leave from top
              // top <= cart bar height is leave from bottom
              // (scrollBounds.bottom < 0) || (scrollBounds.top < 0)
              scrollBounds.bottom >= 0 && scrollBounds.top >= 0
            } else {
              false
            }

            // promotion auto marquee
            promotionView?.let {
              it.isSelected = isSelected
              it.invalidate()
            }
          }
        }
      }
    })
  }

  /**
   * get restaurant prediction
   */
  private fun searchPrediction(keyword: String?) {
    if (isSetKeyworkd) {
      isSetKeyworkd = false
      return
    }

    binding.recyclerRestaurantList.isVisible = false
    // hide the prediction list
    binding.rvRestaurantPrediction.isVisible = false

    lifecycleScope.launch {
      viewModel.getRestaurantPrediction(this@RestaurantSearchActivity, keyword)
        .collect { datas ->
          // show restaurant predictions
          showRestaurantPredictionView(keyword, datas)
        }
    }
  }

  private fun searchRestaurant(keyword: String?) {
    // hide the restaurant prediction
    binding.rvRestaurantPrediction.isVisible = false

    lifecycleScope.launch {
      viewModel.getRestaurants(this@RestaurantSearchActivity, keyword)
        .collect { datas ->
          if (datas.isNullOrEmpty()) {
            // show empty view
            showEmptyView()
          } else {
            // show restaurant list view
            showRestaurantView(datas, keyword)
          }
        }
    }
  }

  private var predictionAdapter: BindListAdapter<View, RestaurantPrediction>? = null

  private fun showRestaurantPredictionView(keyword: String?, predictions: List<RestaurantPrediction>?) {
    binding.rvRestaurantPrediction.isVisible = (
      !predictions.isNullOrEmpty() &&
        (keyword?.isNotEmpty() == true || !binding.recyclerRestaurantList.isVisible)
      )
    if (!predictions.isNullOrEmpty()) {
      binding.tvSearchEmpty.isVisible = false
    }
    if (predictionAdapter == null) {
      predictionAdapter = BindListAdapter(
        predictions, com.ricepo.base.R.layout.view_string_item,
        object : OnBindViewListener<View, RestaurantPrediction> {
          override fun onBindView(view: View, value: RestaurantPrediction?, position: Int) {
            val predictionBinding = ViewStringItemBinding.bind(view)
            val keyword = value?.keyword
            val params = view.layoutParams
            if (params is RecyclerView.LayoutParams) {
              params.height = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_40)
            }
            predictionBinding.tvText.layoutParams = params
            predictionBinding.tvText.text = value?.keyword
            predictionBinding.tvText.maxLines = 1
            predictionBinding.tvText.ellipsize = TextUtils.TruncateAt.END
            predictionBinding.tvText.touchWithTrigger { _, event ->
              getRestaurant(keyword)
            }
          }
        }
      )
      binding.rvRestaurantPrediction.adapter = predictionAdapter
    } else {
      predictionAdapter?.dataSet = predictions
      predictionAdapter?.notifyDataSetChanged()
    }
  }

  private fun showRestaurantView(datas: List<RestaurantUiModel>?, keyword: String?) {
    binding.tvSearchEmpty.isVisible = false
    binding.recyclerRestaurantList.isVisible = true
    binding.rvRestaurantPrediction.isVisible = false
    binding.recyclerRestaurantList.apply {
      adapter = RestaurantAdapter(datas, this@RestaurantSearchActivity) { model, foodName ->
        if (model is RestaurantUiModel.RestaurantVertical) {
          val deliveryMode = if ((viewModel.miles ?: 0) > 0) {
            TabMode.MODE_PICKUP
          } else {
            null
          }
          // the keyword of food name and search restaurant
          var searches = foodName?.let {
            arrayListOf(it)
          }
          keyword?.let {
            searches = if (searches == null) {
              arrayListOf(it)
            } else {
              val builder = searches?.toMutableList()
              builder?.add(it)
              builder?.toCollection(ArrayList())
            }
          }
          FeaturePageRouter.navigateMenuForBusy(
            this@RestaurantSearchActivity,
            model.restaurant, searches,
            deliveryMode = deliveryMode
          )
        }
      }
    }
//        binding.recyclerRestaurantList.setHasFixedSize(true)
    binding.recyclerRestaurantList.isNestedScrollingEnabled = false

    binding.recyclerRestaurantList.post {
      PromotionMarqueeListener.setNestedPromotionMarquee(binding.recyclerRestaurantList)
    }
  }

  private fun showEmptyView() {
    binding.tvSearchEmpty.isVisible = true
    binding.recyclerRestaurantList.isVisible = false
  }

  /**
   * set search tag view
   */
  private fun setTagView(regionId: String?) {
    lifecycleScope.launchWhenCreated {
      viewModel.getSearchTags(regionId)
        .collect {
          // shot tag item from last to first
          val items = it.reversed()
          // set listener to add view
          binding.tclSearchTag.setOnTagClickListener(object : TagCloudView.OnTagClickListener {
            override fun onItemClick(parent: ViewGroup?, child: View?, position: Int) {
              if (binding.tclSearchTag.alpha == 0f) return
              val keyword = items.get(position).keyword
              getRestaurant(keyword)
            }
          })
          binding.ivSearchTagBg.isVisible = items.isNotEmpty()
          binding.tclSearchTag.isVisible = items.isNotEmpty()
          val adapter = SearchTagAdapter(items)
          binding.tclSearchTag.setAdapter(adapter)
        }
    }
  }

  private fun getRestaurant(keyword: String?) {
    isSetKeyworkd = true
    binding.etRestaurantSearch.setText(keyword)
    binding.etRestaurantSearch.setSelection(keyword?.length ?: 0)
    searchRestaurant(keyword)
  }
}
