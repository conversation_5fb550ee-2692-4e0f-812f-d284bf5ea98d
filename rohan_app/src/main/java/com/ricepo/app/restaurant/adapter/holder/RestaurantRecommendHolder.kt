package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.RestaurantItemRecommendBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.recommend.RecommendType
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize

//
// Created by <PERSON><PERSON> on 14/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantRecommendHolder(private val binding: RestaurantItemRecommendBinding) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    binding.tvRecommendWeekClick.clickWithTrigger {
      // recommend food weekly
      CustomerCache.liveCustomer { customer ->
        if (!customer?.id.isNullOrEmpty()) {
          FeaturePageRouter.navigateRecommend(RecommendType.TYPE_RECOMMEND)
        } else {
          FeaturePageRouter.navigateLogin()
        }
      }
    }

    binding.tvRecommendTopClick.clickWithTrigger {
      // recommend food with rank top
      FeaturePageRouter.navigateRecommend(RecommendType.TYPE_RANK)
    }
  }

  fun bind(uiModel: RestaurantUiModel.RestaurantRecommend) {
    val recommend = uiModel.recommend
    val top = uiModel.top

    binding.tvRecommendWeek.text = recommend?.name?.localize()
    binding.tvRecommendTop.text = top?.name?.localize()

    val recommendImgLeft = recommend?.images?.getOrNull(0)
    val recommendImgRight = recommend?.images?.getOrNull(1)

    binding.ivRecommendDishWeekLeft.isVisible = (recommendImgLeft != null)
    binding.ivRecommendDishWeekRight.isVisible = (recommendImgRight != null)

    if (recommendImgLeft != null) {
      ImageLoader.load(binding.ivRecommendWeekLeft, recommendImgLeft)
    } else {
      ImageLoader.load(binding.ivRecommendWeekLeft, com.ricepo.style.R.drawable.dish_placeholder)
    }
    if (recommendImgRight != null) {
      ImageLoader.load(binding.ivRecommendWeekRight, recommendImgRight)
    } else {
      ImageLoader.load(binding.ivRecommendWeekRight, com.ricepo.style.R.drawable.dish_placeholder)
    }

    val topImgLeft = top?.images?.getOrNull(0)
    val topImgRight = top?.images?.getOrNull(1)

    binding.ivRecommendDishTopLeft.isVisible = (topImgLeft != null)
    binding.ivRecommendDishTopRight.isVisible = (topImgRight != null)

    if (topImgLeft != null) {
      ImageLoader.load(binding.ivRecommendTopLeft, topImgLeft)
    } else {
      ImageLoader.load(binding.ivRecommendTopLeft, com.ricepo.style.R.drawable.dish_placeholder)
    }

    if (topImgRight != null) {
      ImageLoader.load(binding.ivRecommendTopRight, topImgRight)
    } else {
      ImageLoader.load(binding.ivRecommendTopRight, com.ricepo.style.R.drawable.dish_placeholder)
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantRecommendHolder {
      val binding = RestaurantItemRecommendBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return RestaurantRecommendHolder(binding)
    }
  }
}
