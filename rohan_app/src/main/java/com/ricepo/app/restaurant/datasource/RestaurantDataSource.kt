package com.ricepo.app.restaurant.datasource

import androidx.paging.PagingSource
import com.ricepo.app.R
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.CategoryJumpItem
import com.ricepo.base.model.LuckRecommendBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.base.model.RestaurantGroupType
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.localize
import com.ricepo.monitor.firebase.FirebaseBaseEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 10/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantDataSource constructor(
  private val repository: CombineRestApi,
  private val mapper: RestaurantMapper,
  private val request: RestaurantRequest,
  private var lastModels: MutableList<RestaurantUiModel> = mutableListOf(),
  private var sortModel: RestaurantUiModel? = null
) :
  PagingSource<Int, RestaurantUiModel>() {

  private val PAGE_INDEX_STARTING = 1

  // cache key to cursor
  private var cacheCursor = mutableMapOf<Int, String?>()
  // cache key to groupId
  private var cacheGroupId = mutableMapOf<Int, String?>()
  // cache the paging restaurant size
  private var restaurantsLastSize = 0

  override val keyReuseSupported: Boolean
    get() = true

  override suspend fun load(loadParams: LoadParams<Int>): LoadResult<Int, RestaurantUiModel> {

    val page = loadParams.key ?: PAGE_INDEX_STARTING

    val isFirstLoad = (page == PAGE_INDEX_STARTING && loadParams is LoadParams.Refresh<*>)
    if (isFirstLoad) {
      cacheCursor.clear()
      cacheGroupId.clear()
      restaurantsLastSize = 0
    }

    // default get cache address
    val location = request.location ?: withContext(Dispatchers.Default) {
      AddressCache.getAddressSuspend()?.location?.coordinates?.joinToString(",")
    }

    val requestParams = mutableMapOf("location" to location)

    // cuisine tag param
    request.cuisine?.tag?.let {
      requestParams.put("cuisine", it)
    }

    // restaurant group id param
    val groupId = cacheGroupId[page] ?: request.groupId
    groupId?.let {
      requestParams.put("groupId", it)
    }

    // restaurant group cursor param
    val cursor = cacheCursor[page]
    cursor?.let {
      requestParams.put("cursor", it)
    }

    // request tab type
    request.type?.let {
      requestParams.put("type", it)
    }

    // request by sort
    request.sortData?.sort?.id?.let {
      requestParams.put("sortBy", it)
    }

    try {

      val models = mutableListOf<RestaurantUiModel>()
      if (lastModels.isNotEmpty() && cursor == null) {
        models.addAll(lastModels)
      }

      val restaurantGroups =
        if (isFirstLoad && !request.restaurants.isNullOrEmpty()) {
          // init the restaurants from page transform
          listOf(RestaurantRemoteGroup(restaurants = request.restaurants))
        } else {
          val remoteSection = repository.getRestaurantSection(requestParams)

          if (!remoteSection?.tabs.isNullOrEmpty()) {
            models.add(RestaurantUiModel.RestaurantTabSection(remoteSection?.tabs))
          }

          remoteSection?.sections
        }
      // get the restaurant region
      if (isFirstLoad && request.isRegion) {
        var regionId: String? = null
        restaurantGroups?.forEach { group ->
          if (!regionId.isNullOrEmpty()) {
            return@forEach
          }
          group.restaurants?.takeIf { it.isNotEmpty() }?.let {
            regionId = it.get(0).region?.id
          }
        }
      }

      if (isFirstLoad) {
        // construct cuisine to ui model
        request.cuisine?.let {
          models.add(RestaurantUiModel.RestaurantCuisineTag(it))
        }

        // group secondary category ui model
        request.groupName?.let {
          models.add(
            RestaurantUiModel.RestaurantCategory(
              RestaurantGroup(
                name = it, image = request.groupImage,
                description = request.groupDesc
              ),
              0, null, null, true
            )
          )
        }
      }

      // construct the restaurant group ui model
      constructGroupModel(restaurantGroups, models, isFirstLoad, request.restaurants, sortModel)

      val lastIndex = restaurantGroups?.lastIndex ?: 0
      val lastGroup = restaurantGroups?.getOrNull(lastIndex)
      // transform restaurants use request cursor
      val lastCursor = lastGroup?.cursor ?: (if (isFirstLoad) request.cursor else null)
      val prevKey = if (page == PAGE_INDEX_STARTING) null else page - 1
      val nextKey = if (lastCursor.isNullOrEmpty()) {
        // last page add bottom empty placeholder
        models.add(
          RestaurantUiModel.RestaurantEmptyPlace(
            ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_100)
          )
        )
        null
      } else {
        val nextPage = page + 1
        cacheCursor.put(nextPage, lastCursor)
        cacheGroupId.put(nextPage, lastGroup?.groupId)
        nextPage
      }

      // fliter without empty place model
      if (isFirstLoad && models.filterNot {
        it is RestaurantUiModel.RestaurantEmptyPlace
      }.isEmpty()
      ) {
        throw NoSuchElementException()
      }

      logPoolEvent(models)

      return LoadResult.Page(
        data = models,
        prevKey = prevKey,
        nextKey = nextKey
      )
    } catch (e: Exception) {
      e.printStackTrace()
      val models = lastModels.filterNot { it is RestaurantUiModel.RestaurantEmptyPlace }.toMutableList()
      return if (page > PAGE_INDEX_STARTING) {
        // show progress bar with load more footer
        Thread.sleep(300)
        LoadResult.Page(
          data = listOf<RestaurantUiModel>(RestaurantUiModel.RestaurantLoadMoreError(page)),
          prevKey = page - 1,
          nextKey = cacheCursor.size + 1
        )
      } else if (models.isNotEmpty()) {
        val position = models.size
        // new model to refresh
        if (sortModel is RestaurantUiModel.RestaurantCategory) {
          val model = sortModel as RestaurantUiModel.RestaurantCategory
          val newSortModel = RestaurantUiModel.RestaurantCategory(
            group = model.group,
            position = model.position,
            jumpGroups = model.jumpGroups,
            sorts = model.sorts,
            sorted = model.sorted,
            models = model.models
          )
          models.add(newSortModel)
        }

        // add error page
        models.add(
          RestaurantUiModel.RestaurantErrorSection(
            request = request,
            lastModels = lastModels,
            groupModel = sortModel,
            position = position,
            errorMessage = e.parseByBuzNetwork().message
          )
        )
        LoadResult.Page(
          data = models,
          prevKey = null,
          nextKey = null
        )
      } else {
        loadResultError(e)
      }
    }
  }

  /**
   * log pool section view firebase event
   */
  private fun logPoolEvent(models: MutableList<RestaurantUiModel>) {
    val isPool = models.any {
      when (it) {
        is RestaurantUiModel.RestaurantVertical -> {
          it.restaurant.pool != null
        }
        is RestaurantUiModel.RestaurantHorizontal -> {
          it.restaurants.any { it.pool != null }
        }
        else -> {
          false
        }
      }
    }
    if (isPool) {
      AnalyticsFacade.logEvent(FirebaseBaseEvent(), FirebaseEventName.rPoolView)
    }
  }

  /**
   * construct restaurant group
   */
  private fun constructGroupModel(
    restaurantGroups: List<RestaurantRemoteGroup>?,
    models: MutableList<RestaurantUiModel>,
    isFirstLoad: Boolean,
    lastRestaurants: List<Restaurant>?,
    restModel: RestaurantUiModel?,
  ) {
    val filterGroups = restaurantGroups?.filter {
      if (it.type == RestaurantGroupType.horizontal ||
        it.type == RestaurantGroupType.vertical
      ) {
        it.restaurants?.isNotEmpty() == true
      } else {
        true
      }
    }
    constructJumpGroups(models, filterGroups)
    filterGroups?.forEachIndexed { groupIndex, restaurantGroup ->
      when (restaurantGroup.type) {
        RestaurantGroupType.horizontal -> {
          if (lastRestaurants == null) {
            constructRestaurantHorizontal(models, restaurantGroup, groupIndex, isFirstLoad)
          } else {
            constructRestaurants(models, restaurantGroup, groupIndex)
          }
        }
        RestaurantGroupType.vertical -> {
          constructRestaurantVertical(models, restaurantGroup, groupIndex, isFirstLoad, restModel)
          if (groupIndex != ((filterGroups?.size ?: 0) - 1) &&
            restaurantGroup.hasNextPage == true && isFirstLoad
          ) {
            // show more button
            models.add(
              RestaurantUiModel.RestaurantVerticalMore(
                restaurantGroup, groupIndex = groupIndex.toLong()
              )
            )
          }
        }
        RestaurantGroupType.plan -> {
          constructGroupCategory(models, restaurantGroup, groupIndex)
          restaurantGroup.data?.plan?.let {
            models.add(RestaurantUiModel.RestaurantPlan(it))
          }
        }
        RestaurantGroupType.cuisine -> {
          constructGroupCategory(models, restaurantGroup, groupIndex)
          restaurantGroup.data?.cuisines?.let {
            models.add(RestaurantUiModel.RestaurantCuisine(it))
          }
        }
        RestaurantGroupType.recommend -> {
          constructGroupCategory(models, restaurantGroup, groupIndex)
          val recommend = restaurantGroup.data?.recommend
          val top = restaurantGroup.data?.top
          if (recommend != null && top != null) {
            models.add(RestaurantUiModel.RestaurantRecommend(recommend, top))
          }
        }
        RestaurantGroupType.lucky -> {
          constructGroupCategory(models, restaurantGroup, groupIndex)
          val name = restaurantGroup.data?.name?.localize()
          val button = restaurantGroup.data?.button?.localize()
          if (name != null && button != null) {
            models.add(RestaurantUiModel.RestaurantLucky(name, button))
          }
        }
        RestaurantGroupType.dishFood -> {
          val items = restaurantGroup.data?.items
          if (items?.isNotEmpty() == true) {
            constructGroupCategory(models, restaurantGroup, groupIndex)
            models.add(
              RestaurantUiModel.RestaurantDish(
                items, restaurantGroup,
                groupIndex.toLong()
              )
            )
          }
        }
        RestaurantGroupType.cascade -> {
          val items = restaurantGroup.data?.items
          if (items?.isNotEmpty() == true) {
            models.add(
              RestaurantUiModel.RestaurantCascade(
                items, restaurantGroup,
                groupIndex.toLong()
              )
            )
          }
        }
        RestaurantGroupType.lucky_v2 -> {
          val name = restaurantGroup.name?.localize()
          if (name != null) {
            // filter size > 1
            val datas = restaurantGroup.restaurants?.map {
              LuckRecommendBean(restaurant = it, items = it.items ?: listOf())
            }?.filter { it.items.size > 1 }?.reversed()
            models.add(RestaurantUiModel.RestaurantLuckyCombine(name, cards = datas))
          }
        }
        RestaurantGroupType.motd -> {
          if (restaurantGroup.data?.motd != null) {
            models.add(RestaurantUiModel.RestaurantMotd(restaurantGroup.data?.motd))
          }
        }
        RestaurantGroupType.banner -> {
          if (!restaurantGroup.data?.banners.isNullOrEmpty()) {
            // add banner ui model with current update time
            models.add(
              RestaurantUiModel.RestaurantBanner(
                restaurantGroup.data?.banners,
                System.currentTimeMillis()
              )
            )
          }
        }
        else -> {
          // default restaurants [cuisine or secondary group]
          constructRestaurantVertical(models, restaurantGroup, groupIndex, isFirstLoad, restModel)
        }
      }
    }
  }

  private fun constructJumpGroups(
    models: MutableList<RestaurantUiModel>,
    filterGroups: List<RestaurantRemoteGroup>?
  ) {
    var position = models.size
    jumpGroups = filterGroups?.mapIndexed { _, group ->
      val jumpGroup = RestaurantGroup(
        name = group.name,
        type = group.type,
        index = position,
        groupId = group.groupId,
        jump = group.jump,
        titleStyle = group.titleStyle
      )
      if (group.name != null && group.type != RestaurantGroupType.horizontal) {
        position += 1
      }
      when (group.type) {
        RestaurantGroupType.horizontal -> {
          if (group.name != null && group.restaurants?.isNotEmpty() == true) {
            position += 2
            setJumpData(group, jumpGroup)
          }
        }
        RestaurantGroupType.vertical -> {
          if (group.restaurants != null) {
            position += (group.restaurants?.size ?: 0)
            setJumpData(group, jumpGroup)
          }
          if (group.hasNextPage == true) {
            position += 1
          }
        }
        RestaurantGroupType.plan -> {
          if (group.data?.plan != null) {
            position += 1
          }
        }
        RestaurantGroupType.cuisine -> {
          if (group.data?.cuisines != null) {
            position += 1
          }
        }
        RestaurantGroupType.recommend -> {
          val recommend = group.data?.recommend
          val top = group.data?.top
          if (recommend != null && top != null) {
            position += 1
          }
        }
        RestaurantGroupType.lucky_v2 -> {
          if (group.name != null) {
            position + 1
          }
        }
        RestaurantGroupType.lucky -> {
          val name = group.data?.name?.localize()
          val button = group.data?.button?.localize()
          if (name != null && button != null) {
            position += 1
          }
        }
        RestaurantGroupType.dishFood -> {
          val items = group.data?.items
          if (items?.isNotEmpty() == true) {
            position += 1
            val items = group.data?.items?.take(2)?.map {
              CategoryJumpItem(it.name?.localize(), it.image?.url)
            }
            jumpGroup.jumpData = CategoryJumpData(
              items = items
            )
          }
        }
        RestaurantGroupType.motd -> {
          if (group.data?.motd != null) {
            position += 1
          }
        }
        RestaurantGroupType.banner -> {
          if (!group.data?.banners.isNullOrEmpty()) {
            position += 1
          }
        }
        else -> {
          if (group.restaurants?.isNotEmpty() == true) {
            position += (group.restaurants?.size ?: 0)
            setJumpData(group, jumpGroup)
          }
        }
      }
      jumpGroup
    }
  }

  private fun setJumpData(
    group: RestaurantRemoteGroup,
    jumpGroup: RestaurantGroup
  ) {
    val items = group.restaurants?.take(2)?.map {
      CategoryJumpItem(it.name?.localize(), it.items?.firstOrNull()?.image?.url)
    }
    jumpGroup.jumpData = CategoryJumpData(
      items = items
    )
  }

  private fun constructRestaurantHorizontal(
    models: MutableList<RestaurantUiModel>,
    restaurantGroup: RestaurantRemoteGroup,
    groupIndex: Int,
    isFirstLoad: Boolean
  ) {
    constructGroupCategory(models, restaurantGroup, groupIndex, isFirstLoad, true)
    if (restaurantGroup.restaurants?.isNotEmpty() == false) return
    restaurantGroup.restaurants?.let {
      // put map closed in RestaurantBindMapper
      models.add(
        RestaurantUiModel.RestaurantHorizontal(
          it, restaurantGroup,
          groupIndex.toLong()
        )
      )
    }
  }

  private fun constructRestaurantVertical(
    models: MutableList<RestaurantUiModel>,
    restaurantGroup: RestaurantRemoteGroup,
    groupIndex: Int,
    isFirstLoad: Boolean,
    restModel: RestaurantUiModel?,
  ) {
    constructGroupCategory(models, restaurantGroup, groupIndex, isFirstLoad, true, restModel)
    constructRestaurants(models, restaurantGroup, groupIndex)
  }

  private fun constructRestaurants(
    models: MutableList<RestaurantUiModel>,
    remoteGroup: RestaurantRemoteGroup,
    groupIndex: Int
  ) {
    val restaurants = remoteGroup.restaurants
    if (restaurants?.isNotEmpty() == false) return
    restaurants?.let {
      val its = it.map {
        it.isClosed = mapper.mapClosed(it)
        it
      }
      its.forEachIndexed() { index, rest ->
        val position = restaurantsLastSize + index
        models.add(
          RestaurantUiModel.RestaurantVertical(
            rest, position, its.size,
            group = RestaurantGroup(
              name = remoteGroup.name,
              groupId = remoteGroup.groupId,
              type = remoteGroup.type,
              titleStyle = remoteGroup.titleStyle,
              index = groupIndex
            )
          )
        )
      }
      restaurantsLastSize += its.size
    }
  }

  private var jumpGroups: List<RestaurantGroup>? = null

  /**
   * construct restaurant group category name
   */
  private fun constructGroupCategory(
    models: MutableList<RestaurantUiModel>,
    restaurantGroup: RestaurantRemoteGroup,
    groupIndex: Int,
    isFirstLoad: Boolean = true,
    isNeedRestaurant: Boolean = false,
    restModel: RestaurantUiModel? = null,
  ) {
    // clear restaurant last size position
    val groupName = restaurantGroup.name ?: return
    // horizontal and vertical filter restaurants to add group name
    if (isNeedRestaurant && restaurantGroup.restaurants?.isNotEmpty() == false) return
    if (!isFirstLoad) return
    restaurantsLastSize = 0

    val lastModels = mutableListOf<RestaurantUiModel>()
    lastModels.addAll(models)
    restaurantGroup?.sorts?.map {
      it.position = models.size
    }
    val model = if (restModel != null && restModel is RestaurantUiModel.RestaurantCategory) {
      RestaurantUiModel.RestaurantCategory(
        restModel.group,
        jumpGroups = restModel.jumpGroups,
        groupIndex = groupIndex,
        position = models.size,
        sorts = restaurantGroup.sorts,
      )
    } else {
      RestaurantUiModel.RestaurantCategory(
        RestaurantGroup(
          name = groupName,
          image = restaurantGroup.image,
          description = restaurantGroup.description,
          jump = restaurantGroup.jump,
          groupId = restaurantGroup.groupId,
          titleStyle = restaurantGroup.titleStyle,
        ),
        jumpGroups = jumpGroups,
        groupIndex = groupIndex,
        position = models.size,
        sorts = restaurantGroup.sorts,
      )
    }
    if (restaurantGroup.sorts != null) {
      model.models = lastModels
    }
    models.add(model)
  }
}
