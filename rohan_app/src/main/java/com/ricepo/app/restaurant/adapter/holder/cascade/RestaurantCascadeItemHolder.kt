package com.ricepo.app.restaurant.adapter.holder.cascade

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Paint
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantCascadeItemBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantDishItem
import com.ricepo.base.model.localize
import com.ricepo.monitor.firebase.FirebaseCascadeEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.extend.AutoPollRecyclerView

//
// Created by Thomsen on 2021/9/14.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

class RestaurantCascadeItemHolder(val binding: RestaurantCascadeItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  val mapper = RestaurantMapper()

  fun navigateMenu() {
    val event = binding.root.getTag(com.ricepo.base.R.id.tag_firebase_event)
    if (event is FirebaseCascadeEvent) {
      event.rScrollDepth = AutoPollRecyclerView.scrollHoriDepth
      AnalyticsFacade.logEvent(event, FirebaseEventName.rCascadeSelect)
    }
    val data = binding.root.tag
    navigateMenu(data, binding.root)
  }

  private fun navigateMenu(data: Any?, view: View) {
    if (data is RestaurantDishItem) {
      data.restaurant?.let {
        val searches = data.name?.localize()?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenuForBusy(binding.root.context, it, searches)
      }
    }
  }

  fun transitionPreview() {
    val data = binding.root.tag ?: return
    val context = binding.root.context ?: return
    if (data is RestaurantDishItem) {
      data.image?.let {
        transitionToNormal(context, Food(image = it), data.restaurant, binding.root)
      }
    }
  }

  private fun transitionToNormal(
    context: Context,
    food: Food,
    restaurant: Restaurant?,
    view: View
  ) {
    val intent = Intent(context, MenuNormalPreviewActivity::class.java)

    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    if (context is Activity) {
      val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
        context,
        Pair(
          view.findViewById<ImageView>(R.id.iv_food),
          MenuNormalPreviewActivity.VIEW_MENU_IMAGE
        ),
        Pair(
          view.findViewById<ImageView>(R.id.iv_food_bg),
          MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
        )
      )
      ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
    }
  }

  fun bind(data: RestaurantCascadeUiModel, position: Int, size: Int) {
    val item = if (data is RestaurantCascadeUiModel.CascadeItem) {
      data.dish
    } else return

    setItemParams(position, size)

    binding.root.tag = item
    binding.root.setTag(
      com.ricepo.base.R.id.tag_firebase_event,
      FirebaseCascadeEvent(
        rPrice = data.dish.price
      )
    )

    binding.ivItemBg.setImageResource(0)
    ImageLoader.load(binding.ivItemBg, item.background?.localize())

    RestViewUtils.setFoodImage(item.image, item.restaurant, binding.ivFood, binding.ivFoodBg)

    // dish food name
    val foodName = item.name?.localize()
    binding.tvFoodName.text = foodName

    // restaurant name
    binding.tvFoodRestaurant.text = item.restaurant?.name?.localize()

    // food price
    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(
      item.price ?: 0, item.restaurant
    )
    binding.tvFoodPrice.setTextColor(
      ResourcesUtil.getColor(
        com.ricepo.style.R.color.subText, binding.root.context
      )
    )

    // food original price
    binding.tvFoodOriginalPrice.isVisible = (item.originalPrice != null)
    binding.tvFoodOriginalPrice.text = null

    item.originalPrice?.let {
      binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
        Paint.ANTI_ALIAS_FLAG
      binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, item.restaurant)
      binding.tvFoodOriginalPrice.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.deliveryNoteText, binding.root.context
        )
      )
      binding.tvFoodOriginalPrice.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_9sp)
      )

      binding.tvFoodPrice.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.green, binding.root.context
        )
      )
    }
  }

  private fun setItemParams(position: Int, size: Int) {
    // root params
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      var width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_270dp)
      val leftMargin = if (position < 2) {
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
      } else {
        0
      }
      width += leftMargin
      params.width = width
      params.leftMargin = leftMargin
      binding.root.layoutParams = params
    }

    // cascade item params
    val itemParams = binding.clCascadeItem.layoutParams
    if (itemParams is ConstraintLayout.LayoutParams) {
      var topMargin = 0
      var bottomMargin = 0

      if (position % 2 == 0) {
        // the top column
        // 428 9.5%
        topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_40dp)
        // the bottom will crop 184 - 155 - 40
        bottomMargin = - ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_15dp)
        when {
          position == 0 -> {
            // the first of top
          }
          position >= (size - 2) -> {
          }
        }
      } else {
        // the bottom column
        // 1.5%
        topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_4dp)
        when {
          position == 1 -> {
            // the first of bottom
          }
          position >= (size - 2) -> {
          }
        }
      }
      itemParams.topMargin = topMargin
//            itemParams.bottomMargin = bottomMargin

      binding.clCascadeItem.layoutParams = itemParams
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantCascadeItemHolder {
      val binding = RestaurantCascadeItemBinding.inflate(LayoutInflater.from(parent.context))
      // width 63% height 50%
      val width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_270dp)
      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_184dp)
      val params = RecyclerView.LayoutParams(width, height)
      binding.root.layoutParams = params

      return RestaurantCascadeItemHolder(binding)
    }
  }
}
