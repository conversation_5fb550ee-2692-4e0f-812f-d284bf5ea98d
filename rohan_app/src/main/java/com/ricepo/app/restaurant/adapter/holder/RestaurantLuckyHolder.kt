package com.ricepo.app.restaurant.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantItemLuckyBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.luckymenu.view.FrameAnimation
import com.ricepo.app.features.luckymenu.view.FrameAnimationUtils
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON> on 10/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantLuckyHolder(val binding: RestaurantItemLuckyBinding) :
  RecyclerView.ViewHolder(binding.root) {

  private var animation: FrameAnimation? = null

  fun bind(uiModel: RestaurantUiModel.RestaurantLucky) {
    binding.tvFeelingLucky.text = uiModel.name
    binding.tvStartNow.text = uiModel.button
    val ivDice = binding.root.findViewById<ImageView>(R.id.iv_dice)
    animation?.let {
      it.release()
    }
    animation = null
    animation = FrameAnimation(
      ivDice,
      FrameAnimationUtils.getRes(), 20, true
    )
    animation?.play(0)
    binding.root.clickWithTrigger {
      FeaturePageRouter.navigateLuckyMenu()
    }
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantLuckyHolder {
      val binding = RestaurantItemLuckyBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      var params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      binding.root.layoutParams = params

      return RestaurantLuckyHolder(binding)
    }
  }
}
