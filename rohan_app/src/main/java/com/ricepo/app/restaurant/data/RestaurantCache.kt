package com.ricepo.app.restaurant.data

import androidx.security.crypto.EncryptedFile
import androidx.security.crypto.MasterKeys
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.BaseApplication
import com.ricepo.base.model.Restaurant
import java.io.File
import java.lang.Exception

object RestaurantCache {

  // the file directory
  private val directory = BaseApplication.context.filesDir.path

  // Creates a file with this name, or replaces an existing file
  // that has the same name. Note that the file name cannot contain
  // path separators.
  private const val fileName = "restaurant_data.txt"

  fun writeRestaurant(restaurant: Restaurant) {
    if (restaurant == null) {
      return
    }

    // Although you can define your own key generation parameter specification, it's
    // recommended that you use the value specified here.
    val keyGenParameterSpec = MasterKeys.AES256_GCM_SPEC
    val masterKeyAlias = MasterKeys.getOrCreate(keyGenParameterSpec)

    val directory = BaseApplication.context.filesDir.path

    // override the file content
    val file = File(directory, fileName)
    if (file.exists()) {
      file.delete()
    }

    val encryptedFile = EncryptedFile.Builder(
      file,
      BaseApplication.context,
      masterKeyAlias,
      EncryptedFile.FileEncryptionScheme.AES256_GCM_HKDF_4KB
    ).build()

    val content = ParserModelFacade.toJson(restaurant)

    encryptedFile.openFileOutput().bufferedWriter().use {
      it.write(content)
    }
  }

  fun readRestaurant(): Restaurant? {
    // Although you can define your own key generation parameter specification, it's
    // recommended that you use the value specified here.
    val keyGenParameterSpec = MasterKeys.AES256_GCM_SPEC
    val masterKeyAlias = MasterKeys.getOrCreate(keyGenParameterSpec)

    val directory = BaseApplication.context.filesDir.path

    val encryptedFile = EncryptedFile.Builder(
      File(directory, fileName),
      BaseApplication.context,
      masterKeyAlias,
      EncryptedFile.FileEncryptionScheme.AES256_GCM_HKDF_4KB
    ).build()

    val contents = encryptedFile.openFileInput()
      .bufferedReader().useLines { lines ->
        lines.fold("") { working, line ->
          "$working\n$line"
        }
      }

    var restaurant: Restaurant? = null
    if (contents != null) {
      try {
        restaurant = ParserModelFacade.fromJson(contents, Restaurant::class.java)
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }

    return restaurant
  }

  fun isRestaurantExists(): Boolean {
    val file = File(directory, fileName)
    return file.exists()
  }

  fun deleteRestaurant(): Boolean {
    val file = File(directory, fileName)
    if (file.exists()) {
      return file.delete()
    }
    return true
  }
}
