package com.ricepo.app.restaurant.adapter.holder

import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.RestaurantDishItemBinding
import com.ricepo.app.databinding.RestaurantItemDishBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.RestaurantDishItem
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.localize
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.ResourcesUtil

//
// Created by Thomsen on 13/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantDishHolder(private val binding: RestaurantItemDishBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantUiModel.RestaurantDish) {

//        val snapHelper = GravitySnapHelper(Gravity.START)
//        snapHelper.attachToRecyclerView(binding.rvRestaurantDish)

    binding.rvRestaurantDish.layoutManager = LinearLayoutManager(
      binding.root.context,
      LinearLayoutManager.HORIZONTAL, false
    )

    var models = mutableListOf<RestaurantDishUiModel>()
    uiModel.dishes?.forEach {
      models.add(
        RestaurantDishUiModel.DishItem(
          it, uiModel.group,
          uiModel.groupIndex
        )
      )
    }

    val adapter = RestaurantDishAdapter(models)
    binding.rvRestaurantDish.adapter = adapter
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantDishHolder {
      val binding = RestaurantItemDishBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.WRAP_CONTENT,
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_295dp)
      )
      // preventing duplication
      AnalyticsFacade.horizontalScrollRestaurant(
        binding.rvRestaurantDish,
        FirebaseEventName.rScrollHorizontal
      )
      return RestaurantDishHolder(binding)
    }
  }
}

class RestaurantDishAdapter(private val dishes: List<RestaurantDishUiModel>) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.restaurant_dish_item -> {
        val binding = RestaurantDishItemBinding.inflate(
          LayoutInflater.from(parent.context)
        )
        // set the root layout left margin
        val params = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.WRAP_CONTENT,
          RecyclerView.LayoutParams.MATCH_PARENT
        )
        RestViewUtils.setFoodViewSize(
          FoodSize.BIG_WIDTH, FoodSize.BIG_HEIGHT,
          binding.ivDishFood, binding.ivDishFoodBg
        )
        binding.root.layoutParams = params

        RestaurantDishViewHolder(binding)
      }
      else -> {
        EmptyViewHolder.create(parent)
      }
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = dishes.get(position)
    when (holder) {
      is RestaurantDishViewHolder -> holder.bind(
        uiModel, position, itemCount
      )
    }
  }

  override fun getItemViewType(position: Int): Int {
    return when (dishes.get(position)) {
      is RestaurantDishUiModel.DishItem -> R.layout.restaurant_dish_item
    }
  }

  override fun getItemCount(): Int = dishes.size
}

class RestaurantDishViewHolder(val binding: RestaurantDishItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  val mapper = RestaurantMapper()

  init {
    binding.root.clickWithTrigger {
      val data = it.tag
      val event = binding.root.getTag(com.ricepo.base.R.id.tag_firebase_event)
      if (event is FirebaseRestaurantEvent) {
        event.rOnFood = false
        it.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
      }
      navigateMenu(data, it)
    }
    binding.ivDishFoodBg.clickWithTrigger {
      val data = binding.root.tag
      val event = binding.root.getTag(com.ricepo.base.R.id.tag_firebase_event)
      if (event is FirebaseRestaurantEvent) {
        event.rOnFood = true
        binding.root.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
      }
      navigateMenu(data, binding.root)
    }
  }

  private fun navigateMenu(data: Any?, it: ConstraintLayout) {
    if (data is RestaurantDishItem) {
      data.restaurant?.let {
        val searches = data.name?.localize()?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenuForBusy(binding.root.context, it, searches)
      }
      AnalyticsFacade.logEvent(it, FirebaseEventName.rSelectRestaurant)
    }
  }

  fun bind(data: RestaurantDishUiModel, position: Int, size: Int) {
    val item = if (data is RestaurantDishUiModel.DishItem) {
      data.dish
    } else return

    // set margin right
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      if (position == 0) {
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
        params.rightMargin = 0
        binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_left)
      } else if (position == (size - 1)) {
        params.leftMargin = 0
        params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
        binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_right)
      } else {
        params.leftMargin = 0
        params.rightMargin = 0
        binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_middle)
      }
      binding.root.layoutParams = params
    }

    binding.root.tag = item
    binding.root.setTag(
      com.ricepo.base.R.id.tag_firebase_event,
      FirebaseRestaurantEvent(
        rGroupId = data.group.groupId,
        rGroupType = data.group.type,
        rGroupIndex = data.groupIndex,
        rRestaurantIndex = position.toLong(),
      )
    )

//        binding.ivDishFoodBg.visibility = View.INVISIBLE
//        val placeholderDrawable = ResourcesUtil.getDrawable(
//            com.ricepo.style.R.drawable.dish_placeholder, binding.root.context)
//        ImageLoader.loadCallback(binding.ivDishFood, item.image?.url, placeholderDrawable,
//            loadReady = {
//                if ((item.image?.url != null && item.image?.noPlate != true)) {
//                    item.restaurant?.let {
//                        setMenuBackground(binding.ivDishFoodBg, it)
//                    }
//                    binding.ivDishFoodBg.visibility = View.VISIBLE
//                }
//            },
//            loadFailed = {
//                binding.ivDishFoodBg.visibility = View.INVISIBLE
//            })

    RestViewUtils.setFoodImage(item.image, item.restaurant, binding.ivDishFood, binding.ivDishFoodBg)

    // dish food name
    val foodName = item.name?.localize()
    binding.tvDishFoodName.text = foodName

    // food price
    binding.tvDishFoodPrice.text = mapper.formatPriceByRestaurant(
      item.price ?: 0, item.restaurant
    )
    binding.tvDishFoodPrice.setTextColor(
      ResourcesUtil.getColor(
        com.ricepo.style.R.color.subText, binding.root.context
      )
    )

    // food original price
    binding.tvDishFoodOriginalPrice.isVisible = (item.originalPrice != null)
    binding.tvDishFoodOriginalPrice.text = null
    item.originalPrice?.let {
      binding.tvDishFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
        Paint.ANTI_ALIAS_FLAG
      binding.tvDishFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, item.restaurant)
      binding.tvDishFoodOriginalPrice.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.deliveryNoteText, binding.root.context
        )
      )

      binding.tvDishFoodPrice.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.green, binding.root.context
        )
      )
    }

    binding.tvDishRestaurantName.text = item.restaurant?.name?.localize()
  }
}

sealed class RestaurantDishUiModel {

  data class DishItem(
    val dish: RestaurantDishItem,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long?
  ) : RestaurantDishUiModel()
}
