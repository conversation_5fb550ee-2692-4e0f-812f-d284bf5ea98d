package com.ricepo.app.restaurant.search

import androidx.activity.ComponentActivity
import com.ricepo.app.R
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.RestaurantPrediction
import com.ricepo.app.model.SearchTag
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 20/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class RestaurantSearchViewModel @Inject constructor(
  private val useCase: RestaurantSearchUseCase
) : BaseViewModel() {

  /**
   * pickup restaurant nearby miles
   */
  var miles: Int? = null

  /**
   * pickup location
   */
  var location: String? = null

  /**
   * region id
   */
  var regionId: String? = null

  /**
   * get the search keyword tag
   */
  fun getSearchTags(regionId: String?): Flow<List<SearchTag>> {
    return flow<List<SearchTag>> {
      if (regionId != null && regionId.isNotEmpty()) {
        try {
          emit(useCase.getSearchTags(regionId))
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }
  }

  /**
   * get the restaurants
   */
  fun getRestaurants(context: ComponentActivity, keyword: String?): Flow<List<RestaurantUiModel>?> {
    return flowLoading(
      context,
      error = { e ->
        DialogFacade.showAlert(context, e.parseByBuzNetwork().message ?: "")
      }
    ) {
      val searchResult = useCase.getRestaurants(location, keyword, miles)

      val restaurants = searchResult?.restaurants
      val recommend = searchResult?.recommend

      var models = mutableListOf<RestaurantUiModel>()
      if (!restaurants.isNullOrEmpty()) {
        restaurants?.forEachIndexed { index, rest ->
          models.add(RestaurantUiModel.RestaurantVertical(rest, index, restaurants.size))
        }
      } else if (!recommend.isNullOrEmpty()) {
        models.add(
          RestaurantUiModel.RestaurantSearchPrompt(
            ResourcesUtil.getString(
              com.ricepo.style.R.string.restaurant_search_recommend
            )
          )
        )
        recommend?.forEachIndexed { index, rest ->
          models.add(RestaurantUiModel.RestaurantVertical(rest, index, recommend.size))
        }
      } else {
        models.add(
          RestaurantUiModel.RestaurantSearchPrompt(
            ResourcesUtil.getString(
              com.ricepo.style.R.string.restaurant_search_not_found
            )
          )
        )
      }

      emit(models)
    }
  }

  /**
   * get the restaurant prediction
   */
  fun getRestaurantPrediction(
    context: ComponentActivity,
    keyword: String?
  ): Flow<List<RestaurantPrediction>?> {
    return flow {
      regionId?.let {
        try {
          val its = useCase.getRestaurantPrediction(it, keyword)
          emit(its)
        } catch (e: Exception) {
          e.printStackTrace()
          emit(listOf<RestaurantPrediction>())
        }
      }
    }.debounce(500)
  }

  /**
   * get the explore address
   */
  fun getExploreAddress(): Flow<List<FormatUserAddress>> {
    return flow {
      emit(useCase.getExploreAddress())
    }
  }

  /**
   * save the explore address
   * add virtual place id in explore json
   */
  fun saveExploreAddress(
    address: FormatUserAddress?,
    block: () -> Unit
  ) {
    if (address != null) {
      AddressCache.saveAddress(address) {
        block()
      }
    }
  }
}
