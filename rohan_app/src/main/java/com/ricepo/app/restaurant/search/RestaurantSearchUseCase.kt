package com.ricepo.app.restaurant.search

import com.google.gson.reflect.TypeToken
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.model.RestaurantPrediction
import com.ricepo.app.model.SearchTag
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.RestaurantSearchResult
import com.ricepo.base.tools.AssetUtils
import com.ricepo.map.model.FormatUserAddress
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 20/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantSearchUseCase @Inject constructor(
  private val repository: CombineRestApi,
  private val addressDao: AddressDao,
  private val mapper: RestaurantMapper
) : BaseUseCase() {

  /**
   * search tags
   */
  suspend fun getSearchTags(regionId: String): List<SearchTag> {
    return repository.getSearchTags(regionId)
  }

  /**
   * search restaurant prediction
   */
  suspend fun getRestaurantPrediction(regionId: String, keyword: String?):
    List<RestaurantPrediction> {
    return repository.getRestaurantPrediction(regionId, keyword)
  }

  /**
   * search the restaurants by [keyword]
   */
  suspend fun getRestaurants(location: String?, keyword: String?, miles: Int? = null):
    RestaurantSearchResult? {
    val keyword = keyword ?: return null

    val address = addressDao.getAddress()
    val loc = location ?: address?.location?.coordinates?.joinToString(",")

    return repository.getRestaurants(loc, keyword, miles)

//        val restaurants = repository.getRestaurants(loc, keyword, miles)

//        // set the closed value
//        restaurants?.forEach { rest ->
//            rest.isClosed = mapper.mapClosed(rest)
//        }
//
//        // resort the data: name > tags > food > closed restaurant
//        val nameSorter = Comparator<Restaurant> { o1, o2 ->
//            val nameScore1 = if (o1?.match?.name == true) 1 else 0
//            val nameScore2 = if (o2?.match?.name == true) 1 else 0
//            nameScore2.compareTo(nameScore1)
//        }
//
//        val tagSorter = Comparator<Restaurant> { o1, o2 ->
//            val tagCount1 = o1?.match?.tags?.count() ?: 0
//            val tagCount2 = o2?.match?.tags?.count() ?: 0
//            tagCount2.compareTo(tagCount1)
//        }
//
//        val foodSorter = Comparator<Restaurant> { o1, o2 ->
//            val foodCount1 = o1?.match?.food?.count() ?: 0
//            val foodCount2 = o2?.match?.food?.count() ?: 0
//            foodCount2.compareTo(foodCount1)
//        }
//
//        val closedSorter = Comparator<Restaurant> { o1, o2 ->
//            val closedScore1 = if (o1?.isClosed != null || o1?.closed != null) 0 else 1
//            val closedScore2 = if (o2?.isClosed != null || o2?.closed != null) 0 else 1
//            closedScore2.compareTo(closedScore1)
//        }
//
//        // then the last is a higher priority
//        return restaurants?.sortedWith(closedSorter.then(foodSorter).then(tagSorter).then(nameSorter))
  }

  /**
   * decode the explore json to object
   */
  fun getExploreAddress(): List<FormatUserAddress> {
    val content = AssetUtils.getExplore()
    return ParserModelFacade.fromJson<List<FormatUserAddress>>(
      content,
      object : TypeToken<List<FormatUserAddress>>() {}.type
    ) ?: listOf()
  }
}
