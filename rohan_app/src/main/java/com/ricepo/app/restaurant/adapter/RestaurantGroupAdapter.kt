package com.ricepo.app.restaurant.adapter

import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantItemGroupBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.holder.FunRestaurantMenuClick
import com.ricepo.app.restaurant.adapter.holder.RestaurantCascadeHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantCuisineHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantDishHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantEmptyHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantErrorHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantHorizontalHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantLuckyCombineHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantLuckyHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantMotdHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantPlanHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantRecommendHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalBigImageHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalMoreHolder
import com.ricepo.app.restaurant.adapter.stick.StickyListener
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantBannerHolder
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantGroupHolder
import com.ricepo.app.restaurant.home.adapter.holder.bind
import com.ricepo.app.restaurant.home.adapter.holder.bindEvent
import com.ricepo.app.restaurant.submore.adapter.holder.RestaurantCuisineTagHolder
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.base.model.RestaurantSort
import com.ricepo.base.model.RestaurantTab
import com.ricepo.style.DisplayUtil
import com.ricepo.style.view.rv.ScrollStatePersist

//
// Created by Thomsen on 10/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantGroupAdapter(
  private val lifecycleOwner: LifecycleOwner,
  private val isRestaurantHome: Boolean = false,
  private val cascadeStatePersist: ScrollStatePersist? = null,
  private val showTab: (tabs: List<RestaurantTab>?) -> Unit = {},
  private val groupJump: (
    groups: List<RestaurantGroup>?,
    groupIndex: Int
  ) -> Unit = { _, _ -> },
  private val sort: (sorts: List<RestaurantSort>?) -> Unit = { _ -> },
  private val refreshView: (uiModel: RestaurantUiModel) -> Unit = {},
  private val retry: (uiModel: RestaurantUiModel) -> Unit = {},
  private val showAll: ((Int) -> Unit)? = null,
  private val itemClick: FunRestaurantMenuClick = { _, _ -> },
) :
  PagingDataAdapter<RestaurantUiModel, RecyclerView.ViewHolder>(RESTAURANT_UIMODEL),
  StickyListener {

  /**
   * the item object by position
   */
  fun getItemModel(position: Int): RestaurantUiModel? {
    return try { getItem(position) } catch (e: Exception) { null }
  }

  private var firstHeaderPosition = -1
  override fun getItemViewType(position: Int): Int {
    if (position >= itemCount) return 0
    return when (val mode = getItemModel(position)?.toVerticalBigImageOrThis()) {
      is RestaurantUiModel.RestaurantVertical -> R.layout.restaurant_item_vertical
      is RestaurantUiModel.RestaurantBigImageVertical -> R.layout.restaurant_big_image_item_vertical
      is RestaurantUiModel.RestaurantCategory -> R.layout.restaurant_item_group.apply {
        if (firstHeaderPosition == -1) firstHeaderPosition = position
      }
      is RestaurantUiModel.RestaurantBanner -> R.layout.restaurant_item_banner
      is RestaurantUiModel.RestaurantHorizontal -> R.layout.restaurant_item_horizontal
      is RestaurantUiModel.RestaurantPlan -> R.layout.restaurant_item_plan
      is RestaurantUiModel.RestaurantCuisine -> R.layout.restaurant_item_cuisine
      is RestaurantUiModel.RestaurantRecommend -> R.layout.restaurant_item_recommend
      is RestaurantUiModel.RestaurantCuisineTag -> R.layout.restaurant_cuisine_tag
      is RestaurantUiModel.RestaurantVerticalMore -> R.layout.restaurant_vertical_more
      is RestaurantUiModel.RestaurantLucky -> R.layout.restaurant_item_lucky
      is RestaurantUiModel.RestaurantDish -> R.layout.restaurant_item_dish
      is RestaurantUiModel.RestaurantLuckyCombine -> R.layout.restaurant_item_lucky_combine
      is RestaurantUiModel.RestaurantTabSection -> {
        // frequent updates
        showTab(mode.tabs)
        0
      }
      is RestaurantUiModel.RestaurantMotd -> R.layout.restaurant_item_motd
      is RestaurantUiModel.RestaurantEmptyPlace -> R.layout.restaurant_item_empty
      is RestaurantUiModel.RestaurantErrorSection -> com.ricepo.base.R.layout.layout_error_container
      is RestaurantUiModel.RestaurantCascade -> R.layout.restaurant_item_cascade
      else -> 0
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    DisplayUtil.setDensity(parent.resources)
    return when (viewType) {
      R.layout.restaurant_item_vertical -> RestaurantVerticalHolder.create(parent, true, itemClick)
      R.layout.restaurant_big_image_item_vertical -> RestaurantVerticalBigImageHolder.create(parent, true, itemClick)
      R.layout.restaurant_item_banner -> RestaurantBannerHolder.create(parent, lifecycleOwner)
      R.layout.restaurant_item_group -> RestaurantGroupHolder.create(parent, groupJump, sort)
      R.layout.restaurant_item_cuisine -> RestaurantCuisineHolder.create(
        parent,
        showAll = {
          var position = 0
          for (i in 0 until itemCount - 1) {
            position =
              (getItem(i) as? RestaurantUiModel.RestaurantCategory)?.position ?: position
          }
          showAll?.invoke(position)
        }
      )
      R.layout.restaurant_item_recommend -> RestaurantRecommendHolder.create(parent)
      R.layout.restaurant_item_plan -> RestaurantPlanHolder.create(parent)
      R.layout.restaurant_item_horizontal -> RestaurantHorizontalHolder.create(parent)
      R.layout.restaurant_cuisine_tag -> RestaurantCuisineTagHolder.create(parent)
      R.layout.restaurant_vertical_more -> RestaurantVerticalMoreHolder.create(parent)
      R.layout.restaurant_item_lucky -> RestaurantLuckyHolder.create(parent)
      R.layout.restaurant_item_dish -> RestaurantDishHolder.create(parent)
      R.layout.restaurant_item_lucky_combine -> {
        observeLuckyData()
        RestaurantLuckyCombineHolder.create(parent, liveLuckyData, lifecycleOwner)
      }
      R.layout.restaurant_item_motd -> RestaurantMotdHolder.create(parent)
      R.layout.restaurant_item_empty -> RestaurantEmptyHolder.create(parent)
      com.ricepo.base.R.layout.layout_error_container -> RestaurantErrorHolder.create(parent, refreshView, retry)
      R.layout.restaurant_item_cascade -> {
        val holder = RestaurantCascadeHolder.create(parent, lifecycleOwner)
        holder.init(cascadeStatePersist)
        holder
      }
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = getItemModel(position)?.toVerticalBigImageOrThis()
    // is not one-to-one when use uiModel
    when (holder) {
      is RestaurantVerticalHolder -> {
        uiModel as RestaurantUiModel.RestaurantVertical
        holder.bind(uiModel, isRestaurantHome)
      }
      is RestaurantVerticalBigImageHolder -> {
        uiModel as RestaurantUiModel.RestaurantBigImageVertical
        holder.bind(uiModel, isRestaurantHome)
      }
      is RestaurantBannerHolder -> holder.bindBanner((uiModel as RestaurantUiModel.RestaurantBanner))
      is RestaurantGroupHolder -> holder.bind((uiModel as RestaurantUiModel.RestaurantCategory), refreshView)
      is RestaurantCuisineHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantCuisine)
      is RestaurantRecommendHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantRecommend)
      is RestaurantPlanHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantPlan)
      is RestaurantHorizontalHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantHorizontal)
      is RestaurantCuisineTagHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantCuisineTag)
      is RestaurantVerticalMoreHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantVerticalMore)
      is RestaurantLuckyHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantLucky)
      is RestaurantDishHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantDish)
      is RestaurantLuckyCombineHolder -> {
        var model = uiModel as RestaurantUiModel.RestaurantLuckyCombine
        model.position = position
        holder.bind(model)
      }
      is RestaurantMotdHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantMotd)
      is RestaurantEmptyHolder -> holder.bind((uiModel as RestaurantUiModel.RestaurantEmptyPlace).height)
      is RestaurantErrorHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantErrorSection)
      is RestaurantCascadeHolder -> holder.bind(uiModel as RestaurantUiModel.RestaurantCascade)
      else -> {}
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
    super.onBindViewHolder(holder, position, payloads)
    if (payloads.isEmpty()) {
      onBindViewHolder(holder, position)
    } else {
      payloads.forEach {
        if (it is RecyclerView.ViewHolder) {
          onBindViewHolder(it, position)
        }
      }
    }
  }

  private var liveLuckyData: MutableLiveData<RestaurantUiModel.RestaurantLuckyCombine?> =
    MutableLiveData()

  private fun observeLuckyData() {
    liveLuckyData.observe(lifecycleOwner) {
      it?.position?.let { position ->
        // refresh the lucky model with remove and refresh
        val model = getItemModel(position)
        if (model is RestaurantUiModel.RestaurantLuckyCombine) {
          model.name = it.name
          model.cards = it.cards
        }
      }
    }
  }

  override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
    super.onViewRecycled(holder)
    if (holder is RestaurantCascadeHolder) {
      holder.onRecycled()
    }
  }

  override fun onViewDetachedFromWindow(holder: RecyclerView.ViewHolder) {
    super.onViewDetachedFromWindow(holder)
    if (holder is RestaurantCascadeHolder) {
      holder.onDetachedFromWindow()
    }
  }

  override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
    super.onViewAttachedToWindow(holder)
    if (holder is RestaurantCascadeHolder) {
      holder.onAttachedToWindow()
    }
  }

  companion object {
    private val RESTAURANT_UIMODEL = object : DiffUtil.ItemCallback<RestaurantUiModel>() {
      override fun areItemsTheSame(
        oldItem: RestaurantUiModel,
        newItem: RestaurantUiModel
      ): Boolean {
        return if (oldItem is RestaurantUiModel.RestaurantBanner &&
          newItem is RestaurantUiModel.RestaurantBanner
        ) {
          oldItem.updateDate == newItem.updateDate
        } else if (oldItem is RestaurantUiModel.RestaurantVertical &&
          newItem is RestaurantUiModel.RestaurantVertical
        ) {
          oldItem.restaurant.id == newItem.restaurant.id
        } else if (oldItem is RestaurantUiModel.RestaurantCategory &&
          newItem is RestaurantUiModel.RestaurantCategory
        ) {
          // refresh when switch language in pixel emulator
          false
        } else if (oldItem is RestaurantUiModel.RestaurantErrorSection &&
          newItem is RestaurantUiModel.RestaurantErrorSection
        ) {
          false
        } else if (oldItem is RestaurantUiModel.RestaurantCascade &&
          newItem is RestaurantUiModel.RestaurantCascade
        ) {
          // refresh cascade when get restaurant data
          false
        } else {
          oldItem == newItem
        }
      }

      override fun areContentsTheSame(
        oldItem: RestaurantUiModel,
        newItem: RestaurantUiModel
      ): Boolean {
        return oldItem == newItem
      }
    }
  }

  override fun getHeaderPositionForItem(itemPosition: Int): Int {
    var headerPosition = 0
    for (i in itemPosition downTo 1) {
      if (isStickHeader(i)) {
        headerPosition = i
        return headerPosition
      }
    }
    return headerPosition
  }

  override fun bindHeaderData(header: ViewBinding, headerPosition: Int) {
    (getItemModel(headerPosition) as? RestaurantUiModel.RestaurantCategory)?.let {
      (header as? RestaurantItemGroupBinding)?.apply {
        bind(it, refreshView)
        bindEvent(
          jump = groupJump,
          sort = sort
        )
      }
    }
  }

  override fun isStickHeader(itemPosition: Int): Boolean {
    return getItemViewType(itemPosition) == R.layout.restaurant_item_group
  }

  override fun firstStickHeaderPosition(): Int = firstHeaderPosition
}
