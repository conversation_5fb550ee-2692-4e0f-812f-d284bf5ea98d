package com.ricepo.app.restaurant.home.adapter.holder

import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantHorizontalItemBinding
import com.ricepo.app.databinding.RestaurantHorizontalMoreBinding
import com.ricepo.app.databinding.RestaurantPickupItemContainerBinding
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.adapter.holder.FunRestaurantHorizontalMenuClick
import com.ricepo.app.restaurant.adapter.holder.RestaurantHorizontalItemViewHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantHorizontalMoreHolder
import com.ricepo.app.restaurant.adapter.holder.RestaurantHorizontalUiModel
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.dp

//
// Created by <PERSON><PERSON> on 11/28/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantPickupHorizontalAdapter(
  val restaurants: List<RestaurantHorizontalUiModel>,
  private val itemClick: FunRestaurantHorizontalMenuClick? = null
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    DisplayUtil.setDensity(parent.resources)
    return when (viewType) {
      R.layout.restaurant_pickup_item_container -> {
        val binding = RestaurantHorizontalItemBinding.inflate(LayoutInflater.from(parent.context))
        // fixed height, otherwise the height is not consistent
        val params = RecyclerView.LayoutParams(
          ResourcesUtil.getDimenPixelOffset(parent, com.ricepo.style.R.dimen.sw_287dp),
          ResourcesUtil.getDimenPixelOffset(parent, com.ricepo.style.R.dimen.sw_307dp)
        )
        binding.root.layoutParams = params

        val closedParams = binding.inRestaurantClosed.tvRestaurantClosed.layoutParams
        if (closedParams is LinearLayout.LayoutParams) {
          closedParams.topMargin = ResourcesUtil.getDimenPixelOffset(parent, com.ricepo.style.R.dimen.sw_22dp)
          binding.inRestaurantClosed.tvRestaurantClosed.layoutParams = closedParams
        }

        val bindingContainer = RestaurantPickupItemContainerBinding.inflate(LayoutInflater.from(parent.context))
        bindingContainer.layoutPickupContainer.addView(binding.root)
        val paramsContainer = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.WRAP_CONTENT,
          RecyclerView.LayoutParams.WRAP_CONTENT
        )
        bindingContainer.root.layoutParams = paramsContainer

        RestaurantPickupHorizontalViewHolder(bindingContainer, binding, itemClick)
      }
      R.layout.restaurant_horizontal_more -> {
        val binding = RestaurantHorizontalMoreBinding.inflate(LayoutInflater.from(parent.context))
        RestaurantHorizontalMoreHolder(binding)
      }
      else -> {
        EmptyViewHolder.create(parent)
      }
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = restaurants[position]
    when (holder) {
      is RestaurantPickupHorizontalViewHolder -> holder.bind(
        uiModel, position, itemCount
      )
      is RestaurantHorizontalMoreHolder -> holder.bind(uiModel)
    }

    if (holder.itemView.layoutParams is RecyclerView.LayoutParams) {
      val params = holder.itemView.layoutParams as RecyclerView.LayoutParams
      params.leftMargin = if (position == 0) {
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen._sw_5dp)
      } else {
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen._sw_15dp)
      }
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen._sw_10dp)
      params.rightMargin = 0
      if (position == (itemCount - 1)) {
        params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_0dp)
      }
      holder.itemView.layoutParams = params
    }
  }

  override fun getItemViewType(position: Int): Int {
    return when (restaurants[position]) {
      is RestaurantHorizontalUiModel.RestaurantItem -> R.layout.restaurant_pickup_item_container
      is RestaurantHorizontalUiModel.RestaurantMore -> R.layout.restaurant_horizontal_more
      is RestaurantHorizontalUiModel.RestaurantBigImageItem -> throw RuntimeException("not support big image here")
    }
  }

  override fun getItemCount(): Int = restaurants.size

  fun getItemModel(position: Int): RestaurantHorizontalUiModel? {
    return restaurants.getOrNull(position)
  }
}

class RestaurantPickupHorizontalViewHolder(
  bindingContainer: RestaurantPickupItemContainerBinding,
  val binding: RestaurantHorizontalItemBinding,
  private val itemClick: FunRestaurantHorizontalMenuClick?
) :
  RecyclerView.ViewHolder(bindingContainer.root) {

  fun bind(data: RestaurantHorizontalUiModel, position: Int, size: Int) {
    val holder = RestaurantHorizontalItemViewHolder(binding, false, itemClick)
    holder.bind(data, position, size, false, isNotPickup = false)
    with(binding.tvFeatureMotd) {
      setPadding(
        16.dp,
        paddingTop,
        16.dp,
        paddingBottom
      )
    }
    // reset restaurant info of miles
    if (data is RestaurantHorizontalUiModel.RestaurantItem) {
      val restaurant = data.restaurant
      val pairInfo = RestaurantBindMapper(data.restaurant).bindInfo(
        false,
        isOnlyShowTag = true
      )
      var miles = ""
      restaurant.distance?.let {
        miles = " ⋅ $it${ResourcesUtil.getString(com.ricepo.style.R.string.pickup_unit_miles)}"
      }
      val infoText = pairInfo.first.append(miles)
      binding.inRestaurantInfo.tvRestaurantInfo.text = infoText

      val bindMapper = RestaurantBindMapper(restaurant, binding.root.context)
      val promotion = bindMapper.bindCombinePromotion()
      val pairClosed = bindMapper.bindClosed()

      // clear text in bind
      binding.inRestaurantInfo.tvRestaurantSubInfo.text = null
      // auto move up one line
      if (binding.inRestaurantInfo.tvRestaurantInfo.text.isNullOrEmpty()) {
        bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantInfo, promotion, pairClosed)
      } else {
        bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantSubInfo, promotion, pairClosed)
      }
    }
  }

  private fun bindRestaurantSubInfo(
    tvView: TextView,
    promotion: SpannableStringBuilder?,
    pairClosed: Pair<SpannableStringBuilder?, Int>
  ) {
    tvView.text = promotion
    tvView.setTextColor(
      ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, binding.root.context)
    )
  }
}
