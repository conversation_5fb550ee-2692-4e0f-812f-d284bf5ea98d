package com.ricepo.app.restaurant.adapter.holder

import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.RestaurantCuisineItemBinding
import com.ricepo.app.databinding.RestaurantItemCuisineBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.luckymenu.view.FrameAnimation
import com.ricepo.app.features.luckymenu.view.FrameAnimationUtils
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Cuisine
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.ThemeImage
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

//
// Created by Thomsen on 13/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantCuisineHolder(
  private val binding: RestaurantItemCuisineBinding,
  private val showAll: () -> Unit
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantUiModel.RestaurantCuisine) {

    val params = binding.root.layoutParams as RecyclerView.LayoutParams
    if (params is RecyclerView.LayoutParams) {
      // top the banner in restaurant home and the cuisine is first
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(
        binding.root,
        R.dimen.sw_section_space
      )
      binding.root.layoutParams = params
    }

    val snapHelper = GravitySnapHelper(Gravity.START)
    snapHelper.scrollMsPerInch = 200f
    snapHelper.attachToRecyclerView(binding.rvRestaurantCuisine)

    binding.rvRestaurantCuisine.layoutManager = LinearLayoutManager(
      binding.root.context,
      LinearLayoutManager.HORIZONTAL, false
    )
    // forbidden the imageview for feeling lucky animation
    binding.rvRestaurantCuisine.recycledViewPool.setMaxRecycledViews(0, 0)

    val adapter = RestaurantCuisineAdapter(
      // TODO: this code is Temporary solution
      uiModel.cuisines.toMutableList().apply {
        add(
          Cuisine(
            name = InternationalizationContent(
              zhCN = "所有餐馆",
              zhHK = "所有餐館",
              enUS = "All Restaurants",
              es = "Todos los Restaurantes"
            ),
            tag = Cuisine.SHOW_ALL,
            image = ThemeImage(
              light = "https://s3.eu-west-1.amazonaws.com/static.rice.rocks/assets/tags/allrest/light.png",
              dark = "https://s3.eu-west-1.amazonaws.com/static.rice.rocks/assets/tags/allrest/dark.png"
            )
          )
        )
      },
      showAll = showAll,
    )
    binding.rvRestaurantCuisine.adapter = adapter
  }

  companion object {
    fun create(
      parent: ViewGroup,
      showAll: () -> Unit
    ): RestaurantCuisineHolder {
      val binding = RestaurantItemCuisineBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return RestaurantCuisineHolder(binding, showAll)
    }
  }
}

class RestaurantCuisineAdapter(
  private val cuisines: List<Cuisine>,
  private val showAll: () -> Unit,
) :
  RecyclerView.Adapter<RestaurantCuisineAdapter.CuisineViewHolder>() {

  class CuisineViewHolder(
    val binding: RestaurantCuisineItemBinding,
    private val showAll: () -> Unit,
  ) :
    RecyclerView.ViewHolder(binding.root) {

    private var animation: FrameAnimation? = null

    init {
      binding.root.clickWithTrigger {
        val data = it.tag
        if (data is Cuisine) {
          when (data.tag) {
            Cuisine.TAG_LUCKY -> {
              FeaturePageRouter.navigateLuckyMenu()
            }
            Cuisine.SHOW_ALL -> {
              showAll.invoke()
            }
            else -> {
              val request = RestaurantRequest(cuisine = data)
              FeaturePageRouter.navigateRestaurantSubmore(request)
            }
          }
        }
      }
    }

    fun bind(data: Cuisine, position: Int, size: Int) {
      binding.root.tag = data

      if (data.tag == Cuisine.TAG_LUCKY) {
        bindLuckyAnim()
      } else {
        ImageLoader.load(binding.ivCuisine, data.image?.localize())
      }

      binding.tvCuisineLabel.text = data.name?.localize()

      val params = binding.root.layoutParams
      if (params is RecyclerView.LayoutParams) {
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.restaurant_cuisine_margin)
        params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.restaurant_cuisine_margin)
        if (position == 0) {
          // left margin
          params.leftMargin = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_card_margin)
        } else if (position == (size - 1)) {
          // right margin
          params.rightMargin = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_card_margin)
        }
        binding.root.layoutParams = params
      }
    }

    private fun bindLuckyAnim() {
      animation?.let {
        it.release()
      }
      animation = null
      animation = FrameAnimation(
        binding.ivCuisine,
        FrameAnimationUtils.getRes(), 20, true
      )
      animation?.play(0)
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CuisineViewHolder {
    val binding = RestaurantCuisineItemBinding.inflate(LayoutInflater.from(parent.context))

    val params = RecyclerView.LayoutParams(
      RecyclerView.LayoutParams.WRAP_CONTENT,
      RecyclerView.LayoutParams.WRAP_CONTENT
    )
    binding.root.layoutParams = params

    RestViewUtils.setFoodViewSize(
      FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT,
      binding.ivCuisine, null
    )

    return CuisineViewHolder(binding, showAll)
  }

  override fun onBindViewHolder(holder: CuisineViewHolder, position: Int) {
    holder.bind(cuisines[position], position, itemCount)
  }

  override fun getItemCount(): Int = cuisines.size
}
