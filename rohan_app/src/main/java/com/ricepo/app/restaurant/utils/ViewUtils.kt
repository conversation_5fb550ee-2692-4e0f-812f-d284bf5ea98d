package com.ricepo.app.restaurant.utils

import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ricepo.app.R
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.ComboFoodImage
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Restaurant
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 22/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object RestViewUtils {

  /**
   * set the restaurant food plate background image
   */
  fun setMenuBackground(
    view: ImageView?,
    item: Restaurant?,
    foodImage: FoodImage?,
    visible: Boolean = true
  ) {
    val resId = if (item?.vip == true) com.ricepo.style.R.drawable.bg_plate_membership else com.ricepo.style.R.drawable.bg_plate
    if (foodImage?.noPlate == true || !visible) {
      view?.visibility = View.INVISIBLE
    } else {
      view?.setImageResource(resId)
      view?.visibility = View.VISIBLE
    }
  }

  /**
   * set the combo food plate background image
   */
  fun setMenuComboBackground(view: ImageView?, item: Restaurant?, foodImage: ComboFoodImage?) {
    val resId = if (item?.vip == true) com.ricepo.style.R.drawable.bg_plate_membership else com.ricepo.style.R.drawable.bg_plate
    if (foodImage?.noPlate == true) {
      view?.visibility = View.INVISIBLE
      view?.setImageResource(0)
    } else {
      view?.setImageResource(resId)
      view?.visibility = View.VISIBLE
    }
  }

  /**
   * set the food image in restaurant card
   */
  fun setFoodImage(
    foodImage: FoodImage?,
    item: Restaurant?,
    menuView: ImageView,
    menuBgView: ImageView
  ) {
    val url = foodImage?.url
    if (foodImage != null && !url.isNullOrEmpty()) {
      setMenuBackground(menuBgView, item, foodImage)
      menuView.setImageResource(0)
      ImageLoader.downloadBitmap(menuView.context, url) {
        it?.let {
          menuView.setImageBitmap(it)
        }
      }
//            ImageLoader.load(menuView, foodImage.url)
    } else {
      menuBgView.setImageResource(0)
      menuView.setImageResource(com.ricepo.style.R.drawable.ic_food_placeholder)
    }
  }

  /**
   * set the food view size by percent
   */
  fun setFoodViewSize(
    ratioWidth: Float,
    ratioHeight: Float,
    menuView: ImageView,
    menuBgView: ImageView?
  ) {
    val params = menuView.layoutParams
    if (params is ConstraintLayout.LayoutParams) {
      params.width = DisplayUtil.percentPx(menuView.context, ratioWidth)
      params.height = DisplayUtil.percentPx(menuView.context, ratioWidth)
      menuView.layoutParams = params
    }

    val bgParams = menuBgView?.layoutParams ?: return
    if (bgParams is ConstraintLayout.LayoutParams) {
      bgParams.width = DisplayUtil.percentPx(menuBgView.context, ratioWidth)
      bgParams.height = DisplayUtil.percentPx(menuBgView.context, ratioHeight)
      menuBgView.layoutParams = bgParams
    }
  }
}
