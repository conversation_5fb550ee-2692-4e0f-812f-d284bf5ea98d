package com.ricepo.app.restaurant.adapter.holder

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.RestaurantItemVerticalBinding
import com.ricepo.app.databinding.RestaurantMenuItemBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithClickTrigger
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.LastOrder
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.gallery.GalleryLayoutManager
import com.ricepo.style.view.CenterAlignImageSpan
import com.ricepo.style.view.gone
import com.ricepo.style.view.placeVisible
import kotlin.math.max
import kotlin.math.min

//
// Created by Thomsen on 11/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

typealias FunRestaurantMenuClick = (uiModel: RestaurantUiModel, foodName: String?) -> Unit

class RestaurantVerticalHolder(
  private val binding: RestaurantItemVerticalBinding,
  private val isClickMenu: Boolean = true,
  private val showMotd: Boolean = true,
  private val itemClick: FunRestaurantMenuClick,
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(item: RestaurantUiModel.RestaurantVertical, isShowCoin: Boolean = true) {
    // set tag
    binding.root.tag = item
    binding.rvRestaurantMenu.tag = item

    val event = FirebaseRestaurantEvent(
      rGroupId = item.group?.groupId,
      rGroupType = item.group?.type,
      rGroupIndex = item.group?.index?.toLong(),
      rRestaurantIndex = item.position.toLong()
    )
    binding.root.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
    binding.rvRestaurantMenu.setTag(com.ricepo.base.R.id.tag_firebase_event, event)

    bind(item, event, isShowCoin)
  }

  private fun bind(
    item: RestaurantUiModel.RestaurantVertical,
    event: FirebaseRestaurantEvent,
    isShowCoin: Boolean
  ) {

    val restaurant = item.restaurant

    val bindMapper = RestaurantBindMapper(restaurant, binding.root.context)

    updateView(restaurant.items, item, event, isClickMenu)

    // restaurant name
    binding.inRestaurantInfo.tvRestaurantName.text = bindMapper.bindName(isShowCoin)
    binding.inRestaurantInfo.tvRestaurantName.maxLines = 1

//    if (restaurant.lastOrder != null) {
//      // re-order section
//      bindOrder(restaurant.lastOrder)
//    } else {
//
//    }
    bindRestaurantInfo(bindMapper, restaurant)

    with(binding.tvFeatureMotd) {
      if (showMotd) {
        val bindMotd = bindMapper.bindMotd(
          featureTint = com.ricepo.style.R.color.rating_color,
          motdTint = com.ricepo.style.R.color.mr,
        )
        placeVisible(bindMotd.isNotBlank())
        text = bindMotd
        isSelected = true
      } else {
        gone()
      }
    }

    // bind the closed status
    val pairClosed = bindMapper.bindClosed()
    bindRestaurantClosed(pairClosed)
  }

  private fun bindOrder(lastOrder: LastOrder?) {
    val order = lastOrder ?: return
    binding.inRestaurantInfo.tvRestaurantInfo.text = order.items?.localize()

    val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_clock, binding.root.context)
    drawable.setBounds(
      0, 0, DisplayUtil.dp2PxOffset(12f),
      DisplayUtil.dp2PxOffset(12f)
    )
    val imageSpan = CenterAlignImageSpan(drawable)
    val spanText = SpannableStringBuilder("  ")
    spanText.append(order.time?.localize())
    spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
    binding.inRestaurantInfo.tvRestaurantSubInfo.text = spanText
    binding.inRestaurantInfo.tvRestaurantSubInfo.setTextColor(
      ResourcesUtil.getColor(
        com.ricepo.style.R.color.subTextMenu, binding.root.context
      )
    )
  }

  private fun bindRestaurantInfo(bindMapper: RestaurantBindMapper, item: Restaurant) {
    // restaurant info and promotion
    val pairInfo = bindMapper.bindInfo(needShowTag = false)
    binding.inRestaurantInfo.tvRestaurantInfo.text = pairInfo.first
    binding.inRestaurantInfo.tvRestaurantInfo.setTextColor(pairInfo.second)

    binding.inRestaurantInfo.tvRestaurantTags.text = bindMapper.bindTags()

//    val promotion = bindMapper.bindCombinePromotion()

    // auto move up one line
//    if (binding.inRestaurantInfo.tvRestaurantInfo.text.isNullOrEmpty()) {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantInfo, promotion)
//    } else {
//      bindRestaurantSubInfo(binding.inRestaurantInfo.tvRestaurantSubInfo, promotion)
//    }

    // show the pool layout
    binding.rtvPool.isVisible = (item.pool != null)
    binding.dividerPool.isVisible = (item.pool != null)

//    bindMapper.bindRating(binding.inRestaurantInfo.tvRating)

    if (item.pool != null) {
      // rice pool
      bindPool(item.pool)
    }
  }

  private fun bindRestaurantSubInfo(
    tvView: TextView,
    promotion: SpannableStringBuilder?
  ) {
    tvView.text = promotion
    tvView.setTextColor(
      ResourcesUtil.getColor(com.ricepo.style.R.color.subTextMenu, binding.root.context)
    )
  }

  private fun bindRestaurantClosed(pairClosed: Pair<SpannableStringBuilder?, Int>) {
    binding.inRestaurantClosed.tvRestaurantClosed.isVisible = !pairClosed.first.isNullOrEmpty()
    pairClosed.first?.let {
      binding.inRestaurantClosed.tvRestaurantClosed.text = it
      binding.inRestaurantClosed.tvRestaurantClosed.setTextColor(pairClosed.second)
    }
  }

  private fun bindPool(pool: RestaurantPool?) {
    val pool = pool ?: return
    binding.rtvPool.setMessage(pool.message?.localize())
    binding.rtvPool.setExpireAt(pool.expiresAt)
  }

  private fun updateView(
    foods: List<Food>?,
    item: RestaurantUiModel.RestaurantVertical,
    event: FirebaseRestaurantEvent,
    isClickMenu: Boolean
  ) {

    val adapter = RestaurantMenuAdapter(isClickMenu, itemClick)
    adapter.initData(foods, item.restaurant, event)

    val layoutManager =
      GalleryLayoutManager(GalleryLayoutManager.HORIZONTAL)

    // start position is middle
    val selectedCount = item.selectedPosition ?: ((1000000 * (if (foods.isNullOrEmpty()) 1 else foods.size)) + 1)
    with(layoutManager) {
      attach(binding.rvRestaurantMenu, selectedCount)
      setItemTransformer(RestaurantTransformer())
    }
    binding.rvRestaurantMenu.removeOnScrollListener(listener)
    binding.rvRestaurantMenu.addOnScrollListener(listener)

    layoutManager.setOnItemSelectedListener(object : GalleryLayoutManager.OnItemSelectedListener {
      override fun onItemSelected(recyclerView: RecyclerView?, view: View?, position: Int) {
        item.selectedPosition = position
      }
    })

    binding.rvRestaurantMenu.clipToPadding = true
    binding.rvRestaurantMenu.adapter = adapter
  }

  private var listener = object : RecyclerView.OnScrollListener() {
    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (newState == RecyclerView.SCROLL_STATE_IDLE) {
        // hide side in pixel 3xl
        binding.rvRestaurantMenu.clipToPadding = true
        recyclerView.postDelayed(
          {
            recyclerView.adapter?.notifyDataSetChanged()
          },
          200
        )
      } else {
        binding.rvRestaurantMenu.clipToPadding = false
      }
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      isClickMenu: Boolean = true,
      itemClick: FunRestaurantMenuClick
    ): RestaurantVerticalHolder {
      val binding = RestaurantItemVerticalBinding.inflate(
        LayoutInflater.from(parent.context)
      )

      initRestaurantVerticalBinding(binding, isClickMenu) { model, name ->
        stopScroll(binding)
        itemClick(model, name)
      }

      return RestaurantVerticalHolder(binding, isClickMenu) { model, name ->
        stopScroll(binding)
        itemClick(model, name)
      }
    }

    private fun stopScroll(binding: RestaurantItemVerticalBinding) {
      // item click when horizontal menu scroll stop
      binding.rvRestaurantMenu.stopScroll()
      binding.rvRestaurantMenu.adapter?.notifyDataSetChanged()
    }

    fun initRestaurantVerticalBinding(
      binding: RestaurantItemVerticalBinding,
      isClickMenu: Boolean = true,
      itemClick: FunRestaurantMenuClick
    ) {
      val bundleParams = binding.root.layoutParams
      val params = if (bundleParams is FrameLayout.LayoutParams) {
        // layout in the FrameLayout of bundle holder
        FrameLayout.LayoutParams(
          ViewGroup.LayoutParams.MATCH_PARENT,
          ViewGroup.LayoutParams.WRAP_CONTENT
        )
      } else {
        RecyclerView.LayoutParams(
          ViewGroup.LayoutParams.MATCH_PARENT,
          ViewGroup.LayoutParams.WRAP_CONTENT
        )
      }
      params.bottomMargin =
        ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_section_space)
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)

      binding.root.layoutParams = params

      initListener(binding, isClickMenu, itemClick)
    }

    private fun initListener(
      binding: RestaurantItemVerticalBinding,
      isClickMenu: Boolean,
      itemClick: FunRestaurantMenuClick
    ) {

      if (isClickMenu) {
        // the conflict with motion layout on swipe
        binding.rvRestaurantMenu.touchWithClickTrigger { view, event ->
          navigateMenu(view, itemClick)
        }

        // bind listener
        binding.root.clickWithTrigger { view ->
          navigateMenu(view, itemClick)
        }
      }

      binding.ivRestaurantLeft.clickWithTrigger {
        val layoutManager = binding.rvRestaurantMenu.layoutManager as GalleryLayoutManager
        val position = layoutManager.curSelectedPosition
        val state = RecyclerView.State()
        layoutManager.smoothScrollToPosition(binding.rvRestaurantMenu, state, position - 1)
      }
      binding.ivRestaurantRight.clickWithTrigger {
        val layoutManager = binding.rvRestaurantMenu.layoutManager as GalleryLayoutManager
        val position = layoutManager.curSelectedPosition
        val state = RecyclerView.State()
        layoutManager.smoothScrollToPosition(binding.rvRestaurantMenu, state, position + 1)
      }

      binding.rvRestaurantMenu.addOnScrollListener(object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
          super.onScrollStateChanged(recyclerView, newState)
          if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            binding.ivRestaurantLeft.isVisible = true
            binding.ivRestaurantRight.isVisible = true
          }
        }

        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
          super.onScrolled(recyclerView, dx, dy)
          if (dx != 0) {
            binding.ivRestaurantLeft.isVisible = false
            binding.ivRestaurantRight.isVisible = false
          }
        }
      })
    }

    private fun navigateMenu(view: View, itemClick: FunRestaurantMenuClick) {
      val item = view.tag
      if (item is RestaurantUiModel.RestaurantVertical) {
        itemClick(item, null)
        val event = view.getTag(com.ricepo.base.R.id.tag_firebase_event)
        if (event is FirebaseRestaurantEvent && item.restaurant.pool != null) {
          event.rPoolTime = SimpleDateUtils.toDateDiffSeconds(item.restaurant.pool?.expiresAt)
        }
        view.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
        AnalyticsFacade.logEvent(view, FirebaseEventName.rSelectRestaurant)
      }
    }
  }
}

/**
 * restaurant menu food adapter
 */
class RestaurantMenuAdapter(
  private val isClickMenu: Boolean,
  private val itemClick: FunRestaurantMenuClick
) :
  RecyclerView.Adapter<RestaurantMenuAdapter.RestaurantMenuItemHolder>() {

  // origin data for index
  private val sourceData: MutableList<Food> = mutableListOf()

  private var restaurant: Restaurant? = null

  private var event: FirebaseRestaurantEvent? = null

  fun initData(data: List<Food>?, restaurant: Restaurant, event: FirebaseRestaurantEvent) {
    sourceData.clear()
    if (data != null) {
      sourceData.addAll(data)
    }
    this.restaurant = restaurant
    this.event = event
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RestaurantMenuItemHolder {
    val binding = RestaurantMenuItemBinding.inflate(
      LayoutInflater.from(parent.context)
    )
    // set the layout params for constraint layout
    RestViewUtils.setFoodViewSize(
      FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT,
      binding.ivMenu, binding.ivMenuBg
    )

    val width = parent.resources.displayMetrics.widthPixels -
      (2 * ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)) -
      (2 * ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_40dp))
    val rootWidth = width / 3
    val rootParams = RecyclerView.LayoutParams(
      rootWidth,
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.dip_165)
    )

    binding.root.layoutParams = rootParams

    if (isClickMenu) {
      binding.ivMenu.clickWithTrigger {
        val food = it.tag
        val restaurant = it.getTag(R.id.tag_restaurant)
        if (food is Food && restaurant is Restaurant) {
          itemClick(
            RestaurantUiModel.RestaurantVertical(
              restaurant, 0, 0
            ),
            food.name?.localize()
          )
          event?.rOnFood = true
          // firebase event for food click
          if (event is FirebaseRestaurantEvent && restaurant.pool != null) {
            event?.rPoolTime = SimpleDateUtils.toDateDiffSeconds(restaurant.pool?.expiresAt)
          }
          it.setTag(com.ricepo.base.R.id.tag_firebase_event, event)
          AnalyticsFacade.logEvent(it, FirebaseEventName.rSelectRestaurant)
        }
      }
    }

    return RestaurantMenuItemHolder(binding)
  }

  override fun onBindViewHolder(holder: RestaurantMenuItemHolder, position: Int) {
    if (sourceData.size > 0) {
      val index = position % sourceData.size
      holder.bind(sourceData.get(index), restaurant, index)
    } else {
      holder.setFoodImage(null, null)
    }
  }

  override fun getItemCount(): Int {
    return Int.MAX_VALUE
  }

  class RestaurantMenuItemHolder(private val binding: RestaurantMenuItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

    fun bind(food: Food?, restaurant: Restaurant?, index: Int) {
      // set tag
      binding.ivMenu.tag = food
      binding.ivMenu.setTag(R.id.tag_restaurant, restaurant)

      RestViewUtils.setFoodImage(food?.image, restaurant, binding.ivMenu, binding.ivMenuBg)

      // foodName
      binding.tvFoodName.text = food?.name?.localize()
    }

    fun setFoodImage(image: FoodImage?, restaurant: Restaurant?) {
      RestViewUtils.setFoodImage(image, restaurant, binding.ivMenu, binding.ivMenuBg)
    }
  }
}

class RestaurantTransformer : GalleryLayoutManager.ItemTransformer {

  override fun transformItem(layoutManager: GalleryLayoutManager, itemView: View, fraction: Float, distance: Int) {

    val imageLayout = itemView.findViewById<ConstraintLayout?>(R.id.layout_food_image)
    val imageWidth = imageLayout.width
    val imageHeight = imageLayout.height

    imageLayout.pivotX = imageWidth / 2f
    imageLayout.pivotY = imageHeight / 2f

    // 77 90 (90 / 77)   maxScale 1.17
    val ratio = 1.66f
    val maxScale = 2f - (ratio * 0.5f)
    val realScale = if (fraction == 1f) 1f else 1.17f
    val scale = max(1f, min(maxScale, realScale))
    if (imageLayout != null) {
      imageLayout.scaleX = scale
      imageLayout.scaleY = scale
    }

    // y translation
    val y = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_30)
    imageLayout.translationY = y.toFloat()

    // x translation
    val screenWidth = itemView.resources.displayMetrics.widthPixels
    val rvWidth = screenWidth -
      2 * ResourcesUtil.getDimenPixelOffset(itemView, R.dimen.sw_card_margin)
    val anchor = if (scale > 1) {
      rvWidth * (0.785f)
    } else {
      rvWidth * (0.605f)
    }

    var offset = if (distance < 0) {
      distance / rvWidth.toFloat()
    } else if (distance > rvWidth) {
      (distance - rvWidth) / rvWidth.toFloat()
    } else {
      (anchor - distance) / rvWidth * fraction
    }
  }
}
