package com.ricepo.app.restaurant.adapter.holder

import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.RestaurantHorizontalBigImageItemBinding
import com.ricepo.app.databinding.RestaurantHorizontalItemBinding
import com.ricepo.app.databinding.RestaurantHorizontalMoreBinding
import com.ricepo.app.databinding.RestaurantItemHorizontalBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseRestaurantEvent
import com.ricepo.style.ResourcesUtil

//
// Created by Thomsen on 13/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

typealias FunRestaurantHorizontalMenuClick = (uiModel: RestaurantHorizontalUiModel, foodName: String?) -> Unit

class RestaurantHorizontalHolder(
  private val binding: RestaurantItemHorizontalBinding,
  private val itemClick: FunRestaurantHorizontalMenuClick?
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantUiModel.RestaurantHorizontal) {

    val snapHelper = GravitySnapHelper(Gravity.START)
    snapHelper.maxFlingSizeFraction = 0.8f
    snapHelper.scrollMsPerInch = 50f
    snapHelper.attachToRecyclerView(binding.rvRestaurantHorizontal)

    binding.rvRestaurantHorizontal.layoutManager = LinearLayoutManager(
      binding.root.context,
      LinearLayoutManager.HORIZONTAL, false
    )

    // promotion marquee
    binding.rvRestaurantHorizontal.addOnScrollListener(PromotionMarqueeListener())

    val models = mutableListOf<RestaurantHorizontalUiModel>()
    uiModel.restaurants.forEach {
      models.add(
        if (it.images?.landing?.isNotBlank() == true) {
          RestaurantHorizontalUiModel.RestaurantBigImageItem(
            it, uiModel.group,
            uiModel.groupIndex
          )
        } else {
          RestaurantHorizontalUiModel.RestaurantItem(
            it, uiModel.group,
            uiModel.groupIndex
          )
        }
      )
    }
    // show see more view
    if (uiModel.group.hasNextPage == true) {
      models.add(
        RestaurantHorizontalUiModel.RestaurantMore(
          uiModel.group,
          uiModel.groupIndex
        )
      )
    }

    val adapter = RestaurantHorizontalAdapter(models, itemClick)
    binding.rvRestaurantHorizontal.adapter = adapter
    binding.rvRestaurantHorizontal.recycledViewPool
      .setMaxRecycledViews(R.layout.restaurant_horizontal_item, 0)
  }

  companion object {
    fun create(parent: ViewGroup, itemClick: FunRestaurantHorizontalMenuClick? = null): RestaurantHorizontalHolder {
      val binding = RestaurantItemHorizontalBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      // preventing duplication
      AnalyticsFacade.horizontalScrollRestaurant(
        binding.rvRestaurantHorizontal,
        FirebaseEventName.rScrollHorizontal
      )
      return RestaurantHorizontalHolder(binding, itemClick)
    }
  }
}

class RestaurantHorizontalAdapter(
  private val restaurants: List<RestaurantHorizontalUiModel>,
  private val itemClick: FunRestaurantHorizontalMenuClick?
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.restaurant_horizontal_item -> {
        // java.lang.NullPointerException: Missing required view with ID if used merge and include
        val binding = RestaurantHorizontalItemBinding.inflate(LayoutInflater.from(parent.context))
        // set the root layout left margin
        val params = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.WRAP_CONTENT,
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_267dp)
        )
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
        binding.root.layoutParams = params

        // set the menu recyclerview layout size
        val restaurantParams = LinearLayout.LayoutParams(
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_268dp),
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_197dp)
        )
        restaurantParams.topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.dip_10)
        binding.layoutRestaurantSmall.layoutParams = restaurantParams

        RestViewUtils.setFoodViewSize(
          FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT,
          binding.ivLeft, binding.ivLeftBg
        )
        RestViewUtils.setFoodViewSize(
          FoodSize.SMALL_WIDTH, FoodSize.SMALL_HEIGHT,
          binding.ivMiddle, binding.ivMiddleBg
        )

        RestaurantHorizontalItemViewHolder(binding, true, itemClick)
      }

      R.layout.restaurant_horizontal_big_image_item -> {

        val binding = RestaurantHorizontalBigImageItemBinding.inflate(LayoutInflater.from(parent.context))

        // set the root layout left margin
        val params = RecyclerView.LayoutParams(
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_295dp),
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_267dp)
        )
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
        binding.root.layoutParams = params

        RestaurantHorizontalBigImageItemViewHolder(binding, itemClick)
      }

      R.layout.restaurant_horizontal_more -> {
        val binding = RestaurantHorizontalMoreBinding.inflate(LayoutInflater.from(parent.context))
        val params = RecyclerView.LayoutParams(
          RecyclerView.LayoutParams.WRAP_CONTENT,
          RecyclerView.LayoutParams.MATCH_PARENT
        )
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.dip_20)
        binding.root.layoutParams = params
        RestaurantHorizontalMoreHolder(binding)
      }
      else -> {
        EmptyViewHolder.create(parent)
      }
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = restaurants[position]
    when (holder) {
      is RestaurantHorizontalItemViewHolder -> holder.bind(
        uiModel, position, itemCount
      )
      is RestaurantHorizontalBigImageItemViewHolder -> holder.bind(
        uiModel, position, itemCount
      )
      is RestaurantHorizontalMoreHolder -> holder.bind(uiModel)
    }
  }

  override fun getItemViewType(position: Int): Int {
    return when (restaurants.get(position)) {
      is RestaurantHorizontalUiModel.RestaurantItem -> R.layout.restaurant_horizontal_item
      is RestaurantHorizontalUiModel.RestaurantMore -> R.layout.restaurant_horizontal_more
      is RestaurantHorizontalUiModel.RestaurantBigImageItem -> R.layout.restaurant_horizontal_big_image_item
    }
  }

  override fun getItemCount(): Int = restaurants.size
}

class RestaurantHorizontalMoreHolder(val binding: RestaurantHorizontalMoreBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RestaurantHorizontalUiModel) {
    val model = if (uiModel is RestaurantHorizontalUiModel.RestaurantMore) uiModel else return

    binding.root.clickWithTrigger {
      val data = model.group
      val request = RestaurantRequest(
        groupId = data.groupId,
        cursor = data.cursor,
        restaurants = data.restaurants,
        groupName = data.name,
        groupImage = data.image,
        groupDesc = data.description
      )
      val scrollPosition = (data.restaurants?.size ?: 0) - 1
      FeaturePageRouter.navigateRestaurantSubmore(request, scrollPosition)
      it.setTag(
        com.ricepo.base.R.id.tag_firebase_event,
        FirebaseRestaurantEvent(
          rGroupId = data.groupId,
          rGroupIndex = model.groupIndex,
          rGroupType = data.type,
        )
      )
      AnalyticsFacade.logEvent(it, FirebaseEventName.rShowMore)
    }
  }
}

sealed class RestaurantHorizontalUiModel {

  data class RestaurantItem(
    val restaurant: Restaurant,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long?
  ) : RestaurantHorizontalUiModel()

  data class RestaurantBigImageItem(
    val restaurant: Restaurant,
    val group: RestaurantRemoteGroup,
    val groupIndex: Long?
  ) : RestaurantHorizontalUiModel()

  data class RestaurantMore(val group: RestaurantRemoteGroup, val groupIndex: Long?) :
    RestaurantHorizontalUiModel()
}
