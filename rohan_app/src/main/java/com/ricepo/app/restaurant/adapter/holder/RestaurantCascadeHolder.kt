package com.ricepo.app.restaurant.adapter.holder

import android.graphics.Rect
import android.os.Handler
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantItemCascadeBinding
import com.ricepo.app.di.entrypoint.CombineApiPoint
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.model.localize
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.holder.cascade.RestaurantCascadeAdapter
import com.ricepo.app.restaurant.adapter.holder.cascade.RestaurantCascadeItemHolder
import com.ricepo.app.restaurant.adapter.holder.cascade.RestaurantCascadeUiModel
import com.ricepo.base.BaseApplication
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.touchWithClickTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.RestaurantDishItem
import com.ricepo.base.model.RestaurantGroupType
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.localize
import com.ricepo.base.parser.ParserFacade
import com.ricepo.monitor.firebase.FirebaseCascadeEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.ScrollStatePersist
import com.ricepo.style.view.rv.extend.AutoPollRecyclerView
import com.ricepo.style.view.rv.listener.OnLoadMoreListener
import com.ricepo.style.view.rv.listener.RecyclerViewLoadMoreScroll
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs
import kotlin.math.min

//
// Created by Thomsen on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantCascadeHolder(
  private val binding: RestaurantItemCascadeBinding,
  private val lifecycleOwner: LifecycleOwner
) :
  RecyclerView.ViewHolder(binding.root), ScrollStatePersist.ScrollStateKeyProvider {

  var adapter: RestaurantCascadeAdapter? = null

  var models = mutableListOf<RestaurantCascadeUiModel>()

  var uiModel: RestaurantUiModel.RestaurantCascade? = null

  lateinit var onLoadMoreScrollerListener: RecyclerViewLoadMoreScroll

  private var persist: ScrollStatePersist? = null

  private val api: CombineRestApi by lazy {
    EntryPointAccessors.fromApplication(
      BaseApplication.context,
      CombineApiPoint::class.java
    ).injectCombineApi()
  }

  fun bind(uiModel: RestaurantUiModel.RestaurantCascade) {
    this.uiModel = uiModel

    if (binding.rvCascadeContainer.getBackgroundBitmap() == null) {
      uiModel.group.background?.localize()?.let {
        ImageLoader.downloadBitmap(binding.root.context, it) {
          Log.i("thom", "download bitmap")
          binding.rvCascadeContainer.setBackgroundBitmap(it)
          // refresh restaurant list to refresh background
          adapter?.notifyDataSetChanged()
        }
      }
    }

    if (adapter == null || compareDiff(uiModel.dishes)) {
      models.clear()
      models.add(RestaurantCascadeUiModel.CascadeTitle(uiModel.group))
      uiModel.dishes?.forEach {
        models.add(
          RestaurantCascadeUiModel.CascadeItem(
            it, uiModel.group,
            uiModel.groupIndex
          )
        )
      }
      adapter = RestaurantCascadeAdapter(models)
      binding.rvCascadeContainer.adapter = adapter
    } else {
      adapter?.dishes = models
      adapter?.notifyDataSetChanged()
    }

    // will trigger bind again
    persist?.restoreScrollState(binding.rvCascadeContainer, this)
  }

  /**
   * the change cache adapter data when address changed
   */
  private fun compareDiff(dishes: List<RestaurantDishItem>?): Boolean {
    val dishes = dishes ?: return true

    val targetSize = dishes.size
    val n = min(targetSize, models.size)
    val modIds = models.filterIsInstance<RestaurantCascadeUiModel.CascadeItem>().take(n).map {
      if (it is RestaurantCascadeUiModel.CascadeItem) {
        it.dish.id
      } else {
        ""
      }
    }
    val targetIds = dishes.map { it.id }
    return (ParserFacade.gson.toJson(modIds) != ParserFacade.gson.toJson(targetIds))
  }

  fun onRecycled() {
    // detached also will invoke
    persist?.saveScrollState(binding.rvCascadeContainer, this)
    binding.rvCascadeContainer.stop()
    uiModel = null
  }

  fun onAttachedToWindow() {
    if (binding.rvCascadeContainer.isAttachedToWindow) {
      binding.rvCascadeContainer.start()
      Log.i("thom", "cascade on attached")
      // solved mismatch item
      persist?.restoreScrollState(binding.rvCascadeContainer, this)
    } else {
      Log.i("thom", "cascade on attach not success")
    }
  }

  fun onDetachedFromWindow() {
    binding.rvCascadeContainer.stop()
    Log.i("thom", "cascade on detached")
  }

  override fun getScrollStateKey(): String? {
    return uiModel?.group?.groupId + uiModel?.group?.name?.localize()
  }

  private val gravitySnapHelper = GravitySnapHelper(Gravity.START) {
  }

  fun init(persist: ScrollStatePersist?) {
    this.persist = persist
    persist?.setupRecyclerView(binding.rvCascadeContainer, this)

    val layoutManager = StaggeredGridLayoutManager(
      2, LinearLayoutManager.HORIZONTAL
    )
    binding.rvCascadeContainer.layoutManager = layoutManager
    binding.rvCascadeContainer.isNestedScrollingEnabled = false
    binding.rvCascadeContainer.itemAnimator = null

    gravitySnapHelper.maxFlingSizeFraction = 0.8f
    gravitySnapHelper.scrollMsPerInch = 50f
    gravitySnapHelper.attachToRecyclerView(binding.rvCascadeContainer)

    binding.rvCascadeContainer.recycledViewPool.setMaxRecycledViews(
      R.layout.restaurant_cascade_item, 36
    )

    onLoadMoreScrollerListener = RecyclerViewLoadMoreScroll(layoutManager)
    onLoadMoreScrollerListener.setOnLoadMoreListener(object : OnLoadMoreListener {
      override fun onLoadMore(position: Int) {
        loadMoreData(position)
      }

      override fun showProgress(isShow: Boolean) {
        binding.pbCascade.isVisible = isShow
      }
    })

    // recover view for scroll screen up-down to refresh
    binding.rvCascadeContainer.addOnScrollListener(onLoadMoreScrollerListener)

    // scroll listener get the last scroll position
    binding.rvCascadeContainer.addOnScrollListener(object : RecyclerView.OnScrollListener() {
      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
          // dragging scroll and not auto scroll
          logCascadeEvent(FirebaseEventName.rCascadeScroll)
        }
      }
    })

    binding.rvCascadeContainer.touchWithClickTrigger { view, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        val x = event.x
        val y = event.y
        binding.rvCascadeContainer.findChildViewUnder(x, y)?.let {
          val holder = binding.rvCascadeContainer.findContainingViewHolder(it)
          if (holder is RestaurantCascadeItemHolder) {
            holder.navigateMenu()
          }
        }
      }
    }
  }

  var startX = 0f
  var startY = 0f
  private var downLong: Long? = null

  private val mHandler = Handler()

  private fun touchLongAndClickListener(event: MotionEvent): Boolean {
    var result = false
    if (event.action == MotionEvent.ACTION_DOWN) {
      downLong = System.currentTimeMillis()
      startX = event.x
      startY = event.y
      mHandler.removeCallbacksAndMessages(null)
      mHandler.postDelayed(
        {
          downLong = null
          val view = binding.rvCascadeContainer.findChildViewUnder(startX, startY) ?: return@postDelayed
          val foodView = view?.findViewById<ImageView>(R.id.iv_food)
          val rect = Rect()
          foodView?.getGlobalVisibleRect(rect)
          if ((startX <= rect.right && startX >= rect.left) &&
            (startY <= rect.bottom)
          ) {
            val holder = binding.rvCascadeContainer.findContainingViewHolder(view)
            if (holder is RestaurantCascadeItemHolder) {
              holder.transitionPreview()
            }
          }
        },
        500
      )
    }
    if (event.action == MotionEvent.ACTION_MOVE) {
      if (abs(event.x - startX) > 15 || abs(event.y - startY) > 15) {
        mHandler.removeCallbacksAndMessages(null)
        downLong = null
      } else {
        // no-op
      }
    }
    if (event.action == MotionEvent.ACTION_CANCEL) {
      mHandler.removeCallbacksAndMessages(null)
    }
    if (event.action == MotionEvent.ACTION_UP) {
      mHandler.removeCallbacksAndMessages(null)
      if (downLong != null) {
        result = true
        val x = event.x
        val y = event.y
        binding.rvCascadeContainer.findChildViewUnder(x, y)?.let {
          val holder = binding.rvCascadeContainer.findContainingViewHolder(it)
          if (holder is RestaurantCascadeItemHolder) {
            holder.navigateMenu()
          }
        }
      }
    }
    return result
  }

  private fun loadMoreData(position: Int) {
    refreshData(models.lastOrNull()?.group)
  }

  private fun refreshData(group: RestaurantRemoteGroup?) {
    val group = group ?: return
    // return when is loading data
    lifecycleOwner.lifecycleScope.launch {
      try {
        // log
        logCascadeEvent(FirebaseEventName.rCascadeMore)
        // refresh the lucky data and filter items size > 1
        val remoteSection = withContext(Dispatchers.IO) {
          val loc = AddressCache.getAddressSuspend()?.location?.loc()
          val params = mutableMapOf("location" to loc)
          group.groupId?.let {
            params.put("groupId", it)
          }
          group.cursor?.let {
            params.put("cursor", it)
          }
          api.getRestaurantSection(params)
        }
        remoteSection?.sections?.forEach { remoteGroup ->
          when (remoteGroup.type) {
            RestaurantGroupType.cascade -> {
              val items = remoteGroup.data?.items
              if (items?.isNotEmpty() == true) {
                items.forEach {
                  models.add(
                    RestaurantCascadeUiModel.CascadeItem(
                      it, remoteGroup, null
                    )
                  )
                }
              }
            }
          }
        }

        onLoadMoreScrollerListener.isLoading = false
        binding.pbCascade.isVisible = false

        if (remoteSection?.sections?.filter {
          (
            it.type
              == RestaurantGroupType.cascade
            )
        }?.isNullOrEmpty() == false
        ) {
          adapter?.notifyDataSetChanged()
        }
      } catch (e: Exception) {
        e.printStackTrace()
      } finally {
        onLoadMoreScrollerListener.isLoading = false
        binding.pbCascade.isVisible = false
      }
    }
  }

  private fun logCascadeEvent(name: String) {
    val event = FirebaseCascadeEvent()
    event.rScrollDepth = AutoPollRecyclerView.scrollHoriDepth
    AnalyticsFacade.logEvent(event, name)
  }

  companion object {

    fun create(parent: ViewGroup, lifecycleOwner: LifecycleOwner): RestaurantCascadeHolder {
      val binding = RestaurantItemCascadeBinding.inflate(LayoutInflater.from(parent.context))

      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      params.bottomMargin = ResourcesUtil.getDimenPixelOffset(
        binding.root, R.dimen.sw_section_space
      )
      binding.root.layoutParams = params

      // set the height for rv to set bitmap dest height
      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_368dp)
      val rvParams = binding.rvCascadeContainer.layoutParams
      if (rvParams is ConstraintLayout.LayoutParams) {
        rvParams.height = height
        binding.rvCascadeContainer.layoutParams = rvParams
      }
      binding.rvCascadeContainer.isSelected = true

      return RestaurantCascadeHolder(binding, lifecycleOwner)
    }
  }
}
