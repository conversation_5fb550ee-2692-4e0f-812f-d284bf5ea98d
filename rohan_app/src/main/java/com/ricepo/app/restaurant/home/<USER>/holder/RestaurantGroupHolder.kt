package com.ricepo.app.restaurant.home.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RestaurantItemGroupBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.RestaurantSort
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.gone
import com.ricepo.style.view.show

//
// Created by <PERSON><PERSON> on 17/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun RestaurantItemGroupBinding.bind(
  uiModel: RestaurantUiModel.RestaurantCategory,
  refreshView: (uiModel: RestaurantUiModel) -> Unit = {}
) {
  val group = uiModel.group

  // sort button
  val sorted = uiModel.sorts?.filter { it.selected }?.firstOrNull()
  val sortSelected = uiModel.sorted ?: sorted
  with(restaurantSort) {
    tag = if (sorted != null) {
      show()
      uiModel.sorts
    } else {
      gone()
      null
    }
    text = sortSelected?.name?.localize()
  }

//        val groupType = group?.type
  // hide the jump icon when submore page or bundle fragment
  ivCategoryJump.isVisible = (group?.jump == true) && !uiModel.isSubMore

  tvRestaurantJumpClick.tag = uiModel

  tvRestaurantGroupName.text = group?.name?.localize() ?: ""
  tvRestaurantGroupName.isSelected = true
  val titleColor = when (group?.titleStyle) {
    RestaurantRemoteGroup.TITLE_STYLE_PROMOTION -> {
      ResourcesUtil.getColor(com.ricepo.style.R.color.rest_address_fill, root)
    }
    RestaurantRemoteGroup.TITLE_STYLE_VIP -> {
      ResourcesUtil.getColor(com.ricepo.style.R.color.goldSubText, root)
    }
    else -> {
      ivRestaurantName.setImageResource(0)
      ResourcesUtil.getColor(com.ricepo.style.R.color.mainText, root)
    }
  }
  tvRestaurantGroupName.setTextColor(titleColor)

  val desc = group?.description?.localize()
  tvRestaurantGroupDesc.isVisible = (desc?.isNotEmpty() == true)
  tvRestaurantGroupDesc.text = desc

  // the zero is the banner not in list
  val groupIndex = uiModel.groupIndex
  tvRestaurantGroupDivider.isVisible = groupIndex != 0

  // hide the divider
  tvRestaurantGroupDivider.isVisible = false
}

fun RestaurantItemGroupBinding.bindEvent(
  jump: (
    groups: List<RestaurantGroup>?,
    groupIndex: Int
  ) -> Unit = { _, _ -> },
  sort: (sorts: List<RestaurantSort>?) -> Unit = { _ -> }
) {
  tvRestaurantJumpClick.clickWithTrigger {
    if (!ivCategoryJump.isVisible) return@clickWithTrigger
    val uiModel = it.tag
    if (uiModel is RestaurantUiModel.RestaurantCategory) {
      val groups = uiModel.jumpGroups?.filter { it.jump == true }
      jump(groups, uiModel.position)
    }
  }

  restaurantSort.clickWithTrigger {
    val sortTag = it.tag
    if (sortTag is List<*>) {
      val sorts = sortTag.mapNotNull {
        if (it is RestaurantSort) {
          it
        } else null
      }
      sort(sorts)
    }
  }
}

class RestaurantGroupHolder(
  private val binding: RestaurantItemGroupBinding,
  private val jump: (
    groups: List<RestaurantGroup>?,
    groupIndex: Int
  ) -> Unit = { _, _ -> },
  private val sort: (sorts: List<RestaurantSort>?) -> Unit = { _ -> }
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    binding.bindEvent(jump, sort)
  }

  fun bind(
    uiModel: RestaurantUiModel.RestaurantCategory,
    refreshView: (uiModel: RestaurantUiModel) -> Unit = {}
  ) {
    binding.bind(uiModel, refreshView)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      jump: (groups: List<RestaurantGroup>?, groupIndex: Int) -> Unit,
      sort: (sorts: List<RestaurantSort>?) -> Unit
    ): RestaurantGroupHolder {
      val binding = RestaurantItemGroupBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.clipChildren = false
      binding.root.layoutParams = params
      return RestaurantGroupHolder(binding, jump, sort)
    }
  }
}
