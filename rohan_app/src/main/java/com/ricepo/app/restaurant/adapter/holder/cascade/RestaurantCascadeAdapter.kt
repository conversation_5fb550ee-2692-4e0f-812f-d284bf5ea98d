package com.ricepo.app.restaurant.adapter.holder.cascade

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 2021/9/14.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

class RestaurantCascadeAdapter(var dishes: List<RestaurantCascadeUiModel>) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    DisplayUtil.setDensity(parent.resources)
    return when (viewType) {
      R.layout.restaurant_cascade_item -> {
        RestaurantCascadeItemHolder.create(parent)
      }
      R.layout.restaurant_cascade_title -> RestaurantCascadeTitleHolder.create(parent)
      else -> {
        EmptyViewHolder.create(parent)
      }
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = dishes.get(position)
    when (holder) {
      is RestaurantCascadeItemHolder -> holder.bind(uiModel, position, itemCount)
      is RestaurantCascadeTitleHolder -> holder.bind(
        uiModel as RestaurantCascadeUiModel.CascadeTitle, position
      )
    }
  }

  override fun getItemViewType(position: Int): Int {
    return when (dishes.get(position)) {
      is RestaurantCascadeUiModel.CascadeItem -> R.layout.restaurant_cascade_item
      is RestaurantCascadeUiModel.CascadeTitle -> R.layout.restaurant_cascade_title
    }
  }

  override fun getItemCount(): Int = dishes.size
}
