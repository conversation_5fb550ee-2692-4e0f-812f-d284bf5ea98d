package com.ricepo.app.restaurant

import com.ricepo.app.features.checkout.CheckoutActivity
import com.ricepo.app.model.Card
import com.ricepo.app.model.ChatModel
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.ImageFeedbackReq
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderReq
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.QuoteRequest
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.SupportReq
import com.ricepo.app.model.ValidateCouponReq
import com.ricepo.app.model.ValidateCouponRes
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.app.restapi.RestaurantRestApi
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.DriverTrace
import com.ricepo.base.model.Menu
import com.ricepo.base.model.MenuBean
import com.ricepo.base.model.RegionModel
import com.ricepo.base.model.Restaurant
import com.ricepo.base.tools.SystemUtils
import com.ricepo.monitor.EventLevel
import com.ricepo.monitor.MonitorEvent
import com.ricepo.monitor.MonitorFacade
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.core.Single
import javax.inject.Inject

class RestaurantRemoteImpl @Inject constructor(
  private val restApi: RestaurantRestApi
) : RestaurantRemote {

  override fun getRestaurants(loc: String?): Single<List<Restaurant>> {
    return restApi.getRestaurants(loc, null)
  }

  override fun getRegions(regionId: String): Single<RegionModel> {
    return restApi.getRegion(regionId)
  }

  override fun getRestaurantById(restaurantId: String, loc: String?): Single<Restaurant> {
    return restApi.getRestaurantById(restaurantId, loc)
  }

  override fun getMenu(restaurantId: String): Single<Menu> {
    return restApi.getMenu(restaurantId)
  }

  override fun getMenu(
    restaurantId: String,
    loc: String,
    search: String?,
    bundles: List<String>?,
    searches: List<String>?,
    filter: String?
  ): Single<MenuBean> {
    val params = mutableMapOf<String, String>()
    if (search?.isEmpty() == false) {
      params["search"] = search
    }
    params["location"] = loc
    filter?.let {
      params.put("filter", filter)
    }
    return restApi.getMenu(
      restaurantId, params,
      bundles?.toTypedArray(), searches?.toTypedArray()
    )
  }

  override fun imageFeedback(req: ImageFeedbackReq): Single<NetworkError> {
    return restApi.imageFeedback(req)
  }

  override fun getQuote(restaurantId: String, body: QuoteRequest): Single<QuoteResponse> {
    return restApi.getQuote(restaurantId, body)
  }

  override fun getCouponByCustomerId(customerId: String): Single<List<Coupon>> {
    return restApi.getCouponByCustomerId(customerId)
  }

  override fun getCouponByRestId(restaurantId: String): Single<List<Coupon>> {
    return restApi.getCouponByRestId(restaurantId)
  }

  override fun getCouponByRegionId(regionId: String): Single<List<Coupon>> {
    return restApi.getCouponByRegionId(regionId)
  }

  override fun validateCoupon(couponId: String, body: ValidateCouponReq): Single<ValidateCouponRes> {
    return restApi.validateCoupon(couponId, body)
  }

  override fun getRecommendCoupons(body: ValidateCouponReq): Single<List<Coupon>> {
    return restApi.getRecommendCoupons(body)
  }

  override fun getCards(customerId: String?, type: Array<String>): Single<List<Card>> {
    return if (customerId.isNullOrEmpty()) {
      restApi.getCards(type)
    } else {
      restApi.getCards(customerId, type)
    }
  }

  override fun deleteCard(customerId: String, cardId: String): Single<retrofit2.Response<Unit>> {
    return restApi.deleteCard(customerId, cardId)
  }

  override fun deleteBbvaCard(customerId: String, cardId: String): Single<retrofit2.Response<Unit>> {
    return restApi.deleteBbvaCard(customerId, cardId)
  }

  override fun createIntentPayment(orderId: String, paymentMethodId: String): Single<PaymentObj> {
    val body = mapOf("payment_method_id" to paymentMethodId)
    return restApi.createIntentPayment(orderId, body)
  }

  override fun createWechatPayment(orderId: String): Single<PaymentObj> {
    return restApi.createWechatPayment(orderId)
  }

  override fun createAlipayPayment(orderId: String): Single<PaymentObj> {
    return restApi.createAlipayPayment(orderId)
  }

  override fun createUnionPayment(orderId: String): Single<PaymentObj> {
    return restApi.createUnionPayment(orderId)
  }

  override fun createPaypalPayment(orderId: String): Single<PaymentObj> {
    return restApi.createPaypalPayment(orderId)
  }

  override fun createBBVAPayment(orderId: String, paymentId: String?): Single<PaymentObj> {
    val params: HashMap<String, String> = hashMapOf()
    if (!paymentId.isNullOrEmpty()) {
      params.put("payment_method_id", paymentId)
    }
    return restApi.createBBVAPayment(orderId, params)
  }


  override fun createOrder(restaurantId: String, body: OrderReq): Single<OrderResponse> {
    // try to avoid duplicate orders
    val topActivityName = SystemUtils.getTopActivityName()
    if (topActivityName != CheckoutActivity.ACTIVITY_NAME) {
      val event = MonitorEvent(
        "Place Order In $topActivityName",
        level = EventLevel.ERROR
      )
      val reqJson = ParserModelFacade.toJson(body)
      event.extras = mapOf(
        "restaurantId" to restaurantId,
        "orderReq" to reqJson
      )
//      MonitorFacade.captureEvent(event)
      return Single.just(
        OrderResponse.error(
          ErrorData(
            Throwable("Please place order in Checkout page")
          )
        )
      )
    }
    return restApi.createOrder(restaurantId, body)
  }

  override fun getOrderById(orderId: String): Single<Order> {
    return restApi.getOrderById(orderId)
  }

  override fun getPoint(orderId: String): Single<DriverPoint> {
    return restApi.getPoint(orderId)
  }

  override fun getDriverTrace(orderId: String): Single<DriverTrace> {
    return restApi.getDriverTrace(orderId)
  }

  override fun getOrders(
    customerId: String,
    type: CustomerOrderType,
    isBefore: String?
  ): Single<List<Order>> {
    var params = mutableMapOf<String, Any>(
      "limit" to 30,
      "type" to type
    )
    if (isBefore != null) {
      params["isBefore"] = isBefore
    }
    return restApi.getOrders(customerId, params)
  }

  override fun createSubscription(planId: String, paymentMethodId: String): Single<PaymentObj> {
    val body = mapOf(
      "planId" to planId,
      "payment_method_id" to paymentMethodId
    )
    return restApi.createSubscription(body)
  }

  override fun cancelSubscription(subscriptionId: String, cancel: Boolean): Single<Void> {
    val body = mapOf(
      "cancel" to cancel
    )
    return restApi.cancelSubscription(subscriptionId, body)
  }

  override fun updateSubscriptionPayment(subscriptionId: String, paymentMethodId: String): Single<Void> {
    val body = mapOf(
      "payment_method_id" to paymentMethodId
    )
    return restApi.updateSubscriptionPayment(subscriptionId, body)
  }

  override fun createTicket(orderId: String, body: SupportReq): Single<Any> {
    return restApi.createTicket(orderId, body)
  }

  override fun getSupportChatToken(): Single<ChatModel> {
    return restApi.getSupportChatToken()
  }

  override fun uploadImage(imageData: ByteArray, imageType: String): Single<String> {
    return restApi.uploadImage()
  }
}
