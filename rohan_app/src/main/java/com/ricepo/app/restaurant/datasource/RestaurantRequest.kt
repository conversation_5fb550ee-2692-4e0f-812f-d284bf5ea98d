package com.ricepo.app.restaurant.datasource

import android.os.Parcelable
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.Cuisine
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.ThemeImage
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 12/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Parcelize
data class RestaurantRequest(
  // FormatUserAddress location?.coordinates?.joinToString(",")
  val location: String? = null,
  val groupId: String? = null,
  val cursor: String? = null,
  val cuisine: Cuisine? = null,
  val groupName: InternationalizationContent? = null,
  val groupImage: ThemeImage? = null,
  val groupDesc: InternationalizationContent? = null,
  // last page restaurants
  val restaurants: List<Restaurant>? = null,
  // otherwise get the region
  val isRegion: Boolean = false,
  // request type
  val type: String? = null,
  val sortPosition: Int? = null,
  val sortData: CategoryJumpData? = null,

) : Parcelable
