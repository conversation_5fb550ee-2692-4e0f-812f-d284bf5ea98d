package com.ricepo.app.restaurant.adapter.holder

import android.content.Context
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.LuckyMenuItemFiveBinding
import com.ricepo.app.databinding.LuckyMenuItemFourBinding
import com.ricepo.app.databinding.LuckyMenuItemThreeBinding
import com.ricepo.app.databinding.LuckyMenuItemTwoBinding
import com.ricepo.app.databinding.RestaurantItemLuckyCombineBinding
import com.ricepo.app.di.entrypoint.CombineApiPoint
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.luckymenu.view.FrameAnimation
import com.ricepo.app.features.luckymenu.view.FrameAnimationUtils
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.BaseApplication
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Cart
import com.ricepo.base.model.LuckRecommendBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseLuckCombineEvent
import com.ricepo.monitor.firebase.FirebaseMonitor
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.CardHelperCallback
import com.ricepo.style.view.rv.CardManager
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by Thomsen on 21/04/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantLuckyCombineHolder(
  val binding: RestaurantItemLuckyCombineBinding,
  private val liveLuckyData: MutableLiveData<RestaurantUiModel.RestaurantLuckyCombine?>,
  private val lifecycleOwner: LifecycleOwner
) :
  RecyclerView.ViewHolder(binding.root) {

  private var animation: FrameAnimation? = null

  private val api: CombineRestApi by lazy {
    EntryPointAccessors.fromApplication(
      BaseApplication.context,
      CombineApiPoint::class.java
    ).injectCombineApi()
  }

  init {
    // play menu lucky animation
    animation?.let {
      it.release()
    }
    animation = null
    animation = FrameAnimation(
      binding.ivLucky,
      FrameAnimationUtils.getRes(), 20, true
    )
    animation?.play(0)

    // recycler view config
    val manager = LuckyCardManager(binding.root.context)
    binding.rvLuckyMenu.layoutManager = manager
    val itemTouchHelper = CardHelperCallback()
    itemTouchHelper.setListener(object : CardHelperCallback.OnItemTouchCallbackListener {
      override fun onSwiped(position: Int, direction: Int) {
        val cards = mAdapter?.datas
        if (cards != null) {
          for (i in 0 until binding.rvLuckyMenu.childCount) {
            val view: View = binding.rvLuckyMenu.getChildAt(i)
            view.alpha = 1f
          }
          // remove card
          val mutableCards = cards.toMutableList()
          mutableCards.removeAt(position)
          mAdapter?.datas = mutableCards
          mAdapter?.notifyDataSetChanged()
          postRefreshData(mutableCards)
          // remove recycler view layout
          if (mutableCards.size < 1) {
            mCurrentCards = null
            // ivLuckyNoCardBg can clickable
            translateLuckyOut()
          }

          // firebase event
          if (mCacheModel != null) {
            val index = ((mCacheModel?.cards?.size ?: 0) - mutableCards.size - 1)
            FirebaseMonitor.logEvent(
              FirebaseEventName.rLuckySwipe,
              FirebaseLuckCombineEvent(index)
            )
          }
        }
      }
    })
    val helper = ItemTouchHelper(itemTouchHelper)
    helper.attachToRecyclerView(binding.rvLuckyMenu)

    // click listener
    binding.ivLuckyRefresh.clickWithTrigger(1000) {
      // refresh new cards
      refreshData()
    }
    binding.ivLuckyNoCardBg.clickWithTrigger(1000) {
      refreshData()
    }

//        try {
//            binding.motionLuckyMenu.getConstraintSet(R.id.lucky_out)
//                    .getConstraint(binding.rvLuckyMenu.id)
//                    .propertySet.mVisibilityMode = 1
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
  }

  private var mAdapter: RestaurantLuckyAdapter? = null

  private var mCacheModel: RestaurantUiModel.RestaurantLuckyCombine? = null
  private var mCurrentCards: List<LuckRecommendBean>? = null

  fun bind(uiModel: RestaurantUiModel.RestaurantLuckyCombine?) {
    val model = uiModel ?: return
    if (mCacheModel == null) {
      // new object to reset
      mCacheModel = RestaurantUiModel.RestaurantLuckyCombine(
        name = model.name, position = model.position, cards = model.cards
      )
    }
    binding.tvFeelingLucky.text = model.name

    // food card
    mCurrentCards = model.cards
    mAdapter = RestaurantLuckyAdapter(mCurrentCards, mCacheModel)

    binding.rvLuckyMenu.adapter = mAdapter
    translateLuckyIn()
    if (mCurrentCards.isNullOrEmpty()) {
      // translate out for clickable
      binding.root.postDelayed(
        {
          translateLuckyOut()
        },
        500
      )
    }
  }

  private fun translateLuckyIn() {
    // setTransitionListener don't remove
    binding.motionLuckyMenu.setTransition(R.id.transition_lucky_translate_in)
    binding.motionLuckyMenu.transitionToEnd()
  }

  private fun translateLuckyOut() {
    // remove the autoTransition in Transition and translate_out before translate_in
    binding.motionLuckyMenu.setTransition(R.id.transition_lucky_translate_out)
    binding.motionLuckyMenu.transitionToEnd()
  }

  private fun startAnimation() {
    if (mCurrentCards.isNullOrEmpty()) {
      bind(mCacheModel)
    } else {
      translateLuckyOut()
      binding.root.postDelayed(
        {
          bind(mCacheModel)
        },
        150
      )
    }
    binding.root.postDelayed(
      {
        stopLoading()
      },
      600
    )
  }

  private fun bindRefresh() {
    bind(mCacheModel)
    if (mCacheModel?.cards.isNullOrEmpty()) {
      translateLuckyOut()
    }
    stopLoading()
  }

  private fun startLoading() {
    ImageLoader.load(
      binding.ivLuckyRefresh, com.ricepo.style.R.drawable.lucky_refresh_loading,
      true, priority = ImageLoader.PRIORITY_IMMEDIATE
    )
    // the tag if loading
    binding.ivLuckyRefresh.tag = true
  }

  private fun stopLoading() {
    ImageLoader.load(
      binding.ivLuckyRefresh, com.ricepo.style.R.drawable.ic_lucky_refresh,
      priority = ImageLoader.PRIORITY_IMMEDIATE
    )
    binding.ivLuckyRefresh.tag = false
  }

  private var transitionListener = object : MotionLayout.TransitionListener {
    override fun onTransitionStarted(p0: MotionLayout?, p1: Int, p2: Int) {
    }

    override fun onTransitionChange(p0: MotionLayout?, p1: Int, p2: Int, p3: Float) {
    }

    override fun onTransitionCompleted(p0: MotionLayout?, p1: Int) {
      when (p1) {
        R.id.lucky_out -> {
          bindRefresh()
        }
      }
    }

    override fun onTransitionTrigger(
      p0: MotionLayout?,
      p1: Int,
      p2: Boolean,
      p3: Float
    ) {
    }
  }

  /**
   * refresh last cards
   */
  private fun refreshCacheData() {
    if (mCurrentCards.isNullOrEmpty()) {
      bindRefresh()
    } else {
      startAnimation()
    }
  }

  private fun refreshData() {
    // return when is loading data
    if (binding.ivLuckyRefresh.tag is Boolean && binding.ivLuckyRefresh.tag == true) return
    startLoading()
    lifecycleOwner.lifecycleScope.launch {
      try {
        // refresh the lucky data and filter items size > 1
        withContext(Dispatchers.IO) {
          val loc = AddressCache.getAddressSuspend()?.location?.loc()
          val restaurants = api.getLuckyData(loc)
          val datas = restaurants?.map {
            LuckRecommendBean(restaurant = it, items = it.items ?: listOf())
          }?.filter { it.items.size > 1 }?.reversed()
          mCacheModel?.cards = datas
          postRefreshData(datas)
        }
        startAnimation()
      } catch (e: Exception) {
        e.printStackTrace()
        stopLoading()
        DialogFacade.showAlert(binding.root.context, e.parseByBuzNetwork().message ?: "")
      }
    }
  }

  /**
   * changing the data in the list
   */
  private fun postRefreshData(cards: List<LuckRecommendBean>?) {
    val model = RestaurantUiModel.RestaurantLuckyCombine(
      mCacheModel?.name ?: "", mCacheModel?.position, cards
    )
    liveLuckyData.postValue(model)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      liveLuckyData: MutableLiveData<RestaurantUiModel.RestaurantLuckyCombine?>,
      lifecycleOwner: LifecycleOwner
    ):
      RestaurantLuckyCombineHolder {
      val binding = RestaurantItemLuckyCombineBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )

      return RestaurantLuckyCombineHolder(binding, liveLuckyData, lifecycleOwner)
    }
  }
}

class RestaurantLuckyAdapter(
  var datas: List<LuckRecommendBean>?,
  private val mModel: RestaurantUiModel.RestaurantLuckyCombine?
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun getItemViewType(position: Int): Int {
    // return the type for food size
    return when (datas?.get(position)?.items?.size ?: 0) {
      2 -> R.layout.lucky_menu_item_two
      3 -> R.layout.lucky_menu_item_three
      4 -> R.layout.lucky_menu_item_four
      5 -> R.layout.lucky_menu_item_five
      else -> com.ricepo.base.R.layout.layout_empty_holder
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.lucky_menu_item_two -> RestaurantLuckyTwoHolder.create(parent)
      R.layout.lucky_menu_item_three -> RestaurantLuckyThreeHolder.create(parent)
      R.layout.lucky_menu_item_four -> RestaurantLuckyFourHolder.create(parent)
      R.layout.lucky_menu_item_five -> RestaurantLuckyFiveHolder.create(parent)
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    // the index of all cards
    val index = if (mModel != null) {
      ((mModel?.cards?.size ?: 0) - itemCount)
    } else {
      position
    }
    val card = datas?.getOrNull(position)
    card?.index = index
    when (holder) {
      is RestaurantLuckyTwoHolder -> (holder.bind(card))
      is RestaurantLuckyThreeHolder -> (holder.bind(card))
      is RestaurantLuckyFourHolder -> (holder.bind(card))
      is RestaurantLuckyFiveHolder -> (holder.bind(card))
    }
  }

  override fun getItemCount() = datas?.size ?: 0
}

open class RestaurantLuckyBaseHolder(private val view: View) : RecyclerView.ViewHolder(view) {

  init {
    view.findViewById<TextView?>(R.id.tv_add_cart)?.clickWithTrigger {
      val bean = it.tag
      if (bean is LuckRecommendBean) {
        addShoppingCart(bean)

        // firebase event
        try {
          FirebaseMonitor.logEvent(
            FirebaseEventName.rLuckySubmit,
            FirebaseLuckCombineEvent(bean.index)
          )
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }
  }

  fun mapper(restaurant: Restaurant): RestaurantBindMapper {
    return RestaurantBindMapper(restaurant, view.context)
  }

  fun setFoodImage(
    luckData: LuckRecommendBean,
    views: List<ImageView>,
    bgViews: List<ImageView>,
    tvViews: List<TextView>
  ) {
    val images = luckData.items?.map {
      it.image
    }
    val names = luckData.items?.map {
      it.name?.localize()
    }
    views.forEachIndexed { index, imageView ->
      val image = images?.getOrNull(index)
      if (image?.url.isNullOrEmpty()) {
        // placeholder image
        bgViews.getOrNull(index)?.setImageResource(0)
        imageView.setImageResource(com.ricepo.style.R.drawable.ic_food_placeholder)
      } else {
        RestViewUtils.setMenuBackground(bgViews.getOrNull(index), luckData.restaurant, image)
        ImageLoader.load(imageView, image?.url)
      }
//            // invisible the plate bg
//            bgViews.getOrNull(index)?.visibility = if (image?.noPlate == true) {
//                View.INVISIBLE
//            } else {
//                View.VISIBLE
//            }

      // set menu food name
      tvViews.getOrNull(index)?.text = names?.getOrNull(index)
    }
  }

  /**
   * add cart list
   */
  private fun addShoppingCart(bean: LuckRecommendBean?) {
    val restaurant = bean?.restaurant ?: return
    val mapper = MenuMapper()
    val cartList = bean?.items?.map {
      if (!it.options.isNullOrEmpty()) {
        // the options food to cart
        val optFood = mapper.mapOptionsLucky(it)
        mapper.mapCart(optFood, restaurant.name)
      } else {
        mapper.mapCart(it, restaurant.name)
      }
    }?.toMutableList()
    view.findViewTreeLifecycleOwner()?.lifecycleScope?.launch {

      // first check the group status
      var cacheCart = RestaurantCartCache.getRestaurantCartSuspend(
        restaurant, true
      ) ?: RestaurantCart(listOf(), restaurant = null)
      if (cacheCart.restaurant?.id != restaurant?.id &&
        (cacheCart.cartList?.size ?: 0) > 0
      ) {
        // alert the cart will be cleared
        val message = ResourcesUtil.getString(
          com.ricepo.style.R.string.hint_cart_will_be_cleared,
          cacheCart?.restaurant?.name?.localize() ?: ""
        )
        DialogFacade.showPrompt(view.context, message) {
          view.findViewTreeLifecycleOwner()?.lifecycleScope?.launch {
            withContext(Dispatchers.IO) {
              GroupOrderCache.deleteOrder()
              RestaurantCartCache.deleteRestaurantCart(restaurant)
            }
            if (cartList != null) {
              addCardAndNav(cacheCart, cartList, restaurant)
            }
          }
        }
      } else {
        // Added shopping cart cache cart
        val toMutableList = cacheCart.cartList?.toMutableList()
        toMutableList?.let {
          cartList?.addAll(it)
        }
        if (cartList != null) {
          addCardAndNav(cacheCart, cartList, restaurant)
        }
      }
    }
  }

  private suspend fun addCardAndNav(
    restautantCart: RestaurantCart,
    cartList: MutableList<Cart>,
    restaurant: Restaurant
  ) {
    // log add food event
    val foodIds = cartList.map { it.id }
    LifecycleNetworkListener.logAddFood(restaurant.id, foodIds)

    withContext(Dispatchers.IO) {
      restautantCart?.cartList = cartList
      restautantCart?.restaurant = restaurant
      restautantCart?.let {
        RestaurantCartCache.saveRestaurantCart(it)
      }
    }
    // forward to menu page
    FeaturePageRouter.navigateMenu(restaurant)
  }

  open fun getDesc(restaurant: Restaurant): Pair<SpannableStringBuilder?, Int> {
    val mapper = mapper(restaurant)
    val closed = mapper.bindClosed()
    val info = mapper.bindInfo(isShowDelivery = false)
    // reset the closed status color
    return if (closed.first != null) Pair(
      closed.first,
      ResourcesUtil.getColor(com.ricepo.style.R.color.alert, view.context)
    ) else info
  }
}

class RestaurantLuckyTwoHolder(val binding: LuckyMenuItemTwoBinding) :
  RestaurantLuckyBaseHolder(binding.root) {

  fun bind(data: LuckRecommendBean?) {
    val data = data ?: return
    binding.tvRestaurantName.text = data.restaurant.name?.localize()
    val descPair = getDesc(data.restaurant)
    binding.tvRestaurantDesc.text = descPair.first
    binding.tvRestaurantDesc.setTextColor(descPair.second)

    binding.tvAddCart.tag = data

    setFoodImage(
      data,
      listOf(binding.ivFoodOne, binding.ivFoodTwo),
      listOf(binding.ivFoodOneBg, binding.ivFoodTwoBg),
      listOf(binding.tvFoodOneName, binding.tvFoodTwoName)
    )
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantLuckyTwoHolder {
      val binding = LuckyMenuItemTwoBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      val margin = DisplayUtil.getScreenWidth().times(0.04).toInt()
      params.leftMargin = margin
      params.rightMargin = margin
      binding.root.layoutParams = params
      return RestaurantLuckyTwoHolder(binding)
    }
  }
}

class RestaurantLuckyThreeHolder(val binding: LuckyMenuItemThreeBinding) :
  RestaurantLuckyBaseHolder(binding.root) {

  fun bind(data: LuckRecommendBean?) {
    val data = data ?: return
    binding.tvRestaurantName.text = data.restaurant.name?.localize()
    val descPair = getDesc(data.restaurant)
    binding.tvRestaurantDesc.text = descPair.first
    binding.tvRestaurantDesc.setTextColor(descPair.second)

    binding.tvAddCart.tag = data

    setFoodImage(
      data,
      listOf(binding.ivFoodOne, binding.ivFoodTwo, binding.ivFoodThree),
      listOf(binding.ivFoodOneBg, binding.ivFoodTwoBg, binding.ivFoodThreeBg),
      listOf(binding.tvFoodOneName, binding.tvFoodTwoName, binding.tvFoodThreeName)
    )
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantLuckyThreeHolder {
      val binding = LuckyMenuItemThreeBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      val margin = DisplayUtil.getScreenWidth().times(0.04).toInt()
      params.leftMargin = margin
      params.rightMargin = margin
      binding.root.layoutParams = params
      return RestaurantLuckyThreeHolder(binding)
    }
  }
}

class RestaurantLuckyFourHolder(val binding: LuckyMenuItemFourBinding) :
  RestaurantLuckyBaseHolder(binding.root) {

  fun bind(data: LuckRecommendBean?) {
    val data = data ?: return
    binding.tvRestaurantName.text = data.restaurant.name?.localize()
    val descPair = getDesc(data.restaurant)
    binding.tvRestaurantDesc.text = descPair.first
    binding.tvRestaurantDesc.setTextColor(descPair.second)

    binding.tvAddCart.tag = data

    setFoodImage(
      data,
      listOf(
        binding.ivFoodOne, binding.ivFoodTwo,
        binding.ivFoodThree, binding.ivFoodFour
      ),
      listOf(
        binding.ivFoodOneBg, binding.ivFoodTwoBg,
        binding.ivFoodThreeBg, binding.ivFoodFourBg
      ),
      listOf(
        binding.tvFoodOneName, binding.tvFoodTwoName,
        binding.tvFoodThreeName, binding.tvFoodFourName
      )
    )
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantLuckyFourHolder {
      val binding = LuckyMenuItemFourBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      val margin = DisplayUtil.getScreenWidth().times(0.04).toInt()
      params.leftMargin = margin
      params.rightMargin = margin
      binding.root.layoutParams = params

      return RestaurantLuckyFourHolder(binding)
    }
  }
}

class RestaurantLuckyFiveHolder(val binding: LuckyMenuItemFiveBinding) :
  RestaurantLuckyBaseHolder(binding.root) {

  fun bind(data: LuckRecommendBean?) {
    val data = data ?: return
    binding.tvRestaurantName.text = data.restaurant.name?.localize()
    val descPair = getDesc(data.restaurant)
    binding.tvRestaurantDesc.text = descPair.first
    binding.tvRestaurantDesc.setTextColor(descPair.second)

    binding.tvAddCart.tag = data

    setFoodImage(
      data,
      listOf(
        binding.ivFoodOne, binding.ivFoodTwo, binding.ivFoodThree,
        binding.ivFoodFour, binding.ivFoodFive
      ),
      listOf(
        binding.ivFoodOneBg, binding.ivFoodTwoBg, binding.ivFoodThreeBg,
        binding.ivFoodFourBg, binding.ivFoodFiveBg
      ),
      listOf(
        binding.tvFoodOneName, binding.tvFoodTwoName, binding.tvFoodThreeName,
        binding.tvFoodFourName, binding.tvFoodFiveName
      )
    )
  }

  companion object {
    fun create(parent: ViewGroup): RestaurantLuckyFiveHolder {
      val binding = LuckyMenuItemFiveBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
//            params.leftMargin = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_20)
      val margin = DisplayUtil.getScreenWidth().times(0.04).toInt()
      params.leftMargin = margin
      params.rightMargin = margin

      binding.root.layoutParams = params
      return RestaurantLuckyFiveHolder(binding)
    }
  }
}

class LuckyCardManager(context: Context) : CardManager(context) {

  // plate width and height scale margin because the vertical has shadow
  // the ration w,1:1.2 (ratio = width / height) (h = w * ratio || w = h / ratio)
  private val ratio = 1.2

  override fun setConstraint(view: View) {
    // set the constraint by screen width
    when (view.id) {
      R.id.cl_lucky_menu_item_two -> {
        setConstraintTwo(LuckyMenuItemTwoBinding.bind(view))
      }
      R.id.cl_lucky_menu_item_four -> {
        setConstraintFour(LuckyMenuItemFourBinding.bind(view))
      }
      R.id.cl_lucky_menu_item_three -> {
        setConstraintThree(LuckyMenuItemThreeBinding.bind(view))
      }
      R.id.cl_lucky_menu_item_five -> {
        setConstraintFive(LuckyMenuItemFiveBinding.bind(view))
      }
    }
  }

  private fun setConstraintTwo(binding: LuckyMenuItemTwoBinding) {

    val screenWidth = DisplayUtil.getScreenWidth()
    val oneSize = screenWidth.times(0.20f).toInt()
    val oneLeftMargin = screenWidth.times(0.16).toInt()
    val oneParams = ConstraintLayout.LayoutParams(oneSize, oneSize.times(ratio).toInt())
    oneParams.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.topToTop = binding.guideLuckyHeader.id
    oneParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.leftMargin = oneLeftMargin
    binding.ivFoodOneBg.layoutParams = oneParams

    val twoSize = screenWidth.times(0.24f).toInt()
    val twoLeftMargin = screenWidth.times(0.10).toInt()
    val twoParams = ConstraintLayout.LayoutParams(twoSize, twoSize.times(ratio).toInt())
    twoParams.leftToRight = binding.ivFoodOneBg.id
    twoParams.bottomToBottom = binding.ivFoodOneBg.id
    twoParams.leftMargin = twoLeftMargin
    binding.ivFoodTwoBg.layoutParams = twoParams
  }

  private fun setConstraintThree(binding: LuckyMenuItemThreeBinding) {

    val screenWidth = DisplayUtil.getScreenWidth()
    val oneSize = screenWidth.times(0.17f).toInt()
    val oneLeftMargin = screenWidth.times(0.09).toInt()
    val oneParams = ConstraintLayout.LayoutParams(oneSize, oneSize.times(ratio).toInt())
    oneParams.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.topToTop = binding.guideLuckyHeader.id
    oneParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.leftMargin = oneLeftMargin
    binding.ivFoodOneBg.layoutParams = oneParams

    val twoSize = screenWidth.times(0.17f).toInt()
    val twoLeftMargin = screenWidth.times(0.04).toInt()
    val twoParams = ConstraintLayout.LayoutParams(twoSize, twoSize.times(ratio).toInt())
    twoParams.leftToRight = binding.ivFoodOneBg.id
    twoParams.topToTop = binding.ivFoodOneBg.id
    twoParams.leftMargin = twoLeftMargin
    twoParams.topMargin = oneSize.times(0.3).toInt()
    binding.ivFoodTwoBg.layoutParams = twoParams

    val threeSize = screenWidth.times(0.24f).toInt()
    val threeLefMargin = screenWidth.times(0.04).toInt()
    val threeParams = ConstraintLayout.LayoutParams(threeSize, threeSize.times(ratio).toInt())
    threeParams.leftToRight = binding.ivFoodTwoBg.id
    threeParams.bottomToBottom = binding.ivFoodOneBg.id
    threeParams.leftMargin = threeLefMargin
    binding.ivFoodThreeBg.layoutParams = threeParams
  }

  private fun setConstraintFour(binding: LuckyMenuItemFourBinding) {

    val screenWidth = DisplayUtil.getScreenWidth()
    val oneSize = screenWidth.times(0.18f).toInt()
    val oneLeftMargin = screenWidth.times(0.06).toInt()
    val oneParams = ConstraintLayout.LayoutParams(oneSize, oneSize.times(ratio).toInt())
    oneParams.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.topToTop = binding.guideLuckyHeader.id
    oneParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.leftMargin = oneLeftMargin
    binding.ivFoodOneBg.layoutParams = oneParams

    val twoSize = screenWidth.times(0.14f).toInt()
    val twoLeftMargin = screenWidth.times(0.02).toInt()
    val twoParams = ConstraintLayout.LayoutParams(twoSize, twoSize.times(ratio).toInt())
    twoParams.leftToRight = binding.ivFoodOneBg.id
    twoParams.topToTop = binding.ivFoodOneBg.id
    twoParams.leftMargin = twoLeftMargin
    twoParams.topMargin = oneSize.div(2)
    binding.ivFoodTwoBg.layoutParams = twoParams

    val threeSize = screenWidth.times(0.18f).toInt()
    val threeLefMargin = screenWidth.times(0.02).toInt()
    val threeParams = ConstraintLayout.LayoutParams(threeSize, threeSize.times(ratio).toInt())
    threeParams.leftToRight = binding.ivFoodTwoBg.id
    threeParams.bottomToBottom = binding.ivFoodOneBg.id
    threeParams.leftMargin = threeLefMargin
    threeParams.bottomMargin = oneSize.div(2)
    binding.ivFoodThreeBg.layoutParams = threeParams

    val fourSize = DisplayUtil.getScreenWidth().times(0.14f).toInt()
    val fourLeftMargin = screenWidth.times(0.02).toInt()
    val fourParams = ConstraintLayout.LayoutParams(fourSize, fourSize.times(ratio).toInt())
    fourParams.leftToRight = binding.ivFoodThreeBg.id
    fourParams.topToTop = binding.ivFoodOneBg.id
    fourParams.leftMargin = fourLeftMargin
    binding.ivFoodFourBg.layoutParams = fourParams
  }

  private fun setConstraintFive(binding: LuckyMenuItemFiveBinding) {

    val screenWidth = DisplayUtil.getScreenWidth()
    val oneSize = screenWidth.times(0.12f).toInt()
    val oneLeftMargin = screenWidth.times(0.06).toInt()
    val oneParams = ConstraintLayout.LayoutParams(oneSize, oneSize.times(ratio).toInt())
    oneParams.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.topToTop = binding.guideLuckyHeader.id
    oneParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
    oneParams.leftMargin = oneLeftMargin
    oneParams.bottomMargin = oneSize.times(1.1).toInt()
    binding.ivFoodOneBg.layoutParams = oneParams

    val twoSize = screenWidth.times(0.16f).toInt()
    val twoLeftMargin = screenWidth.times(0.01).toInt()
    val twoParams = ConstraintLayout.LayoutParams(twoSize, twoSize.times(ratio).toInt())
    twoParams.leftToRight = binding.ivFoodOneBg.id
    twoParams.topToTop = binding.ivFoodOneBg.id
    twoParams.leftMargin = twoLeftMargin
    twoParams.topMargin = oneSize.times(0.9).toInt()
    binding.ivFoodTwoBg.layoutParams = twoParams

    val threeSize = screenWidth.times(0.16f).toInt()
    val threeLefMargin = screenWidth.times(0.00).toInt()
    val threeParams = ConstraintLayout.LayoutParams(threeSize, threeSize.times(ratio).toInt())
    threeParams.leftToRight = binding.ivFoodTwoBg.id
    threeParams.bottomToBottom = binding.ivFoodOneBg.id
    threeParams.leftMargin = threeLefMargin
    threeParams.bottomMargin = oneSize.times(0.2).toInt()
    binding.ivFoodThreeBg.layoutParams = threeParams

    val fourSize = DisplayUtil.getScreenWidth().times(0.16f).toInt()
    val fourLeftMargin = screenWidth.times(0.00).toInt()
    val fourParams = ConstraintLayout.LayoutParams(fourSize, fourSize.times(ratio).toInt())
    fourParams.leftToRight = binding.ivFoodThreeBg.id
    fourParams.topToTop = binding.ivFoodOneBg.id
    fourParams.leftMargin = fourLeftMargin
    fourParams.topMargin = oneSize.times(0.9).toInt()
    binding.ivFoodFourBg.layoutParams = fourParams

    val fiveSize = DisplayUtil.getScreenWidth().times(0.11f).toInt()
    val fiveLeftMargin = screenWidth.times(0.02).toInt()
    val fiveParams = ConstraintLayout.LayoutParams(fiveSize, fiveSize.times(ratio).toInt())
    fiveParams.leftToRight = binding.ivFoodFourBg.id
    fiveParams.topToTop = binding.ivFoodOneBg.id
    fiveParams.leftMargin = fiveLeftMargin
    fiveParams.topMargin = oneSize.times(0.1).toInt()
    binding.ivFoodFiveBg.layoutParams = fiveParams
  }
}
