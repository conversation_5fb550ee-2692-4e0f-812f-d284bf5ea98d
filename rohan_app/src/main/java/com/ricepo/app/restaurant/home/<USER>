package com.ricepo.app.restaurant.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.ricepo.app.HomeTab
import com.ricepo.app.MainViewModel
import com.ricepo.app.R
import com.ricepo.app.compose.fontFamily
import com.ricepo.app.databinding.LayoutRestaurantTabBinding
import com.ricepo.base.BaseFragment
import com.ricepo.base.BaseSupperActivity
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

interface TabRefreshInterface {
  fun refreshTab(type: String?)
  fun refreshTab(tab: HomeTab)
}

abstract class TabFragment : BaseFragment(), TabRefreshInterface

abstract class ComposeTabActivity : BaseSupperActivity(), TabRefreshInterface {

  abstract fun isSearchVisible(): Boolean
  /**
   * show restaurant tab view
   */
  protected fun initTabState(
    binding: LayoutRestaurantTabBinding,
    mainViewModel: MainViewModel
  ) {

    lifecycleScope.launchWhenStarted {

      launch {
        mainViewModel.tabs.collectLatest {
          val selected = it.toList().filter { it.isSelected }.first().javaClass
          binding.layRestaurantTab.setContent {
            composeTabs(
              tabs = it.toList(),
              onSelectClick = {
                if (isSearchVisible()) {
                  showSelectDialog(it.toList())
                }
              },
              onTabClick = { ct ->
                if (isSearchVisible() && ct.javaClass != selected) {
                  mainViewModel.refreshTabs(ct)
                  refreshTab(ct)
                }
              }
            )
          }
        }
      }

      launch {
        mainViewModel.showTab.collectLatest {
          binding.root.isVisible = it
          binding.layRestaurantTab.isVisible = it
        }
      }
    }
  }

  @Composable
  private fun composeTabs(
    tabs: List<HomeTab>,
    onSelectClick: () -> Unit,
    onTabClick: (HomeTab) -> Unit
  ) {
    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
      val itemWidth = with(LocalDensity.current) {
        (constraints.maxWidth / tabs.size).toDp()
      }
      Row(
        modifier = Modifier.fillMaxWidth().align(Alignment.Center),
        horizontalArrangement = Arrangement.SpaceAround
      ) {
        tabs.forEach {
          TabItem(
            tab = it,
            showSelect = (
              (
                it as? HomeTab.Home
                )?.innerTabs?.size ?: 0
              ) > 1,
            width = itemWidth,
            onTabClick = {
              onTabClick.invoke(it)
            },
            onSelectClick = {
              onSelectClick.invoke()
            }
          )
        }
      }
    }
  }

  @Composable
  private fun TabItem(
    tab: HomeTab,
    showSelect: Boolean = false,
    width: Dp,
    onSelectClick: () -> Unit,
    onTabClick: () -> Unit,
  ) {
    val uiTab = tab.toUi()
    Row(
      modifier = Modifier.width(width).padding(start = 6.dp, end = 6.dp).clickable {
        onTabClick()
      }
    ) {
      Image(
        painter = painterResource(id = uiTab.getCurrentImage()),
        contentDescription = ""
      )
      Text(
        modifier = Modifier.align(Alignment.CenterVertically).padding(start = 4.dp),
        text = uiTab.title,
        color = colorResource(id = getColorRes(uiTab.selected)),
        fontFamily = fontFamily,
        fontWeight = FontWeight.Bold,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
      )
      if (showSelect) {
        Image(
          painter = painterResource(
            id = com.ricepo.style.R.drawable.ic_arrow_down
          ),
          contentDescription = "",
          modifier = Modifier.clickable {
            onSelectClick.invoke()
          }.align(Alignment.CenterVertically),
          colorFilter = ColorFilter.tint(
            colorResource(id = getColorRes(uiTab.selected)),
          )
        )
      }
    }
  }

  private fun getColorRes(isSelected: Boolean) = if (isSelected) {
    com.ricepo.style.R.color.mr
  } else {
    com.ricepo.style.R.color.tab_item
  }

  private fun showSelectDialog(tabs: List<HomeTab>) {
    HomeSwitchModeBottomSheet(tabs) {
      refreshTab(it.type)
    }.show(supportFragmentManager, "")
  }
}
