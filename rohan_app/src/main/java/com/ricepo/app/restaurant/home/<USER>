package com.ricepo.app.restaurant.home

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadState
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.eazypermissions.common.model.PermissionResult
import com.eazypermissions.coroutinespermission.PermissionManager
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.logEvent
import com.ricepo.app.BuildConfig
import com.ricepo.app.HomeTab
import com.ricepo.app.MainActivity
import com.ricepo.app.MainViewModel
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.databinding.FragmentRestaurantFloorBinding
import com.ricepo.app.databinding.RestaurantItemGroupBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.jump.CategoryJumpFragment
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.UpdateInfo
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.GoogleMapsRestApi
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantGroupAdapter
import com.ricepo.app.restaurant.adapter.holder.RestaurantCascadeHolder
import com.ricepo.app.restaurant.adapter.stick.StickyHeaderDecoration
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantGroupHolder
import com.ricepo.app.utils.log
import com.ricepo.base.adapter.FooterLoadStateAdapter
import com.ricepo.base.analytics.ScrollDepthFacade
import com.ricepo.base.animation.Loading
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.base.model.RestaurantSort
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.PlacesFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.utils.GpsUtils
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.ScrollStatePersist
import com.ricepo.style.view.rv.TopSmoothScroller
import com.scwang.smart.refresh.layout.constant.RefreshState
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.subjects.BehaviorSubject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 17/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@AndroidEntryPoint
class RestaurantFloorFragment : TabFragment() {

  companion object {
    fun newInstance() = RestaurantFloorFragment()
  }

  val restaurantViewModel: RestaurantViewModel by viewModels()

  val mainViewModel: MainViewModel by activityViewModels()

  @Inject
  lateinit var combineRestApi: CombineRestApi

  @Inject
  lateinit var googleMapsRestApi: GoogleMapsRestApi

  var restaurantDisplayAdapter: RestaurantGroupAdapter? = null

  lateinit var viewModelOutput: RestaurantViewModel.Output

  /**
   * the boolean is force refresh
   */
  lateinit var aRefreshRestaurant: BehaviorSubject<Boolean>

  private val REQUEST_CODE_LOCATION: Int = 1002

  private var mContext: Context? = null

  private lateinit var binding: FragmentRestaurantFloorBinding

  private lateinit var menuMapper: MenuMapper

//  private lateinit var appUpdateManager: AppUpdateManager

  private var cascadeSectionPersist: ScrollStatePersist? = null

  private var ratingJob: Job? = null

  override fun onAttach(context: Context) {
    super.onAttach(context)
    mContext = context
//    appUpdateManager = AppUpdateManagerFactory.create(context)
  }

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View? {
    cascadeSectionPersist = ScrollStatePersist(savedInstanceState)

    binding = FragmentRestaurantFloorBinding.inflate(inflater)
    return binding.root
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    cascadeSectionPersist?.onSaveInstanceState(outState)
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)

    bindViewModelInput()
    bindViewModelOutput()
    setupRecyclerView()
    menuMapper = MenuMapper()

    ScrollDepthFacade.computeScrollDepth(binding.recyclerFloorList)

    binding.recyclerFloorList.addOnScrollListener(PromotionMarqueeListener())
    binding.recyclerFloorList.addOnScrollListener(onSortScrollListener)
    binding.inRestaurantSort.clRestaurantSort.clickWithTrigger {
      val sortTag = sortLayout?.tag
      if (sortTag is List<*>) {
        showSortFragment(sortTag)
      }
    }
    observeLoginLoad()
    observe()
  }

  private fun observe() {
    lifecycleScope.launch {
      launch {
        restaurantViewModel.popupData.collectLatest {
          showPromoImage(
            requireContext(),
            it
          )
        }
      }
      launch {
        restaurantViewModel.checkRating.collectLatest {
          checkRating(it)
        }
      }
    }
  }

  override fun onDestroyView() {
    super.onDestroyView()
    binding.recyclerFloorList.removeOnScrollListener(onSortScrollListener)
  }

  private fun bindViewModelInput() {
    aRefreshRestaurant = BehaviorSubject.create()
    val viewModelInput = RestaurantViewModel.Input(viewLifecycleOwner, aRefreshRestaurant)
    viewModelOutput = restaurantViewModel.transform(viewModelInput)
  }

  private fun bindViewModelOutput() {

    viewModelOutput.liveAddress.observe(
      viewLifecycleOwner
    ) {
      if (it?.placeId == null && it?.formatted == null) {
        initRestaurantByLoc()
      } else {
        restaurantViewModel.isLoadTrigger = true
        setTitle(it)
        if (mContext != null) {
          // pull down not show screen loading
          val showLoading = binding.srlFloorList.state != RefreshState.Refreshing
          // blink of
          if (showLoading) {
            binding.recyclerFloorList.itemAnimator = null
          } else {
            binding.recyclerFloorList.itemAnimator = DefaultItemAnimator()
          }
          getNearRestaurants(it)
        }
      }
    }
  }

  private fun getNearRestaurants(address: FormatUserAddress) {
    sortPosition = SORT_INVALID
    restaurantViewModel.getNearRestaurants(address)
  }

  private fun observeLoginLoad() {
    restaurantViewModel.loginState.observe(
      this.viewLifecycleOwner
    ) {
      if (it.isLoginAndVipChange) {
        restaurantViewModel.isLoadTrigger = false
        refreshTab(null)
        // check update
        checkPageStart(true)
      } else {
        // refresh if 'can't get location dialog' dismiss
        // and the first load end
        if (restaurantViewModel.isLoadTrigger) {
          restaurantViewModel.isLoadTrigger = false
          // refresh 30 minutes background
          val refresh = restaurantViewModel.checkStopPoint(mContext)
          if (refresh) {
            sortPosition = SORT_INVALID
          }
          aRefreshRestaurant.onNext(refresh)
          firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SEARCH) {
            param(FirebaseAnalytics.Param.VALUE, "30 minutes is $refresh")
          }
        }
        checkPageStart(false)
      }
    }
  }

  override fun onStart() {
    super.onStart()
    // refresh cart bar when back from menu
    (requireActivity() as? MainActivity)?.checkMenuCart()
    if (!isVisibleToUser) return
    restaurantViewModel.checkLoginChange()
    ScrollDepthFacade.resumeScrollDepth(binding.recyclerFloorList)
    onCascadeLifecycle(false)
  }

  override fun onStop() {
    super.onStop()
    restaurantViewModel.saveStopPoint(mContext)
    ratingJob?.cancel()
//    clearUpdate()
    onCascadeLifecycle(true)
    jumpSheetFragment?.dismissAllowingStateLoss()
    jumpSheetFragment = null
    sortSheetFragment?.dismissAllowingStateLoss()
    sortSheetFragment = null
  }

  private fun onCascadeLifecycle(isStop: Boolean) {
    val layoutManager = binding.recyclerFloorList.layoutManager
    if (layoutManager is LinearLayoutManager) {
      try {
        val firstIndex = layoutManager.findFirstVisibleItemPosition()
        val lastIndex = layoutManager.findLastCompletelyVisibleItemPosition()
        for (i in firstIndex..lastIndex) {
          val holder = binding.recyclerFloorList.findViewHolderForAdapterPosition(i)
          if (holder is RestaurantCascadeHolder) {
            if (isStop) {
              holder.onDetachedFromWindow()
            } else {
              holder.onAttachedToWindow()
            }
          }
        }
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  @OptIn(ExperimentalPagingApi::class)
  private fun setupRecyclerView() {

    var drawHeader = true

    val layoutManager = LinearLayoutManager(context)
    layoutManager.initialPrefetchItemCount = 0
    layoutManager.isItemPrefetchEnabled = false
    binding.recyclerFloorList.layoutManager = layoutManager

    restaurantDisplayAdapter = RestaurantGroupAdapter(
      viewLifecycleOwner,
      isRestaurantHome = true,
      cascadeStatePersist = cascadeSectionPersist,
      showTab = { tabs ->
        showTabsView(tabs)
      },
      groupJump = { groups, index ->
        showJumpSelectSheet(groups, index)
      },
      sort = { sorts ->
        showSortFragment(sorts)
      },
      itemClick = { model, foodName ->
        itemClick(model, foodName)
      },
      refreshView = { model ->
        refreshViewByHolder(model)
      },
      retry = { model ->
        retryBySort(model)
      },
      showAll = {
        layoutManager.scrollToPositionWithOffset(it, 0)
      }
    ).apply {
      addDataRefreshListener {
        drawHeader = false
        lifecycleScope.launch {
          delay(2000)
          drawHeader = true
        }
      }
    }

    binding.recyclerFloorList.apply {
      adapter = restaurantDisplayAdapter?.withLoadStateFooter(
        FooterLoadStateAdapter {
          restaurantDisplayAdapter?.retry()
        }
      )
      val itemDecoration = StickyHeaderDecoration(
        listener = restaurantDisplayAdapter!!,
        binding = {
          RestaurantItemGroupBinding.inflate(
            LayoutInflater.from(mContext),
            binding.root as ViewGroup,
            true
          ).apply {
            root.setBackgroundResource(com.ricepo.style.R.color.background)
          }
        },
        shouldDraw = {
          drawHeader
        }
      )
      this.addItemDecoration(itemDecoration)
    }

    lifecycleScope.launchWhenCreated {
      restaurantViewModel.restaurants.collectLatest {
        restaurantDisplayAdapter?.submitData(it)
      }
    }

    refreshStateFlow(layoutManager)

    binding.srlFloorList.setOnRefreshListener {
      showSearchView(View.GONE)
      sortPosition = SORT_INVALID
      binding.inRestaurantSort.clRestaurantSort.alpha = 0f
      binding.srlFloorList.disableHorizontalScroll = true
      // pull down refresh
      aRefreshRestaurant.onNext(true)
    }
  }

  private var sortSheetFragment: CategoryJumpFragment? = null

  private fun showSortFragment(sortTag: List<*>?) {
    val sorts = sortTag?.map {
      if (it is RestaurantSort) {
        it
      } else null
    }?.filterNotNull() ?: return

    mContext ?: return

    val sorted = sorts.filter { it.selected }.firstOrNull()
    sortPosition = sorted?.position ?: sortPosition

    val restModel = restaurantDisplayAdapter?.getItemModel(sortPosition)
    var lastModels: MutableList<RestaurantUiModel> = mutableListOf()
    val groupId = if (restModel is RestaurantUiModel.RestaurantCategory) {
      restModel.models?.let {
        lastModels.addAll(it)
      }
      restModel.group?.groupId
    } else null
    val sortSelected = if (restModel is RestaurantUiModel.RestaurantCategory) {
      restModel.sorted
    } else null
    val models = sorts.map { sort ->
      CategoryJumpData(
        title = sort.name?.localize(),
        sort = sort,
        sortGroupId = groupId,
        sortSelectedId = sortSelected?.id ?: sorted?.id
      )
    }

    sortSheetFragment = CategoryJumpFragment.newInstance(null, models)
    // need reset last models
    sortSheetFragment?.restUiModels = lastModels
    sortSheetFragment?.restUiModel = restModel
    sortSheetFragment?.sort = { restModels, model, restModel ->
      if (restModel is RestaurantUiModel.RestaurantCategory) {
        restModel.sorted = model.sort
      }
      if (model.sort?.order == 1) {
        binding.inRestaurantSort.ivRestaurantSort.setImageResource(com.ricepo.style.R.drawable.ic_restaurant_sortup)
        sortLayout?.findViewById<ImageView?>(R.id.iv_restaurant_sort)?.setImageResource(
          com.ricepo.style.R.drawable.ic_restaurant_sortup
        )
      } else {
        binding.inRestaurantSort.ivRestaurantSort.setImageResource(com.ricepo.style.R.drawable.ic_restaurant_sort)
        sortLayout?.findViewById<ImageView?>(R.id.iv_restaurant_sort)?.setImageResource(
          com.ricepo.style.R.drawable
            .ic_restaurant_sort
        )
      }
      // sort restaurant
      model.sort?.id?.let {
        if (model?.sortSelectedId == it) {
          // not refresh
        } else {
          val name = model.sort?.name?.localize()
          sortLayout?.findViewById<TextView?>(R.id.tv_restaurant_sort)?.text = name
          binding.inRestaurantSort.tvRestaurantSort.text = name
          restaurantViewModel.getSortRestaurants(restModels, model, restModel)
        }
      }
    }
    sortSheetFragment?.show(childFragmentManager, CategoryJumpFragment.TAG)
  }

  private val SORT_INVALID = -1
  private var sortPosition = SORT_INVALID
  private var sortLayout: ConstraintLayout? = null
  private var sortDatas: List<RestaurantSort>? = null

  private val onSortScrollListener = object : RecyclerView.OnScrollListener() {

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (newState == RecyclerView.SCROLL_STATE_IDLE) {
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is LinearLayoutManager) {
          val position = layoutManager.findFirstVisibleItemPosition()
          if (sortPosition != SORT_INVALID && sortPosition < position) {
            binding.inRestaurantSort.clRestaurantSort.alpha = 1f
            sortLayout?.alpha = 0f
            showSortFloatOnIdle(sortDatas, sortPosition)
          }
        }
      }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)

      val layoutManager = recyclerView.layoutManager
      if (layoutManager is LinearLayoutManager) {
        val position = layoutManager.findFirstVisibleItemPosition()
        showSortFloat(recyclerView, position)
      }
    }
  }

  private fun showSortFloat(recyclerView: RecyclerView, position: Int) {
    val viewHolder = recyclerView.findViewHolderForAdapterPosition(position) ?: return
    lifecycleScope.launch {
      if (viewHolder is RestaurantGroupHolder) {
        sortLayout = viewHolder.itemView.findViewById(R.id.cl_restaurant_sort)

        val sortTag = sortLayout?.tag
        if (sortTag is List<*>) {
          showSortFloatOnIdle(sortTag, position)

          val floatRect = Rect()
          binding.inRestaurantSort.clRestaurantSort.getGlobalVisibleRect(floatRect)

          val rect = Rect()
          sortLayout?.getGlobalVisibleRect(rect)

          if (rect.bottom < floatRect.bottom) {
            binding.inRestaurantSort.clRestaurantSort.alpha = 1f
            sortLayout?.alpha = 0f
          } else {
            binding.inRestaurantSort.clRestaurantSort.alpha = 0f
            sortLayout?.alpha = 1f
          }
        }
      }
      if (position < sortPosition) {
        binding.inRestaurantSort.clRestaurantSort.alpha = 0f
        sortLayout?.alpha = 1f
      } else {
      }
    }
  }

  private fun showSortFloatOnIdle(sortTag: List<*>?, position: Int) {
    val sorts = sortTag?.map {
      if (it is RestaurantSort) {
        it
      } else null
    }?.filterNotNull()
    val sorted = sorts?.firstOrNull { it.selected }

    if (sorted != null) {
      sortPosition = position
      binding.inRestaurantSort.tvRestaurantSort.text = sorted.name?.localize()
      if (sorted.order == 1) {
        binding.inRestaurantSort.ivRestaurantSort.setImageResource(com.ricepo.style.R.drawable.ic_restaurant_sortup)
      } else {
        binding.inRestaurantSort.ivRestaurantSort.setImageResource(com.ricepo.style.R.drawable.ic_restaurant_sort)
      }

      binding.inRestaurantSort.clRestaurantSort.tag = sorts
    }
  }

  private var jumpSheetFragment: CategoryJumpFragment? = null

  private fun showJumpSelectSheet(groups: List<RestaurantGroup>?, groupIndex: Int) {
    val context = mContext ?: return
    val models = groups?.filter { it.jumpData != null }?.map { group ->
      val jumpData = group.jumpData ?: CategoryJumpData()
      jumpData.title = group.name?.localize()
      jumpData.itemPosition = group.index
      jumpData
    } ?: return

    // don't cache the fragment avoid memory leaks
    jumpSheetFragment = CategoryJumpFragment.newInstance(null, models)
    jumpSheetFragment?.jump = { index ->
      val targetPosition = index ?: 0
      val layoutManager = binding.recyclerFloorList.layoutManager as LinearLayoutManager
      // inner method reference code
      val bottomScroller = TopSmoothScroller(context)
      bottomScroller.targetPosition = targetPosition
      layoutManager.startSmoothScroll(bottomScroller)
    }
    jumpSheetFragment?.show(childFragmentManager, CategoryJumpFragment.TAG)
  }

  /**
   * data refresh state changed listener
   */
  private fun refreshStateFlow(layoutManager: LinearLayoutManager) {
    lifecycleScope.launchWhenCreated {
      @OptIn(ExperimentalPagingApi::class)
      restaurantDisplayAdapter?.dataRefreshFlow?.collectLatest {
        // finish the pull refresh
        finishPullRefresh()

        val itemCount = restaurantDisplayAdapter?.itemCount ?: 0
        region@ for (i in 0 until itemCount) {
          val uiModel = restaurantDisplayAdapter?.getItemModel(i)
          if (uiModel is RestaurantUiModel.RestaurantVertical) {
            val regionId = uiModel.restaurant.region?.id
            if (regionId != null) {
              restaurantViewModel.regionId = regionId
              break@region
            }
          }
        }

        if (sortPosition != SORT_INVALID) {
          val layoutManager = binding.recyclerFloorList.layoutManager
          if (layoutManager is LinearLayoutManager) {
            // 3 is tab category and vertical
            if ((restaurantDisplayAdapter?.itemCount ?: 0) > 4) {
              val offset = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_10dp)
              layoutManager.scrollToPositionWithOffset(sortPosition, offset)
              // Can't be topped when there is little data
              layoutManager.stackFromEnd = true
            } else {
              context?.let {
                val topScroller = TopSmoothScroller(it, 1f)
                topScroller.targetPosition = sortPosition
                layoutManager.startSmoothScroll(topScroller)
              }
            }
            sortPosition = SORT_INVALID
          }

          // show tabs
          val uiModel = restaurantDisplayAdapter?.getItemModel(0)
          if (uiModel is RestaurantUiModel.RestaurantTabSection) {
            showTabsView(uiModel.tabs)
          }
        } else {
          // switch tab
          layoutManager.stackFromEnd = false
        }
      }
    }

    lifecycleScope.launchWhenCreated {
      restaurantDisplayAdapter?.loadStateFlow
        ?.distinctUntilChangedBy { it.refresh }
        ?.filter {
          it.refresh is LoadState.Loading &&
            binding.srlFloorList?.state != RefreshState.Refreshing
        }
        ?.collectLatest {
          if (sortPosition != SORT_INVALID) {
          } else {
            binding.recyclerFloorList?.alpha = 0f
            // hide the float sort button
            binding.inRestaurantSort.clRestaurantSort.alpha = 0f
          }
        }
    }

    lifecycleScope.launchWhenCreated {
      restaurantDisplayAdapter?.loadStateFlow
        ?.distinctUntilChangedBy { it.refresh }
        ?.filter { it.refresh is LoadState.NotLoading }
        ?.collectLatest {
          cascadeSectionPersist?.clearScrollState()
          binding.recyclerFloorList?.scrollToPosition(0)
          binding.recyclerFloorList?.alpha = 1f
        }
    }

    restaurantDisplayAdapter?.addLoadStateListener { loadState ->
      // pull to refresh not show screen loading
      val showLoading = binding.srlFloorList?.state != RefreshState.Refreshing
      if (loadState.refresh is LoadState.Loading && showLoading) {
        Loading.showLoading(activity)
        showSearchView(View.GONE)
        clearErrorView(binding.flRestaurantPage)
        mainViewModel.changeShowTab(false)
        cascadeSectionPersist?.clearScrollState()
      } else {
        binding.srlFloorList.isVisible = true
        if (isVisibleToUser) {
          Loading.hideLoading()
        }

        if (loadState.refresh is LoadState.Error) {
          val state = loadState.refresh as LoadState.Error
          if (state.error is NoSuchElementException) {
            showErrorEmptyView()
          } else {
            showErrorNetworkView(state.error.parseByBuzNetwork().message)
          }
          // finish the pull refresh
          finishPullRefresh()
          showSearchView(View.GONE)
        } else if (loadState.refresh is LoadState.NotLoading) {
          showSearchView(View.VISIBLE)
          binding.recyclerFloorList.post {
            PromotionMarqueeListener.setPromotionMarquee(binding.recyclerFloorList)
          }
        }
      }
    }
  }

  private fun finishPullRefresh() {
    binding.srlFloorList.finishRefresh()
    // finish refresh delayed 300ms
    binding.srlFloorList.postDelayed(
      {
        binding.srlFloorList.disableHorizontalScroll = false
      },
      300
    )
  }

  /**
   * refresh data  when sort all restaurants error
   */
  private fun retryBySort(model: RestaurantUiModel) {
    if (model is RestaurantUiModel.RestaurantErrorSection) {
      sortPosition = model.position ?: -1
      restaurantViewModel.getSortRestaurants(
        model.lastModels,
        model.request?.sortData, model.groupModel
      )
    }
  }

  private fun refreshViewByHolder(model: RestaurantUiModel) {
    if (model is RestaurantUiModel.RestaurantErrorSection) {
      binding.inRestaurantSort.clRestaurantSort.alpha = 0f
      sortLayout?.isVisible = true
      model.lastModels
        .filterIsInstance<RestaurantUiModel.RestaurantTabSection>()
        .firstOrNull()?.let {
          showTabsView(it.tabs)
        }
    } else if (model is RestaurantUiModel.RestaurantCategory) {
      binding.inRestaurantSort.clRestaurantSort.alpha = 0f
      sortPosition = model.position
      sortDatas = model.sorts
    }
  }

  private fun itemClick(model: RestaurantUiModel, foodName: String?) {
    when (model) {
      is RestaurantUiModel.RestaurantVertical -> {
        val searches = foodName?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenuForBusy(mContext, model.restaurant, searches)
      }
      is RestaurantUiModel.RestaurantBigImageVertical -> {
        FeaturePageRouter.navigateMenuForBusy(mContext, model.restaurant)
      }
      else -> { }
    }
  }

  private fun showTabsView(tabs: List<RestaurantTab>?) {
    val selectedTab = tabs?.firstOrNull { it.selected == true }
    if (selectedTab?.type == RestaurantTab.TYPE_PICKUP) {
      mainViewModel.refreshTabs(tabs)
      // 刷新 pickup fragment
      if (mContext is MainActivity) {
        val activity = mContext as MainActivity
        activity.refreshTab(RestaurantTab.TYPE_PICKUP)
      }
    } else {
      mainViewModel.refreshTabs(tabs)
      binding.srlFloorList.isVisible = true
    }
  }

  /**
   * when not get current address and deny permission show dialog prompt
   */
  private fun showEnterAddressDialog() {
    mContext?.let {
      DialogFacade.showAlert(
        it, com.ricepo.style.R.string.error_cannot_get_location,
        canCancel = false, isReset = true
      ) {
        // if dialog dismiss that first load is end
        restaurantViewModel.isLoadTrigger = true
        // context can be null if dialog not reset from share link
        mContext?.let { ct ->
          if (BuildConfig.DEBUG) {
            AddressCache.saveAddress(AddressCache.defaultAddress()!!) {
              aRefreshRestaurant.onNext(true)
            }
          } else {
            FeaturePageRouter.navigateAddress(ct)
          }
        }
      }
    }
  }

  private fun initRestaurantByLoc() {

    val thisActivity = mContext as Activity

    if (ContextCompat.checkSelfPermission(
        thisActivity,
        Manifest.permission.ACCESS_FINE_LOCATION
      )
      != PackageManager.PERMISSION_GRANTED
    ) {

      // Permission is not granted
      // Should we show an explanation?
      if (ActivityCompat.shouldShowRequestPermissionRationale(
          thisActivity,
          Manifest.permission.ACCESS_FINE_LOCATION
        )
      ) {
        // Show an explanation to the user *asynchronously* -- don't block
        // this thread waiting for the user's response! After the user
        // sees the explanation, try again to request the permission.
        showEnterAddressDialog()
      } else {
        requestLocationPermission()
      }
    } else {
      fetchCurrentPlace()
    }
  }

  private fun fetchCurrentPlace() {
    val context = mContext ?: return
    if (context is Activity) else return
    // loading
    Loading.showLoading(context)
    PlacesFacade.instance.fetchCurrentPlace(context)
      .doOnError {
        Loading.hideLoading()
      }
      .subscribe {
        if (it is FormatUserAddress && it.placeId != null) {
          Loading.hideLoading()
          AddressCache.saveAddress(it) {
            aRefreshRestaurant.onNext(true)
          }
          "success locating".log()
        } else {
          lifecycleScope.launch {
            var address: FormatUserAddress? = null
            try {
              val loc = GpsUtils.getCurrentLocation(context)
              if (loc != null) {
                val lat = loc.latitude
                val lng = loc.longitude
                val userAddress = googleMapsRestApi.fetchCurrentPlace(
                  "$lat,$lng"
                ).results.firstOrNull()
                address = FormatUserAddress(userAddress)
                // set address from gps location
                address.source = FormatUserAddress.SOURCE_GPS
              }
            } catch (e: Exception) {
              e.log("locating failed")
              address = null
              e.printStackTrace()
            } finally {
              "success locating".log()
              Loading.hideLoading()
              if (address?.formatted == null) {
                showEnterAddressDialog()
              } else {
                AddressCache.saveAddress(address) {
                  aRefreshRestaurant.onNext(true)
                }
              }
            }
          }
        }
      }
  }

  // Can request only one set of permissions at a time (dynamic link)
  private var isRequirePermission = false

  private fun requestLocationPermission() {
    if (isRequirePermission) return
    lifecycleScope.launch {
      isRequirePermission = true
      val permissionResult = PermissionManager.requestPermissions(
        this@RestaurantFloorFragment,
        REQUEST_CODE_LOCATION,
        Manifest.permission.ACCESS_FINE_LOCATION
      )
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          fetchCurrentPlace()
          isRequirePermission = false
        }
        is PermissionResult.ShowRational,
        is PermissionResult.PermissionDenied -> {
          showEnterAddressDialog()
          isRequirePermission = false
        }
        // first callback with long time not click
        is PermissionResult.PermissionDeniedPermanently -> {
          requestLocationPermission()
          isRequirePermission = false
        }
        else -> { isRequirePermission = false }
      }
    }
  }

  private fun showErrorEmptyView() {
    binding.srlFloorList.isVisible = false
    showErrorView(
      binding.flRestaurantPage, com.ricepo.style.R.drawable.ic_error_empty,
      com.ricepo.style.R.string.error_title_restaurant_not_found,
      getString(com.ricepo.style.R.string.error_restaurant_not_found), com.ricepo.style.R.string.try_other_address,
      isRemoveView = false,
      listener = View.OnClickListener {
        FeaturePageRouter.navigateRegionExplore(context)
      }
    )
  }

  private fun showErrorNetworkView(message: String?) {
    binding.srlFloorList.isVisible = false
    showErrorView(
      binding.flRestaurantPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      listener = View.OnClickListener {
        aRefreshRestaurant.onNext(true)
      }
    )
  }

  private fun setTitle(it: FormatUserAddress) {
    firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SELECT_CONTENT) {
      param(FirebaseAnalytics.Param.VALUE, it.name ?: "")
      param(FirebaseAnalytics.Param.ORIGIN, "activity is $activity")
      param(FirebaseAnalytics.Param.DESTINATION, "mContext is $mContext")
    }
    if (mContext is MainActivity) {
      val activity = mContext as MainActivity
      activity.setTitle(it.name)
    }
  }

  private fun showSearchView(visibility: Int) {
    if (mContext is MainActivity) {
      val activity = mContext as MainActivity
      activity.showSearchView(visibility)
    }
  }

  override fun refreshTab(type: String?) {
    if (type == RestaurantTab.TYPE_PICKUP) {
      if (mContext is MainActivity) {
        val activity = mContext as MainActivity
        activity.refreshTab(RestaurantTab.TYPE_PICKUP)
      }
    } else {
      restaurantViewModel.restaurantTabType = type
      refresh()
    }
  }

  override fun refreshTab(tab: HomeTab) {
  }

  fun refresh() {
    sortPosition = SORT_INVALID
    aRefreshRestaurant.onNext(true)
  }

  fun navigateSearch() {
    if (mContext is MainActivity) {
      val act = mContext as MainActivity
      val regionId = restaurantViewModel.regionId

      FeaturePageRouter.navigateRestaurantSearch(
        act, null, regionId, null
      )
    }
  }

  private fun checkPageStart(isRating: Boolean) {
    val context = mContext ?: return
//    val updateFlag = CommonPref.getCheckUpdate(context)
    lifecycleScope.launch {
      // checkout the group order
      val groupOrder = GroupOrderCache.getOrderSuspend()
      if (groupOrder != null) {
        FeaturePageRouter.navigateMenu(
          Restaurant(groupOrder.restaurant.id),
          groupId = groupOrder.groupId
        )
        return@launch
      }
      when {
//        updateFlag == null -> {
//          var updateMethod: String? = null
//          try {
//            val updateInfo = combineRestApi.checkUpdate()
//            updateMethod = updateInfo.update
//            // save update flag
//            CommonPref.saveCheckUpdate(context, updateMethod ?: "")
//          } catch (e: Exception) {
//            e.printStackTrace()
//          } finally {
//            if (updateMethod != null) {
//              checkUpdateFromStore(context, updateMethod)
//            } else {
//              restaurantViewModel.fetchPopup(isRating = isRating)
//            }
//          }
//        }
//        updateFlag == UpdateInfo.UPDATE_REQUIRED -> {
//          checkUpdateFromStore(context, updateFlag)
//        }
        isRating -> {
          checkRating(isRating)
        }
        else -> {
          restaurantViewModel.fetchPopup(isRating = isRating)
        }
      }
    }
  }

  private fun checkRating(
    isRating: Boolean,
  ) {
    val driverRatingFlag = CommonPref.gettDriverRating(requireContext())
    driverRatingFlag?.let {
      ratingJob = LifecycleNetworkListener.checkRatingOrder(lifecycleScope) { order ->
        order?.let {
          FeaturePageRouter.navigateDriverRating(
            requireContext(), it, 0,
            FeaturePageConst.REQUEST_CODE_DRIVER_RATING_RESUME, false
          )
        }
      }
      CommonPref.saveDriverRating(requireContext(), isRating.toString())
    }
  }

//  private suspend fun checkUpdateFromStore(context: Context, update: String?) {
//    if (update != null) {
//      // wait the address selected
//      withContext(Dispatchers.IO) {
//        AddressCache.getAddressSuspend()
//      } ?: return
//      // cancel rating driver job
//      ratingJob?.cancel()
//      // check update method
//      if (update == UpdateInfo.UPDATE_REQUIRED) {
//        DialogFacade.showAlert(context, ResourcesUtil.getString(com.ricepo.style.R.string.update_required)) {
//          checkPlayStore(true)
//        }
//      } else if (update == UpdateInfo.UPDATE_OPTIONAL) {
//        checkPlayStore(false)
//      }
//    }
    // check the app downloaded
//    appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
//      if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
//        // app downloaded to install
//        DialogFacade.showPrompt(
//          context,
//          ResourcesUtil.getString(com.ricepo.style.R.string.update_to_install)
//        ) {
//          appUpdateManager.completeUpdate()
//        }
//      }
//    }
//  }

//  private var updatedListener: InstallStateUpdatedListener? = null

//  private fun checkPlayStore(isForce: Boolean) {
//    val context = mContext ?: return
//
//    // Returns an intent object that you use to check for an update.
//    val appUpdateInfoTask = appUpdateManager.appUpdateInfo
//
//    val updateType = if (isForce) {
//      AppUpdateType.IMMEDIATE
//    } else {
//      // register downloaded listener
//      updatedListener = InstallStateUpdatedListener { state ->
//        Log.d("thom", "update install status ${state.installStatus()}")
//        if (state.installStatus() == InstallStatus.DOWNLOADED) {
//          DialogFacade.showPrompt(context, ResourcesUtil.getString(com.ricepo.style.R.string.update_to_install)) {
//            appUpdateManager.completeUpdate()
//          }
//        }
//      }
//      updatedListener?.let {
//        appUpdateManager.registerListener(it)
//      }
//
//      // for a flexible update
//      AppUpdateType.FLEXIBLE
//    }
//
//    appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
//      if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
//        appUpdateInfo.isUpdateTypeAllowed(updateType)
//      ) {
//        // Request the update.
//        startUpdate(appUpdateInfo, updateType)
//      } else {
//        if (updateType != AppUpdateType.IMMEDIATE) return@addOnSuccessListener
//        val intent = Intent(Intent.ACTION_VIEW).apply {
//          data = Uri.parse(
//            "https://play.google.com/store/apps/details?id=rocks.rice.app"
//          )
//          setPackage("com.android.vending")
//        }
//        startActivity(intent)
//      }
//    }
//  }
//
//  private fun startUpdate(appUpdateInfo: AppUpdateInfo, updateType: Int) {
//    (mContext as? Activity)?.let {
//      appUpdateManager.startUpdateFlowForResult(
//        // Pass the intent that is returned by 'getAppUpdateInfo()'.
//        appUpdateInfo,
//        // AppUpdateType.IMMEDIATE Or 'AppUpdateType.FLEXIBLE'
//        updateType,
//        // The current activity making the update request.
//        it,
//        // Include a request code to later monitor this update request.
//        UpdateInfo.REQUEST_CODE_APP_INSTALL
//      )
//    }
//  }
//
//  private fun clearUpdate() {
//    updatedListener?.let {
//      appUpdateManager.unregisterListener(it)
//    }
//  }
}
