package com.ricepo.app.restaurant.home

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.RestaurantUseCase
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.app.restaurant.usecase.PopupUseCase
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantRemoteSection
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.network.executor.PostExecutionThread
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.addTo
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flatMapMerge
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RestaurantViewModel @Inject constructor(
  private val postExecutionThread: PostExecutionThread,
  private val useCase: RestaurantUseCase,
  private val addressDao: AddressDao,
  private val popupUseCase: PopupUseCase
) : BaseViewModel() {

  data class Input(
    val lifecycleOwner: LifecycleOwner,
    val refreshRestaurant: Observable<Boolean>
  )

  data class Output(
    val liveAddress: MutableLiveData<FormatUserAddress?>
  )

  private val disposables = CompositeDisposable()

  private val liveAddress: MutableLiveData<FormatUserAddress?> = MutableLiveData()
  private var currentAddress: FormatUserAddress? = null

  var regionId: String? = null

  /**
   * trigger the restaurant refresh
   */
  var isLoadTrigger: Boolean = true

  /**
   * request restaurant tab type
   */
  var restaurantTabType: String? = null

  var location: String? = null

  val popupData = popupUseCase.popupData.asSharedFlow()

  val checkRating = popupUseCase.checkRating.asSharedFlow()

  fun fetchPopup(isRating: Boolean) {
    viewModelScope.launch {
      popupUseCase.fetchPopup(
        regionId,
        isRating = isRating
      )
    }
  }

  fun transform(input: Input): Output {

    input.refreshRestaurant
      .observeOn(postExecutionThread.ioScheduler)
      .subscribe {
        if (addressDao.getCount() == 0) {
          liveAddress.postValue(null)
        } else {
          val address = addressDao.getAddressLatest()
          if (address?.placeId != currentAddress?.placeId ||
            (address?.placeId == null && address?.formatted != currentAddress?.formatted) ||
            it
          ) {
            liveAddress.postValue(address)
            currentAddress = address
          } else {
            // first load end
            isLoadTrigger = true
          }
        }
      }
      .addTo(disposables)

    return Output(liveAddress)
  }

  data class RequestChannel(
    val groupModel: RestaurantUiModel?,
    val lastModels: MutableList<RestaurantUiModel> = mutableListOf(),
    val request: RestaurantRequest,
  )

  private val requestChannel = MutableSharedFlow<RequestChannel?>(1)

  @OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
  val restaurants = flowOf(
    requestChannel
      .filterNotNull()
      .flatMapLatest { channel ->
        useCase.getNearRestaurants(channel.request, channel.lastModels, channel.groupModel)
      }
  ).flatMapMerge { it }

  fun getNearRestaurants(address: FormatUserAddress) {

    viewModelScope.launch {
      location = address.location?.coordinates?.joinToString(",")
      requestChannel.emit(
        RequestChannel(
          null, mutableListOf(),
          RestaurantRequest(
            location, isRegion = true,
            type = restaurantTabType
          ),
        )
      )
    }
  }

  fun getSortRestaurants(
    models: MutableList<RestaurantUiModel>?,
    model: CategoryJumpData?,
    groupModel: RestaurantUiModel?
  ) {
    viewModelScope.launch {
      requestChannel.emit(
        RequestChannel(
          groupModel, models ?: mutableListOf(),
          RestaurantRequest(
            location,
            isRegion = true, type = restaurantTabType,
            groupId = model?.sortGroupId,
            sortData = model
          )
        )
      )
    }
  }

  fun getPickupRestaurants(
    activity: ComponentActivity,
    location: String?,
    errorHandle: () -> Unit
  ):
    Flow<RestaurantRemoteSection?> {
    return flowLoading(
      activity,
      error = { error ->
        DialogFacade.showAlert(activity, error.parseByBuzNetwork().message)
        errorHandle()
      }
    ) {
      val section = useCase.getPickupRestaurants(location ?: "")
      emit(section)
    }
  }

  /**
   * save the point of page stop
   */
  fun saveStopPoint(context: Context?) {
    val activity = context ?: return
    val point = System.currentTimeMillis()
    CommonPref.saveRestaurantPoint(activity, "$point")
  }

  fun checkStopPoint(context: Context?): Boolean {
    val activity = context ?: return false
    val point = CommonPref.getRestaurantPoint(activity) ?: return false
    val originPoint = point.toLong()
    val currentPoint = System.currentTimeMillis()
    val diff = currentPoint - originPoint
    // 30 minutes
    val baseline = 30 * 60 * 1000
    return (diff > baseline)
  }

  fun isClosed(item: Restaurant): Boolean {
    return useCase.isClosed(item)
  }

  override fun onCleared() {
    super.onCleared()
    useCase.dispose()
    disposables.dispose()
  }
}
