package com.ricepo.app.restaurant.home

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.imageview.ShapeableImageView
import com.ricepo.app.databinding.LayoutDialogPromoImageBinding
import com.ricepo.app.model.PopUp
import com.ricepo.app.model.localize
import com.ricepo.app.view.CircleShadowIndicator
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.youth.banner.adapter.BannerAdapter

private class PopupBannerAdapter(
  popups: List<PopUp>,
  val onSelected: (PopUp?) -> Unit
) : BannerAdapter<PopUp, PopupBannerAdapter.BannerVh>(popups) {

  class BannerVh(val view: ImageView) : RecyclerView.ViewHolder(view) {
    fun bind(
      data: PopUp?,
      click: () -> Unit
    ) {
      ImageLoader.loadLargeImage(
        imageView = view,
        url = data?.data?.image?.localize(),
        placeholderId = 0,
        maxWidth = 1200,
        maxHeight = 800,
        onSuccess = {
        },
        onError = { exception ->
          exception?.printStackTrace()
        }
      )
      view.clickWithTrigger {
        click.invoke()
      }
    }
  }

  override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerVh {
    val imageView = ShapeableImageView(
      parent.context
    ).apply {
      layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
      )
      scaleType = ImageView.ScaleType.CENTER_CROP
    }
    return BannerVh(imageView)
  }

  override fun onBindView(holder: BannerVh?, data: PopUp?, position: Int, size: Int) {
    holder?.bind(data = data) {
      onSelected.invoke(data)
    }
  }
}

fun RestaurantFloorFragment.showPromoImage(
  context: Context,
  popups: List<PopUp>
) {
  val builder = MaterialAlertDialogBuilder(context)
  builder.setCancelable(false)
  val promoBinding = LayoutDialogPromoImageBinding.inflate(
    LayoutInflater.from(context)
  )
  builder.setView(promoBinding.root)
  val dialog = builder.create()
  dialog.setCanceledOnTouchOutside(true)
  popups.forEach {
    it.data
  }
  with(promoBinding.banner) {
    setAdapter(PopupBannerAdapter(popups) {
      // todo maybe next version open this logic of code
//      val restaurantId = it?._id
//      restaurantId?.let {
//        FeaturePageRouter.navigateMenu(Restaurant(id = restaurantId))
//      }
//      dialog.dismiss()
    })
    indicator = CircleShadowIndicator(promoBinding.root.context)
  }
  promoBinding.ivPromoClose.clickWithTrigger {
    dialog.dismiss()
  }

  dialog.show()
}
