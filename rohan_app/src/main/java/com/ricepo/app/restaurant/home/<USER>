package com.ricepo.app.restaurant.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ricepo.app.HomeTab
import com.ricepo.app.R
import com.ricepo.app.compose.fontFamily
import com.ricepo.app.toUi
import com.ricepo.app.utils.activityContext
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.localize
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment
import com.skydoves.whatif.whatIfNotNull
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeSwitchModeBottomSheet(
  val tabs: List<HomeTab> = listOf(),
  private val onTabSelect: (RestaurantTab) -> Unit = {}
) : RoundedBottomSheetDialogFragment() {

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    return ComposeView(activityContext()).apply {
      tabs.filterIsInstance<HomeTab.Home>()
        .firstOrNull()
        ?.innerTabs
        .whatIfNotNull(
          whatIf = {
            setContent {
              MaterialTheme {
                HomeSwitchModal(
                  tabs = it,
                  onTabSelect = {
                    finish()
                    onTabSelect(it)
                  }
                ) {
                  finish()
                }
              }
            }
          },
          whatIfNot = {
            finish()
          }
        )
    }
  }

  fun finish() {
    dismiss()
  }
}

@Composable
fun HomeSwitchModal(
  tabs: List<RestaurantTab>,
  onTabSelect: (RestaurantTab) -> Unit,
  onClose: () -> Unit,
) {
  Column(
    modifier = Modifier.clip(
      RoundedCornerShape(
        topStart = dimensionResource(id = com.ricepo.style.R.dimen.card_radius),
        topEnd = dimensionResource(id = com.ricepo.style.R.dimen.card_radius)
      )
    )
  ) {
    TopBar(onClose = onClose)
    SwitchRow(
      tabs = tabs,
      onTabSelect = onTabSelect
    )
  }
}

@Composable
private fun SwitchRow(
  tabs: List<RestaurantTab>,
  onTabSelect: (RestaurantTab) -> Unit
) {
  BoxWithConstraints(
    modifier = Modifier
      .fillMaxWidth()
      .background(
        colorResource(id = com.ricepo.style.R.color.card_background)
      )
  ) {
    val itemWidth = with(LocalDensity.current) {
      (constraints.maxWidth / tabs.size).toDp()
    }
    Row(
      modifier = Modifier
        .padding(
          top = 42.dp, bottom = 101.dp
        )
        .fillMaxWidth(),
      horizontalArrangement = Arrangement.SpaceEvenly
    ) {
      tabs.forEach {
        TabItem(
          tab = it,
          width = itemWidth,
          onTabSelect = onTabSelect
        )
      }
    }
  }
}

@Composable
private fun TabItem(
  tab: RestaurantTab,
  width: Dp,
  shape: Shape = RoundedCornerShape(
    dimensionResource(id = com.ricepo.style.R.dimen.button_radius)
  ),
  onTabSelect: (RestaurantTab) -> Unit,
) {
  val ui = tab.toUi()
  Column(
    modifier = Modifier.width(width).clickable {
      onTabSelect.invoke(tab)
    }
  ) {
    Box(
      modifier = Modifier
        .size(60.dp)
        .align(Alignment.CenterHorizontally)
        .clip(shape = shape)
        .background(
          colorResource(
            id = if (tab.selected == true)
              com.ricepo.style.R.color.disable_mr
            else
              com.ricepo.style.R.color.background
          )
        )
    ) {
      Image(
        modifier = Modifier
          .size(35.dp)
          .align(Alignment.Center),
        painter = painterResource(id = ui.getCurrentImage()),
        contentDescription = ""
      )
    }
    Text(
      text = tab.name?.localize() ?: "",
      modifier = Modifier
        .align(Alignment.CenterHorizontally)
        .padding(top = 12.dp, start = 6.dp, end = 6.dp),
      color = colorResource(id = com.ricepo.style.R.color.mr),
      fontFamily = fontFamily,
      fontWeight = FontWeight.Bold,
      maxLines = 1,
      overflow = TextOverflow.Ellipsis
    )
  }
}

@Composable
private fun TopBar(
  onClose: () -> Unit
) {
  Box(
    modifier = Modifier
      .fillMaxWidth()
      .background(
        color = colorResource(id = com.ricepo.style.R.color.card_background)
      )
  ) {
    Text(
      text = stringResource(id = com.ricepo.style.R.string.mode_switch_popup_title),
      color = colorResource(id = com.ricepo.style.R.color.mainText),
      modifier = Modifier
        .align(
          Alignment.TopStart
        )
        .padding(
          horizontal = 36.dp,
          vertical = 20.dp
        ),
      style = TextStyle(
        fontSize = dimensionResource(id = com.ricepo.style.R.dimen.font_size_h2).value.sp,
      ),
      fontFamily = fontFamily,
      fontWeight = FontWeight.Bold
    )
    Image(
      modifier = Modifier
        .align(
          Alignment.TopEnd
        )
        .padding(
          horizontal = 36.dp,
          vertical = 19.dp
        )
        .clickable {
          onClose()
        },
      painter = painterResource(id = com.ricepo.style.R.drawable.ic_close_svg),
      colorFilter = ColorFilter.tint(colorResource(id = com.ricepo.style.R.color.fun_n6)),
      contentDescription = ""
    )
  }
}

@Composable
@Preview
private fun preview() {
  val tabs = remember {
    mutableStateListOf(
      RestaurantTab(
        name = InternationalizationContent.mock("test3")
      ),
    )
  }
  HomeSwitchModal(
    tabs = tabs,
    onTabSelect = {
      tabs.add(
        RestaurantTab(
          name = InternationalizationContent.mock("test1")
        )
      )
    }
  ) {
  }
}
