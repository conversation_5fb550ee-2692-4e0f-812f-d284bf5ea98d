package com.ricepo.app.restaurant.home

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.imageview.ShapeableImageView
import com.ricepo.app.databinding.LayoutDialogPromoImageBinding
import com.ricepo.app.model.PopUp
import com.ricepo.app.model.localize
import com.ricepo.app.view.CircleShadowIndicator
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.youth.banner.adapter.BannerAdapter

private class PopupBannerAdapter(
  popups: List<PopUp>,
  val onSelected: (PopUp?) -> Unit
) : BannerAdapter<PopUp, PopupBannerAdapter.BannerVh>(popups) {

  class BannerVh(val view: ImageView) : RecyclerView.ViewHolder(view) {
    fun bind(
      data: PopUp?,
      click: () -> Unit
    ) {
      // Use loadLargeImage with proportional scaling (FIT_CENTER)
      // This ensures the entire popup image is visible without cropping
      ImageLoader.loadLargeImage(
        imageView = view,
        url = data?.data?.image?.localize(),
        placeholderId = 0,
        maxWidth = if (view.width == 0) 1200 else view.width,
        maxHeight = if (view.height == 0) 800 else view.height,
        onSuccess = {
          // Image loaded successfully with proportional scaling
        },
        onError = { exception ->
          // Log error for debugging
          exception?.printStackTrace()
        }
      )
      view.clickWithTrigger {
        click.invoke()
      }
    }
  }

  override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerVh {
    val imageView = ShapeableImageView(
      parent.context
    ).apply {
      layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
      )
      // Use FIT_CENTER for proportional scaling without cropping
      // This will be set by loadLargeImage method, but we set it here as default
      scaleType = ImageView.ScaleType.FIT_CENTER
    }
    return BannerVh(imageView)
  }

  override fun onBindView(holder: BannerVh?, data: PopUp?, position: Int, size: Int) {
    holder?.bind(data = data) {
      onSelected.invoke(data)
    }
  }
}

fun RestaurantFloorFragment.showPromoImage(
  context: Context,
  popups: List<PopUp>
) {
  val builder = MaterialAlertDialogBuilder(context)
  builder.setCancelable(false)
  val promoBinding = LayoutDialogPromoImageBinding.inflate(
    LayoutInflater.from(context)
  )
  builder.setView(promoBinding.root)
  val dialog = builder.create()
  dialog.setCanceledOnTouchOutside(true)
  popups.forEach {
    it.data
  }
  with(promoBinding.banner) {
    setAdapter(PopupBannerAdapter(popups) {
      // todo maybe next version open this logic of code
//      val restaurantId = it?._id
//      restaurantId?.let {
//        FeaturePageRouter.navigateMenu(Restaurant(id = restaurantId))
//      }
//      dialog.dismiss()
    })
    indicator = CircleShadowIndicator(promoBinding.root.context)
  }
  promoBinding.ivPromoClose.clickWithTrigger {
    dialog.dismiss()
  }

  dialog.show()
}
