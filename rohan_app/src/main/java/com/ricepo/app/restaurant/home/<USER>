package com.ricepo.app.restaurant.home

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Point
import android.graphics.PointF
import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.eazypermissions.common.model.PermissionResult
import com.eazypermissions.coroutinespermission.PermissionManager
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.gms.maps.model.MarkerOptions
import com.mapbox.mapboxsdk.annotations.IconFactory
import com.mapbox.mapboxsdk.annotations.Marker
import com.mapbox.mapboxsdk.maps.MapboxMap
import com.ricepo.app.HomeTab
import com.ricepo.app.MainActivity
import com.ricepo.app.MainViewModel
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentRestaurantPickupBinding
import com.ricepo.app.databinding.LayoutMapLocationBinding
import com.ricepo.app.databinding.LayoutMapRestaurantBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.adapter.holder.RestaurantHorizontalUiModel
import com.ricepo.app.restaurant.home.adapter.holder.RestaurantPickupHorizontalAdapter
import com.ricepo.app.utils.OSUtils
import com.ricepo.base.animation.Loading
import com.ricepo.base.consts.TabMode
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantGroupType
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.PlacesFacade
import com.ricepo.map.extension.boundCenterAndPositions
import com.ricepo.map.extension.fromCenterAndPositions
import com.ricepo.map.extension.getCenterAndZoom
import com.ricepo.map.fragment.MapFragment
import com.ricepo.map.fragment.MapboxFragment
import com.ricepo.map.model.FormatLocation
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.model.MapUiModel
import com.ricepo.map.model.RestaurantMarketModel
import com.ricepo.map.overlay.PulseMarkerView
import com.ricepo.map.utils.GpsUtils
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.ricepo.style.view.rv.HorizontalRecyclerViewScrollListener
import com.ricepo.style.view.rv.TopSmoothScroller
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.subjects.BehaviorSubject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by Thomsen on 26/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@AndroidEntryPoint
class RestaurantPickupFragment :
  TabFragment(),
  OnMapReadyCallback,
  HorizontalRecyclerViewScrollListener.OnItemCoverListener {

  companion object {
    fun newInstance() = RestaurantPickupFragment()

    const val DEFAULT_MILES = 5
  }

  val ZOOM_LEVEL = 15f
  var zoomLevel = ZOOM_LEVEL

  val restaurantViewModel: RestaurantViewModel by viewModels()

  val mainViewModel: MainViewModel by activityViewModels()

  private lateinit var binding: FragmentRestaurantPickupBinding

  private var mContext: Context? = null

  // map for google or mapbox
  private var mMapUiModel: MapUiModel? = null

  private var realCenterLatLng: LatLng? = null

  private var screenCenterLatLng: LatLng? = null

  lateinit var aRefreshRestaurant: BehaviorSubject<Boolean>

  override fun onAttach(context: Context) {
    super.onAttach(context)
    mContext = context
  }

  override fun onCreateView(
    inflater: LayoutInflater,
    parent: ViewGroup?,
    bundle: Bundle?
  ): View? {
    binding = FragmentRestaurantPickupBinding.inflate(inflater)
    val snapHelper = GravitySnapHelper(Gravity.START)
    snapHelper.attachToRecyclerView(binding.rvRestaurantHorizontal)
    return binding.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    initMapFragment()
    fetchRestaurantByAddress(null, true, false)
    initListener()
    bindViewModel()
    // set alpha to hide init map
    view.alpha = 0f
    observeLoginLoad()
  }

  private fun observeLoginLoad() {
    restaurantViewModel.loginState.observe(
      this.viewLifecycleOwner
    ) {
      when {
        it.isFirstCreate -> {
          refreshTab(RestaurantTab.TYPE_PICKUP)
        }
        it.isLoginAndVipChange -> {
          // to reset the tab status
          refreshTab(null)
        }
        else -> {
          refreshTimer()
        }
      }
    }
  }

  override fun onStart() {
    super.onStart()
    if (!isVisibleToUser) return
    restaurantViewModel.checkLoginChange()
  }

  override fun onStop() {
    super.onStop()
    restaurantViewModel.saveStopPoint(mContext)
  }

  private fun initMapFragment() {

    if (OSUtils.isGMSInstalledAndEnabled()) {
      binding.layoutMapbox.isVisible = false
      binding.layoutMap.isVisible = true
      var mapFragment = childFragmentManager.findFragmentById(
        R.id.fragment_map
      ) as? MapFragment
      mapFragment?.getMapAsync(this)
    } else {
      binding.layoutMap.isVisible = false
      binding.layoutMapbox.isVisible = true
      var mapboxFragment = childFragmentManager.findFragmentById(
        R.id.fragment_mapbox
      ) as? MapboxFragment
      mapboxFragment?.onMapboxReady = { mapboxMap ->
        mMapUiModel = MapUiModel.Mapbox(mapboxMap)
        binding.mapOverlayBox.setupMap(mapboxMap)
        mapboxMap.uiSettings.run {
          isCompassEnabled = false
          isLogoEnabled = false
          isAttributionEnabled = false
        }
        // show location icon
        binding.mapLocation.isVisible = true
      }
    }
  }

  private fun initRestaurantByLoc(isInit: Boolean = true) {
    val context = mContext ?: return
    val thisActivity = context as Activity

    if (PackageManager.PERMISSION_GRANTED != ContextCompat.checkSelfPermission(
        thisActivity, Manifest.permission.ACCESS_FINE_LOCATION
      )
    ) {
      // Permission is not granted
      // Should we show an explanation?
      if (ActivityCompat.shouldShowRequestPermissionRationale(
          thisActivity,
          Manifest.permission.ACCESS_FINE_LOCATION
        )
      ) {
        if (isInit) {
          DialogFacade.showAlert(thisActivity, com.ricepo.style.R.string.pickup_map_location_failed) {
            removeAllView()
            fetchRestaurantByAddress(null, isInit)
          }
        } else {
          removeAllView()
          fetchRestaurantByAddress(null, isInit)
        }
      } else {
        requestLocationPermission()
      }
    } else {
      removeAllView()
      fetchCurrentPlace(isInit)
    }
  }

  private fun initListener() {
    binding.mapLocation.setOnClickListener() {
      initRestaurantByLoc(true)
      binding.mapSearch.isVisible = false
    }

    binding.mapSearch.setOnClickListener {
      removeAllView()
      val latLng = getScreenPointLatLng()
      if (latLng != null) {
        fetchRestaurantByLatLng(latLng, false)
        binding.mapSearch.isVisible = false
        setTitle(ResourcesUtil.getString(com.ricepo.style.R.string.pickup_map_custom_location))
      }
    }
  }

  private fun bindViewModel() {
    aRefreshRestaurant = BehaviorSubject.create()
    val viewModelInput = RestaurantViewModel.Input(viewLifecycleOwner, aRefreshRestaurant)
    val viewModelOutput = restaurantViewModel.transform(viewModelInput)
    viewModelOutput.liveAddress.observe(
      viewLifecycleOwner
    ) {
      if (it?.placeId == null && it?.formatted == null) {
        initRestaurantByLoc()
      } else {
        restaurantViewModel.isLoadTrigger = true
        setTitle(null, it)
        removeAllView()
        fetchRestaurantByLocation(it.location, true)
      }
    }
  }

  private fun removeAllView() {
    if (mMapUiModel is MapUiModel.Google) {
      val mapUiModel = mMapUiModel as MapUiModel.Google
      mapUiModel.map?.clear()
      binding.mapOverlay.removeAllMarker()
    }
    if (mMapUiModel is MapUiModel.Mapbox) {
      val mapUiModel = mMapUiModel as MapUiModel.Mapbox
      mapUiModel.map?.clear()
      binding.mapOverlayBox.removeAllViews()
    }
    binding.rvRestaurantHorizontal.adapter = RestaurantPickupHorizontalAdapter(listOf())
    binding.rvRestaurantHorizontal.isVisible = false
    binding.mapSearch.isVisible = false
  }

  private fun setTitle(title: String? = null, address: FormatUserAddress? = null) {
    if (mContext is MainActivity) {
      val activity = mContext as MainActivity
      activity.setTitle(title ?: address?.name ?: "")
    }
  }

  private fun fetchCurrentPlace(isInit: Boolean) {
    val context = mContext ?: return
    if (context is Activity) else return
    // loading
    PlacesFacade.instance.fetchCurrentPlace(context)
      .doOnSubscribe {
        Loading.showLoading(context)
      }
      .doOnError {
        Loading.hideLoading()
      }
      .subscribe {
        if (it is FormatUserAddress) {
          // get the pickup restaurant
          fetchRestaurantByAddress(it, isInit)
        } else {
          val loc = GpsUtils.getCurrentLocation(context)
          if (loc != null) {
            fetchRestaurantByAddress(
              FormatUserAddress(
                location = FormatLocation(
                  FormatLocation.TYPE_POINT, listOf(loc.longitude, loc.latitude)
                )
              ),
              isInit
            )
          } else {
            Loading.hideLoading()
            binding.mapSearch.isVisible = true
            DialogFacade.showAlert(context, com.ricepo.style.R.string.error_load_failed)
          }
        }
      }
  }

  private fun fetchRestaurantByAddress(
    currentAddress: FormatUserAddress?,
    isInit: Boolean,
    isByGps: Boolean = true
  ) {
    val context = mContext ?: return
    var curAddress = currentAddress

    lifecycleScope.launch {
      val address = withContext(Dispatchers.IO) {
        // no address was obtained when placeId is null
        if (currentAddress?.placeId != null) {
          currentAddress
        } else if (isByGps) {
          val loc = GpsUtils.getCurrentLocation(context)
          if (loc != null) {
            curAddress = FormatUserAddress(
              location = FormatLocation(
                FormatLocation.TYPE_POINT, listOf(loc.longitude, loc.latitude)
              )
            )
            curAddress
          } else {
            AddressCache.getAddress()
          }
        } else {
          AddressCache.getAddress()
        }
      }
      fetchRestaurantByLocation(address?.location, isInit)

      if (curAddress?.location?.coordinates != null) {
        setTitle(ResourcesUtil.getString(com.ricepo.style.R.string.pickup_map_current_location))
      } else {
        setTitle(null, address)
      }
    }
  }

  private fun fetchRestaurantByLocation(location: FormatLocation?, isInit: Boolean) {
    val lng = location?.coordinates?.getOrNull(0)
    val lat = location?.coordinates?.getOrNull(1)
    if (lng != null && lat != null) {
      realCenterLatLng = LatLng(lat, lng)
    }

    val loc = location?.loc()
    fetchPickupRestaurant(loc, isInit)
  }

  private fun fetchRestaurantByLatLng(latLng: LatLng, isInit: Boolean) {
    realCenterLatLng = latLng

    val loc = "${latLng.longitude},${latLng.latitude}"
    fetchPickupRestaurant(loc, isInit)
  }

  private fun fetchPickupRestaurant(loc: String?, isInit: Boolean) {
    val act = if (mContext is MainActivity) {
      mContext as MainActivity
    } else return
    Log.i("thom", "is $isVisibleToUser")
    if (isVisibleToUser || !isInit) else return
    showSearchView(View.GONE)
    mainViewModel.changeShowTab(false)
    lifecycleScope.launch {
      restaurantViewModel.getPickupRestaurants(
        act, loc,
        errorHandle = {
          moveMapAndAddMarker(null)
        }
      ).collectLatest { remoteSection ->
        if (remoteSection != null) {
          try {
            val tabs = remoteSection.tabs
            // filter the type with vertical in beluga
            val restaurants = remoteSection.sections?.filter { it.type == RestaurantGroupType.vertical }
              ?.lastOrNull()?.restaurants ?: listOf()
            // move the map location
            moveMapAndAddMarker(restaurants)

            if (tabs.isNullOrEmpty() && restaurants.isNullOrEmpty()) {
              // retain the query location
              return@collectLatest
            }

            if (tabs?.firstOrNull { it.selected == true }?.type != RestaurantTab.TYPE_PICKUP) {
              refreshTab(null)
              return@collectLatest
            }

            if (restaurants.isNotEmpty()) {
              showSearchView(View.VISIBLE)
            } else {
              showSearchView(View.GONE)
            }
            showRestaurants(restaurants)
//            showTabView(binding.inRestaurantTab, tabs)
            mainViewModel.refreshTabs(tabs)
          } catch (e: Exception) {
            e.printStackTrace()
            // move to center
            moveLocationToCenter(zoomLevel)
          }
        }
      }
    }
  }

  private fun moveLocationToCenter(
    zoomLevel: Float = ZOOM_LEVEL,
    bounds: List<LatLng>? = null
  ) {
    val latLng = realCenterLatLng ?: return
    val map = when (mMapUiModel) {
      is MapUiModel.Google -> {
        val model = mMapUiModel as MapUiModel.Google
        model.map
      }
      is MapUiModel.Mapbox -> {
        val model = mMapUiModel as MapUiModel.Mapbox
        val bounds = bounds?.map {
          com.mapbox.mapboxsdk.geometry.LatLng(it.latitude, it.longitude)
        }
        moveBoxLocationToCenter(model.map, zoomLevel, bounds)
        return
      }
      else -> return
    }
    val heightOffset = binding.root.measuredHeight / 4

    var level = zoomLevel
    if (!bounds.isNullOrEmpty()) {
      // 15.68 by real center region zoom
      val fiveMilesZoomLevel = 19 - Math.log(5 * 5.508)
      level = latLng.getCenterAndZoom(bounds, 14f).second

      val padding = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_24)
      map.moveCamera(
        CameraUpdateFactory.newLatLngBounds(
          latLng.boundCenterAndPositions(bounds), padding
        )
      )
      val boundLevel = map.cameraPosition.zoom
      level = level.coerceAtMost(boundLevel)
    }

    map.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, level))

    val projection = map.projection
    val mapPoint = projection.toScreenLocation(latLng)
    mapPoint.x += 0
    mapPoint.y += heightOffset
    screenCenterLatLng = projection.fromScreenLocation(mapPoint)
    map.moveCamera(CameraUpdateFactory.newLatLngZoom(screenCenterLatLng, level))
    try {
      realCenterLatLng?.let {
        map.addMarker(
          MarkerOptions().position(it)
            .anchor(0.5f, 1f)
            .icon(BitmapDescriptorFactory.fromBitmap(createLocationDrawableFromView()))
        )
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  var boxLocMarker: Marker? = null

  private fun moveBoxLocationToCenter(
    map: MapboxMap,
    zoomLevel: Float = ZOOM_LEVEL,
    bounds: List<com.mapbox.mapboxsdk.geometry.LatLng>? = null
  ) {
    val realCenterLatLng = realCenterLatLng ?: return
    val heightOffset = binding.root.measuredHeight / 4

    val realLatLng = com.mapbox.mapboxsdk.geometry.LatLng(
      realCenterLatLng.latitude, realCenterLatLng.longitude
    )

    var level = zoomLevel
    if (!bounds.isNullOrEmpty()) {
      // 15.68 by real center region zoom
      val fiveMilesZoomLevel = 19 - Math.log(5 * 5.508)
      level = realLatLng.getCenterAndZoom(bounds, 14f).second

      val padding = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_24)
      map.moveCamera(
        com.mapbox.mapboxsdk.camera.CameraUpdateFactory.newLatLngBounds(
          realLatLng.boundCenterAndPositions(bounds), padding
        )
      )
      val boundLevel = map.cameraPosition.zoom
      level = level.coerceAtMost(boundLevel.toFloat())
    }

    map.moveCamera(com.mapbox.mapboxsdk.camera.CameraUpdateFactory.newLatLngZoom(realLatLng, level.toDouble()))

    val projection = map.projection
    val mapPoint = projection.toScreenLocation(realLatLng)
    mapPoint.x += 0
    mapPoint.y += heightOffset
    val centerLatLng = projection.fromScreenLocation(mapPoint)
    screenCenterLatLng = LatLng(centerLatLng.latitude, centerLatLng.longitude)

    map.moveCamera(com.mapbox.mapboxsdk.camera.CameraUpdateFactory.newLatLngZoom(centerLatLng, level.toDouble()))

    try {
      val context = mContext ?: return
      val bitmap = createLocationDrawableFromView() ?: return
      val iconFactory = IconFactory.getInstance(context)
      boxLocMarker?.let {
        map.removeMarker(it)
      }
      boxLocMarker = map.addMarker(
        com.mapbox.mapboxsdk.annotations.MarkerOptions().position(realLatLng)
          .icon(iconFactory.fromBitmap(bitmap))
      )
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun getScreenPointLatLng(): LatLng? {
    val screenWidth = DisplayUtil.getScreenWidth()
    val screenHeight = binding.root.measuredHeight
    val x = (screenWidth / 2)
    val y = (screenHeight / 4)
    return if (mMapUiModel is MapUiModel.Google) {
      val screenPoint = Point(x, y)
      val model = mMapUiModel as MapUiModel.Google
      val map = model.map
      map.projection?.fromScreenLocation(screenPoint)
    } else if (mMapUiModel is MapUiModel.Mapbox) {
      val screenPoint = PointF(x.toFloat(), y.toFloat())
      val model = mMapUiModel as MapUiModel.Mapbox
      val map = model.map
      map.projection.fromScreenLocation(screenPoint).let {
        LatLng(it.latitude, it.longitude)
      }
    } else {
      return null
    }
  }

  private fun showRestaurants(restaurants: List<Restaurant>) {
    binding.rvRestaurantHorizontal.isVisible = true

    val uiModels = restaurants.map { restaurant ->
      RestaurantHorizontalUiModel.RestaurantItem(
        restaurant,
        RestaurantRemoteGroup(), 0
      )
    }

    val snapHelper = GravitySnapHelper(Gravity.START)
    snapHelper.maxFlingSizeFraction = 0.8f
    snapHelper.scrollMsPerInch = 50f
    snapHelper.attachToRecyclerView(binding.rvRestaurantHorizontal)

    binding.rvRestaurantHorizontal.layoutManager = LinearLayoutManager(
      binding.root.context,
      LinearLayoutManager.HORIZONTAL, false
    )

    val adapter = RestaurantPickupHorizontalAdapter(uiModels) { model, foodName ->
      if (model is RestaurantHorizontalUiModel.RestaurantItem) {
        val location = if (null != realCenterLatLng) {
          "${realCenterLatLng?.longitude},${realCenterLatLng?.latitude}"
        } else {
          null
        }
        val searches = foodName?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenuForBusy(
          mContext, model.restaurant, searches,
          deliveryMode = TabMode.MODE_PICKUP,
          location = location
        )
      }
    }
    binding.rvRestaurantHorizontal.adapter = adapter
    // for image background and placeholder cached 0
    binding.rvRestaurantHorizontal.recycledViewPool.setMaxRecycledViews(
      R.layout.restaurant_pickup_item_container, 0
    )

    binding.rvRestaurantHorizontal.addOnScrollListener(horizontalScrollListener)

    // promotion marquee
    binding.rvRestaurantHorizontal.addOnScrollListener(PromotionMarqueeListener())
    binding.rvRestaurantHorizontal.post {
      PromotionMarqueeListener.setPromotionMarquee(binding.rvRestaurantHorizontal)
    }

    // scroll listener
    binding.rvRestaurantHorizontal.addOnScrollListener(object : RecyclerView.OnScrollListener() {
      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
          if (isClickMove) {
            recyclerView.postDelayed({ isClickMove = false }, 300)
          }
        }
      }
    })
  }

  private val horizontalScrollListener = HorizontalRecyclerViewScrollListener(this)

  private var mRestaurants: List<Restaurant>? = null

  private fun moveMapAndAddMarker(restaurants: List<Restaurant>?) {

    val bounds = mutableListOf<LatLng>()
    restaurants?.forEach { restaurant ->
      val coordinates = restaurant.address?.innerItemValue?.location?.coordinates
      val lng = coordinates?.getOrNull(0)
      val lat = coordinates?.getOrNull(1)
      if (lng != null && lat != null) {
        val latLng = LatLng(lat, lng)
        bounds.add(latLng)
      }
    }

    binding.mapOverlay.setOnCameraIdleListener {
      binding.mapOverlay.removeAllMarker()
      restaurants?.forEachIndexed { index, restaurant ->
        val coordinates = restaurant.address?.innerItemValue?.location?.coordinates
        val lng = coordinates?.getOrNull(0)
        val lat = coordinates?.getOrNull(1)
        if (lng != null && lat != null) {
          // java.lang.IllegalStateException: no included points
          val latLng = LatLng(lat, lng)
          val isClosed = restaurantViewModel.isClosed(restaurant)
          val imgAlpha = if (isClosed) {
            if (ThemeUtil.isDarkMode()) 0.6f else 0.7f
          } else {
            1f
          }
          val model = RestaurantMarketModel(
            index, restaurant.name?.localize(),
            restaurant.image?.localize(), isClosed, imgAlpha
          )
          binding.mapOverlay.createAndShowMarker(latLng, model)
        }
      }
      binding.mapOverlay.setOnCameraIdleListener(null)
      // default show marker animation
      if (restaurants?.isNotEmpty() == true) {
        binding.mapOverlay.showMarker(0)
      }

      binding.mapSearch.isVisible = restaurants.isNullOrEmpty()
      // listener the map move and zoom
      binding.mapOverlay.setOnCameraMoveListener {
        binding.mapOverlay.refresh()
        // show search button
        binding.mapSearch.isVisible = true
      }

      // set alpha to show map
      view?.alpha = 1f
    }

    mRestaurants = restaurants
    binding.mapOverlayBox.setOnCameraIdleListener(onBoxCameraIdleListener)
    view?.postDelayed(
      {
        if (view?.alpha == 1f) {
          // remove idle listener
          binding.mapOverlayBox.removeOnCameraIdleListener(onBoxCameraIdleListener)
        }
      },
      1000
    )

    // scroll restaurant list when marker clicked
    binding.mapOverlay.setOnMarkerClickListener {
      val position = it.position
      binding.mapOverlay.showMarker(position)
      isClickMove = true
      binding.rvRestaurantHorizontal.smoothScrollToPosition(position)
      mContext?.let {
        val topSmoothScroller = TopSmoothScroller(it)
        topSmoothScroller.targetPosition = position
        val layoutManager = binding.rvRestaurantHorizontal.layoutManager
        layoutManager?.startSmoothScroll(topSmoothScroller)
      }
    }

    binding.mapOverlayBox.setOnMarkerClickListener {
      val position = it.position
      binding.mapOverlayBox.showMarker(position)
      isClickMove = true
      mContext?.let {
        val topSmoothScroller = TopSmoothScroller(it)
        topSmoothScroller.targetPosition = position
        val layoutManager = binding.rvRestaurantHorizontal.layoutManager
        layoutManager?.startSmoothScroll(topSmoothScroller)
      }
    }

    // move to center
    moveLocationToCenter(zoomLevel, bounds)
  }

  private var isClickMove = false

  private val onBoxCameraIdleListener = MapboxMap.OnCameraIdleListener {
    binding.mapOverlayBox.removeAllMarker()
    mRestaurants?.forEachIndexed { index, restaurant ->
      val coordinates = restaurant.address?.innerItemValue?.location?.coordinates
      val lng = coordinates?.getOrNull(0)
      val lat = coordinates?.getOrNull(1)
      if (lng != null && lat != null) {
        // java.lang.IllegalStateException: no included points
        val latLng = com.mapbox.mapboxsdk.geometry.LatLng(lat, lng)
        val isClosed = restaurantViewModel.isClosed(restaurant)
        val imgAlpha = if (isClosed) {
          if (ThemeUtil.isDarkMode()) 0.6f else 0.7f
        } else {
          1f
        }
        val model = RestaurantMarketModel(
          index, restaurant.name?.localize(),
          restaurant.image?.localize(), isClosed, imgAlpha
        )
        binding.mapOverlayBox.createAndShowMarker(latLng, model)
      }
    }
    // default show marker animation
    if (mRestaurants?.isNotEmpty() == true) {
      binding.mapOverlayBox.showMarker(0)
    }

    binding.mapSearch.isVisible = mRestaurants.isNullOrEmpty()
    view?.alpha = 1f

    // listener the map move and zoom
    binding.mapOverlayBox.setOnCameraMoveListener(onBoxCameraMoveListener)
  }

  private val onBoxCameraMoveListener = MapboxMap.OnCameraMoveListener {

    binding.mapOverlayBox.refresh()
    // show search button
    binding.mapSearch.isVisible = true
  }

  private fun addMarker(restaurants: List<Restaurant>) {

    val map = if (mMapUiModel is MapUiModel.Google) {
      val model = mMapUiModel as MapUiModel.Google
      model.map
    } else return

    val builder = LatLngBounds.Builder()

    val bounds = mutableListOf<LatLng>()

    restaurants.forEach { restaurant ->
      val coordinates = restaurant.address?.innerItemValue?.location?.coordinates
      val lng = coordinates?.getOrNull(0)
      val lat = coordinates?.getOrNull(1)
      if (lng != null && lat != null) {
        val latLng = LatLng(lat, lng)
        bounds.add(latLng)
        map.addMarker(
          MarkerOptions().position(latLng).icon(
            BitmapDescriptorFactory
              .fromBitmap(createRestaurantDrawableFromView(restaurant))
          )
        )
        builder.include(latLng)
      }
    }

    // bounds
    val latLng = realCenterLatLng
    val cameraUpdate = if (latLng != null) {
      CameraUpdateFactory.newLatLngBounds(
        latLng
          .fromCenterAndPositions(bounds),
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_24)
      )
    } else {
      CameraUpdateFactory.newLatLngBounds(
        builder.build(),
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_24)
      )
    }
    map.moveCamera(cameraUpdate)
    val zoomLevel = map.cameraPosition?.zoom ?: ZOOM_LEVEL

    // move to center
    moveLocationToCenter(zoomLevel)
  }

  private fun createLocationDrawableFromView(): Bitmap? {
    val inflater = mContext?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) ?: return null
    inflater as LayoutInflater
    // onGetLayoutInflater() cannot be executed until the Fragment is attached to the FragmentManager.
    val binding = LayoutMapLocationBinding.inflate(inflater)

    binding.ivLocationImg.setImageResource(com.ricepo.style.R.drawable.ic_center_point)

    val view = binding.root
    view.layoutParams = ConstraintLayout.LayoutParams(
      ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_40),
      ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_40)
    )

    val displayMetrics = DisplayMetrics()
    (mContext as Activity).windowManager.defaultDisplay.getMetrics(displayMetrics)
    view.measure(displayMetrics.widthPixels, displayMetrics.heightPixels)
    view.layout(0, 0, displayMetrics.widthPixels, displayMetrics.heightPixels)

    view.buildDrawingCache()

    val bitmap = Bitmap.createBitmap(
      view.measuredWidth,
      view.measuredHeight, Bitmap.Config.ARGB_8888
    )

    val canvas = Canvas(bitmap)
    view.draw(canvas)

    return bitmap
  }

  private fun createRestaurantDrawableFromView(restaurant: Restaurant): Bitmap {
    val binding = LayoutMapRestaurantBinding.inflate(layoutInflater)
    binding.tvRestaurantName.text = restaurant.name?.localize()

    ImageLoader.load(binding.ivRestaurantImg, restaurant.image?.localize())

    val view = binding.root
    view.layoutParams = ConstraintLayout.LayoutParams(
      ConstraintLayout.LayoutParams.WRAP_CONTENT,
      ConstraintLayout.LayoutParams.WRAP_CONTENT
    )

    val displayMetrics = DisplayMetrics()
    (mContext as Activity).windowManager.defaultDisplay.getMetrics(displayMetrics)
    view.measure(displayMetrics.widthPixels, displayMetrics.heightPixels)
    view.layout(0, 0, displayMetrics.widthPixels, displayMetrics.heightPixels)

    view.buildDrawingCache()

    val bitmap = Bitmap.createBitmap(
      view.measuredWidth,
      view.measuredHeight, Bitmap.Config.ARGB_8888
    )

    val canvas = Canvas(bitmap)
    view.draw(canvas)

    return bitmap
  }

  // Can request only one set of permissions at a time (dynamic link)
  private var isRequirePermission = false

  private val REQUEST_CODE_LOCATION: Int = 1003

  private fun requestLocationPermission() {
    if (isRequirePermission) return
    lifecycleScope.launch {
      isRequirePermission = true
      val permissionResult = PermissionManager.requestPermissions(
        this@RestaurantPickupFragment,
        REQUEST_CODE_LOCATION,
        Manifest.permission.ACCESS_FINE_LOCATION
      )
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          fetchCurrentPlace(true)
          isRequirePermission = false
        }
        is PermissionResult.ShowRational,
        is PermissionResult.PermissionDenied -> {
          fetchRestaurantByAddress(null, true)
          isRequirePermission = false
        }
        // first callback with long time not click
        is PermissionResult.PermissionDeniedPermanently -> {
          requestLocationPermission()
          isRequirePermission = false
        }
        else -> { isRequirePermission = false }
      }
    }
  }

  override fun onMapReady(map: GoogleMap?) {
    map ?: return
    mMapUiModel = MapUiModel.Google(map)
    binding.mapOverlay.setupMap(map)
    map.uiSettings.isCompassEnabled = false

    // show location icon
    binding.mapLocation.isVisible = true
  }

  override fun onItemCover(position: Int) {
    if (isClickMove) return
    when (mMapUiModel) {
      is MapUiModel.Google -> {
        binding.mapOverlay.showMarker(position)
        moveToVisible(position)
      }
      is MapUiModel.Mapbox -> {
        val map = (mMapUiModel as MapUiModel.Mapbox).map
        moveToBoxVisible(map, position)
      }
      else -> {

      }
    }
  }

  private fun moveToBoxVisible(map: MapboxMap, position: Int) {
    binding.mapOverlayBox.showMarker(position)
    // y = top + translationY
    val topY = (
      binding.rvRestaurantHorizontal.y -
        DisplayUtil.dp2Px(PulseMarkerView.POINT_HEIGHT.toFloat())
      ).toInt()
    if (!binding.mapOverlayBox.isMarkerVisibleByTop(position, topY)) {
      val latLng = binding.mapOverlayBox.getMarker(position)?.latLng()
      if (latLng != null) {
        moveBoxLocationToCenter(map, zoomLevel, listOf(latLng))
        binding.mapOverlay.refresh()
      }
    }
  }

  private fun moveToVisible(position: Int) {
    // y = top + translationY
    val topY = (
      binding.rvRestaurantHorizontal.y -
        DisplayUtil.dp2Px(PulseMarkerView.POINT_HEIGHT.toFloat())
      ).toInt()
    if (!binding.mapOverlay.isMarkerVisibleByTop(position, topY)) {
      val latLng = binding.mapOverlay.getMarker(position)?.latLng()
      if (latLng != null) {
        moveLocationToCenter(zoomLevel, listOf(latLng))
        binding.mapOverlay.refresh()
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_ADDRESS) {
        aRefreshRestaurant.onNext(true)
      }
    }
  }

  fun navigateSearch() {
    (mContext as? MainActivity)?.let { act ->
      val adapter = binding.rvRestaurantHorizontal.adapter
      var regionId: String? = null
      if (adapter is RestaurantPickupHorizontalAdapter) {
        val uiModel = adapter.getItemModel(0)
        regionId = if (uiModel is RestaurantHorizontalUiModel.RestaurantItem) {
          uiModel.restaurant.region?.id
        } else {
          null
        }
      }
      val location = if (null != realCenterLatLng) {
        "${realCenterLatLng?.longitude},${realCenterLatLng?.latitude}"
      } else {
        null
      }
      FeaturePageRouter.navigateRestaurantSearch(
        act, location, regionId, DEFAULT_MILES
      )
    }
  }

  override fun refreshTab(type: String?) {
    if (type == RestaurantTab.TYPE_PICKUP) {
      // current click to not refresh
      if (type != restaurantViewModel.restaurantTabType) {
        // hide restaurant and marker to refresh
        binding.rvRestaurantHorizontal.isVisible = false
        removeAllView()
        // need before refresh
        restaurantViewModel.restaurantTabType = type
        refresh()
      }
    } else {
      restaurantViewModel.restaurantTabType = null
    }
  }

  override fun refreshTab(tab: HomeTab) {
  }

  /**
   * switch toggle refresh
   */
  private fun refresh() {
    try {
      fetchRestaurantByAddress(null, false, false)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  /**
   * 30 minutes refresh
   */
  private fun refreshTimer() {
    val refresh = restaurantViewModel.checkStopPoint(mContext)
    if (refresh) {
      realCenterLatLng?.let {
        removeAllView()
        fetchRestaurantByLatLng(it, false)
      }
    }
  }

  private fun showSearchView(visibility: Int) {
    if (mContext is MainActivity) {
      val activity = mContext as MainActivity
      activity.showSearchView(visibility)
    }
  }
}
