package com.ricepo.app.features.recommend.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.features.recommend.RecommendUiModel
import com.ricepo.base.adapter.EmptyViewHolder

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RecommendAdapter(
  private val dataList: List<RecommendUiModel>,
  private val itemClick: (RecommendUiModel) -> Unit
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun getItemCount() = dataList.size

  override fun getItemViewType(position: Int): Int {
    if (position >= itemCount) return 0
    val mode = dataList.get(position)
    return when (mode) {
      is RecommendUiModel.TitleItem -> R.layout.recommend_item_title
      is RecommendUiModel.FoodItem -> R.layout.recommend_item_food
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.recommend_item_title -> RecommendTitleHolder.create(parent)
      R.layout.recommend_item_food -> RecommendFoodHolder.create(parent)
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    when (val uiModel = dataList[position]) {
      is RecommendUiModel.TitleItem -> (holder as RecommendTitleHolder).bind(uiModel)
      is RecommendUiModel.FoodItem -> (holder as RecommendFoodHolder).bind(uiModel)
    }
  }
}
