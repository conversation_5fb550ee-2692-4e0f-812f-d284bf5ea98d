package com.ricepo.app.features.support

import com.ricepo.app.model.Order
import com.ricepo.app.model.SupportItem
import com.ricepo.app.model.SupportReq
import com.ricepo.app.restapi.CombineRestApi
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File
import javax.inject.Inject

class OrderSupportRepo @Inject constructor(
  private val api: CombineRestApi
) {
  /**
   * get choosed food items
   */
  fun getItemSection(order: Order): List<ChoosedItem>? {
    return order.items?.fold(mutableListOf()) { result, item ->
      val currentItems: MutableList<ChoosedItem> = result
      for (i in 1..(item.qty ?: 0)) {
        val choosedItem = ChoosedItem.Food(item, false)
        currentItems.add(choosedItem)
      }
      currentItems
    }
  }

  /**
   * Get all customer processing methods,
   * All methods are as follows:
   * 1. Get Refund
   * 2. Redelivery
   * 3. Other
   */
  fun getProcessingMethodSection(supportItem: SupportItem): List<ChoosedItem>? {
    return supportItem.chooseProcessingContent?.fold(mutableListOf()) { result, item ->
      result.add(ChoosedItem.Method(item, false))
      result
    }
  }

  /**
   * get choosed problem items
   */
  fun getProblemSection(supportItem: SupportItem): List<ChoosedItem>? {
    return supportItem.chooseItemContent?.fold(mutableListOf()) { result, item ->
      result.add(ChoosedItem.Problem(item, false))
      result
    }
  }

  /**
   * subject: The text that user entered on the help page
   */
  suspend fun createTicket(orderId: String, supportReq: SupportReq): Any {
    return api.createTicket(orderId, supportReq)
  }

  suspend fun uploadImage(path: String): String {
    val file = File(path)
    val requestFile = RequestBody.create("multipart/form-data".toMediaTypeOrNull(), file)
    // multipartbody part is used to send also the actual file name
    val fileBody = MultipartBody.Part.createFormData("image", file.name, requestFile)
    val description = RequestBody.create("multipart/form-data".toMediaTypeOrNull(), "choose item image desc")
//        val body = mapOf<String, String>("imageType" to "support-images")\
    val imageType = "support-images"
    val typeBody = MultipartBody.Part.createFormData("type", imageType)
    return api.uploadImage(description, listOf(fileBody, typeBody))
  }
}
