package com.ricepo.app.features.checkout.comment

import android.os.Bundle
import android.text.TextUtils
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityCheckoutCommentBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 2/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_CHECKOUT_COMMENT)
class CommentActivity : BaseActivity() {

  private lateinit var binding: ActivityCheckoutCommentBinding

  private var restaurantId: String? = null

  @Inject
  lateinit var combineRestApi: CombineRestApi

  // no inject also to add activity module

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityCheckoutCommentBinding.inflate(layoutInflater)
    setContentView(binding.root)

    restaurantId = intent.getStringExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_RESTAURANT_ID)
    val comments = intent.getStringExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_COMMENT) ?: ""
    binding.etComment?.setText(comments)

    setupListener()

    getHistoryComments()
  }

  private fun getHistoryComments() {
    val restaurantId = restaurantId ?: return
    lifecycleScope.launch {
      flowLoading(this@CommentActivity) {
        emit(combineRestApi.getHistoryComments(restaurantId))
      }.collectLatest {
        showHistoryComments(it)
      }
    }
  }

  private fun showHistoryComments(comments: List<String>) {
    val historyAdapter = BindListAdapter(
      comments, com.ricepo.base.R.layout.view_string_item,
      object : OnBindViewListener<TextView, String> {
        override fun onBindView(view: TextView, value: String?, position: Int) {
          var params = RecyclerView.LayoutParams(
            RecyclerView.LayoutParams.WRAP_CONTENT,
            RecyclerView.LayoutParams.WRAP_CONTENT
          )
          params.topMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_15)
          view.layoutParams = params
          view.text = value
          view.maxLines = 2
          view.ellipsize = TextUtils.TruncateAt.END
          view.setOnClickListener {
            binding.etComment.setText(value)
            binding.etComment.setSelection(value?.length ?: 0)
          }
        }
      }
    )
    binding.rvCommentHistory.adapter = historyAdapter
  }

  override fun onResume() {
    super.onResume()
    KeyboardUtil.showKeyboard(this, binding.etComment)
  }

  private fun setupListener() {

    binding.btnSaveComment?.clickWithTrigger {
      val resultIntent = intent.putExtra(
        FeaturePageConst.PARAM_PAGE_CHECKOUT_COMMENT,
        binding.etComment?.text.toString()
      )
      backResultEvent(resultIntent)
    }
  }
}
