package com.ricepo.app.features.subscription.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.SubscriptionDetailsItemBinding
import com.ricepo.base.model.SubscriptionPlanDesc
import com.ricepo.base.model.localize
import com.ricepo.style.DisplayUtil

//
// Created by <PERSON><PERSON> on 23/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class SubscriptionDetailsAdapter(private val models: List<SubscriptionPlanDesc>) :
  RecyclerView.Adapter<SubscriptionDetailHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SubscriptionDetailHolder {
    // reset density for viewHolder create view
    DisplayUtil.setDensity(parent.resources)
    return SubscriptionDetailHolder.create(parent)
  }

  override fun onBindViewHolder(holder: SubscriptionDetailHolder, position: Int) {
    DisplayUtil.logDensity(holder.itemView.context)
    holder.bind(models.get(position))
  }

  override fun getItemCount(): Int = models.size
}

class SubscriptionDetailHolder(private var binding: SubscriptionDetailsItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(item: SubscriptionPlanDesc) {
    binding.tvSubscriptionTitle.text = item.title?.localize()?.replace("\\n", "\n")
    binding.tvSubscriptionSubtitle.isVisible = (item.subTitle?.localize()?.isNotBlank() == true)
    binding.tvSubscriptionSubtitle.text = item.subTitle?.localize()

  }

  companion object {
    fun create(parent: ViewGroup): SubscriptionDetailHolder {

      val binding = SubscriptionDetailsItemBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return SubscriptionDetailHolder(binding)
    }
  }
}
