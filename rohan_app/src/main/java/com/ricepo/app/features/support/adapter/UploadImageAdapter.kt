package com.ricepo.app.features.support.adapter

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 14/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class UploadImageAdapter(
  private val datas: List<String>,
  private val pathUri: Map<String, Uri?>,
  private val delete: (path: String) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  class ViewHolder(
    private val view: View,
    private val delete: (path: String) -> Unit
  ) : RecyclerView.ViewHolder(view) {

    private val viewImage = view.findViewById<ImageView>(R.id.iv_support_issue)
    private val viewDel = view.findViewById<ImageView>(R.id.iv_support_issue_del)

    init {
      viewDel.clickWithTrigger { v ->
        val path = v.tag
        if (path is String) {
          delete(path)
        }
      }
    }

    fun bind(path: String, uri: Uri?) {
      viewDel.tag = path
      uri?.let {
        ImageLoader.load(viewImage, it)
      }
//            val bitmap = BitmapFactory.decodeFile(path)
//            val dr: RoundedBitmapDrawable = RoundedBitmapDrawableFactory.create(view.resources, bitmap)
//            dr.cornerRadius = 20f

//            val target = getRoundedCornerBitmap(bitmap, 16)
//            viewImage.setImageBitmap(target)
//            viewImage.setImageDrawable(dr)
    }

    private fun getRoundedCornerBitmap(bitmap: Bitmap, pixels: Int): Bitmap? {
      val output = Bitmap.createBitmap(
        bitmap.width,
        bitmap
          .height,
        Bitmap.Config.ARGB_8888
      )
      val canvas = Canvas(output)
      val color = ResourcesUtil.getColor(com.ricepo.style.R.color.textBoxShadow, view)
      val paint = Paint()
      val rect = Rect(0, 0, bitmap.width, bitmap.height)
      val rectF = RectF(rect)
      val roundPx = pixels.toFloat()
      paint.isAntiAlias = true
      canvas.drawARGB(0, 0, 0, 0)
      paint.color = color
      canvas.drawRoundRect(rectF, roundPx, roundPx, paint)
      paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
      canvas.drawBitmap(bitmap, rect, rect, paint)
      return output
    }

    companion object {
      fun create(parent: ViewGroup, delete: (path: String) -> Unit): ViewHolder {
        val view = LayoutInflater.from(parent.context)
          .inflate(R.layout.item_support_issue_image, parent, false)
        val width = (
          DisplayUtil.getScreenWidth(parent.context) -
            (3 * ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_20))
          ) / 2
        val height = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_100)
        val params = RecyclerView.LayoutParams(width, height)
        view.layoutParams = params
        return ViewHolder(view, delete)
      }
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return ViewHolder.create(parent, delete)
  }

  override fun getItemCount(): Int = datas.size

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val path = datas[position]
    return (holder as ViewHolder).bind(path, pathUri[path])
  }
}
