package com.ricepo.app.features.checkout.tips

import android.os.Bundle
import android.view.MotionEvent
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.databinding.ActivityTipsEnterBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.filter.MoneyInputFilter
import com.ricepo.base.inputmanager.KeyboardUtil

//
// Created by <PERSON><PERSON> on 3/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_CHECKOUT_TIPS_ENTER)
class TipsEnterActivity : BaseActivity() {

  private lateinit var binding: ActivityTipsEnterBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityTipsEnterBinding.inflate(layoutInflater)
    setContentView(binding.root)

    // set default tip value
    binding.etTips.setText("2")

    setupViewAndListener()
  }

  override fun onResume() {
    super.onResume()
    KeyboardUtil.showKeyboard(this, binding.etTips)
  }

  private fun setupViewAndListener() {
    binding.etTips?.filters = arrayOf(MoneyInputFilter())

    binding.etFocusTips?.setOnTouchListener { v, event ->
      when (event?.action) {
        MotionEvent.ACTION_DOWN -> {
          KeyboardUtil.toggleKeyboard(binding.etTips)
        }
      }
      true
    }

    binding.btnSaveTips.clickWithTrigger {
      val amount = binding.etTips.text.toString()
      if (amount.isNotEmpty()) {
        intent.putExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_ENTER, amount)
        backResultEvent(intent)
      }
    }
  }
}
