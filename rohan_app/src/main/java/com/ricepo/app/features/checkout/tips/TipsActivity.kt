package com.ricepo.app.features.checkout.tips

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityTipsBinding
import com.ricepo.app.databinding.TipsItemBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.model.TipModel
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Discount
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 3/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_CHECKOUT_TIPS)
class TipsActivity : BaseActivity() {

  var tips: List<TipModel>? = null

  var restaurant: Restaurant? = null

  lateinit var binding: ActivityTipsBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityTipsBinding.inflate(layoutInflater)
    setContentView(binding.root)

    val tipPrompt = intent.getStringExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_PROMPT)
    tips = intent.getParcelableArrayListExtra<TipModel>(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_OPTIONS)

    restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_RESTAURANT)

    tipPrompt?.let {
      binding.tvTipPrompt.text = it
    }
    setupViewAndListener()
  }

  private fun setupViewAndListener() {

    binding.rvTipsSelect.adapter = BindListAdapter(
      tips, R.layout.tips_item,
      object : OnBindViewListener<View, TipModel> {
        override fun onBindView(view: View, tipModel: TipModel?, position: Int) {
          val tipBinding = TipsItemBinding.bind(view)
          tipBinding.tvTip.text = tipModel?.name?.localize() ?: ""

          view.clickWithTrigger {
            intent.putExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS, tipModel?.value)
            backResultEvent(intent)
          }
        }
      }
    )

    binding.tvTipsCustom.clickWithTrigger {
      FeaturePageRouter.navigateTipsEnter(this)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT_TIPS_ENTER) {
        val value: String? = data?.getStringExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_ENTER) ?: null
        if (value != null && value.isNotEmpty() && value.isNotBlank()) {
          val tip = Discount(factor = 0.0, flat = (value.toDouble() * 100).toInt())
          intent.putExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS, tip)
        }
        backResultEvent(intent)
      }
    }
  }
}
