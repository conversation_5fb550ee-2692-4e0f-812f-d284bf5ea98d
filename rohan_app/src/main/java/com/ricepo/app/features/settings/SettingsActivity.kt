package com.ricepo.app.features.settings

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.compose.ComposeUiType
import com.ricepo.app.compose.launchComposeUi
import com.ricepo.app.databinding.ActivitySettingsBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.tools.SystemUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.log.Logger
import com.ricepo.style.LocaleConst
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourceApplication
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 26/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_SETTINGS)
class SettingsActivity : BaseActivity() {

  val settingsViewModel: SettingsViewModel by viewModels()

  lateinit var binding: ActivitySettingsBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivitySettingsBinding.inflate(layoutInflater)
    setContentView(binding.root)
    setTitle(ResourcesUtil.getString(com.ricepo.style.R.string.setting))

    bindViewModel()

    // set version
    binding.tvVersion.text = "${ResourcesUtil.getString(com.ricepo.style.R.string.ricepo)} ${SystemUtils.versionName()}"
  }

  override fun onStart() {
    super.onStart()
    setupListener()
  }

  override fun onResume() {
    super.onResume()
    settingsViewModel.initSettings()
  }

  private fun setupListener() {

    // language section
    binding.tvLanguageHans.clickWithTrigger {
      changeLanguage(LocaleConst.zhCN)
    }
    binding.tvLanguageHk.clickWithTrigger {
      changeLanguage(LocaleConst.zhHK)
    }
    binding.tvLanguageEn.clickWithTrigger {
      changeLanguage(LocaleConst.enUS)
    }
    binding.tvLanguageEs.clickWithTrigger {
      changeLanguage(LocaleConst.ES)
    }

    // personal info section
    binding.tvPersonCoupon.clickWithTrigger {
      FeaturePageRouter.navigateCoupon(this, ShowCouponCase.customer, restaurant = null)
    }
    binding.tvPersonCoin.clickWithTrigger {
      FeaturePageRouter.navigateRewardSummary()
    }
    binding.tvPersonCard.clickWithTrigger {
      FeaturePageRouter.navigatePayment(this, showPaymentCase = ShowPaymentCase.editCard)
    }

    // account section
    binding.tvLoginLogout.clickWithTrigger {
      if (binding.tvLoginLogout.text == ResourcesUtil.getString(com.ricepo.style.R.string.login)) {
        FeaturePageRouter.navigateLogin()
      } else if (binding.tvLoginLogout.text == ResourcesUtil.getString(com.ricepo.style.R.string.logout)) {
        DialogFacade.showPrompt(this, com.ricepo.style.R.string.logout_confirm) {
          settingsViewModel.logout()
        }
      }
    }
    binding.tvFeedback.clickWithTrigger {
      feedbackAction()
    }

    binding.tvTestMemory.clickWithTrigger {
    }

    binding.ivBack.clickWithTrigger {
      if (settingsViewModel.isLogoutSuccess) {
        backEvent()
      }
    }

    binding.tvAccountSafety.clickWithTrigger {
      launchComposeUi(ComposeUiType.AccountSafety)
    }

    // email feedback
    binding.tvVersion.setOnLongClickListener {
      Logger.flush()
      var file = Logger.getLogFile(this)
      IntentUtils.intentMail(this, file)
      true
    }
  }

  @Deprecated("Deprecated in Java")
  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == ComposeUiType.AccountSafety.code) {
      settingsViewModel.logout()
    }
  }

  private fun feedbackAction() {
    launchComposeUi(ComposeUiType.Rating)
  }

  private fun changeLanguage(lang: String) {
    LocaleUtil.saveLanguage(this, lang)
    ResourceApplication.resetContext(lang)
    FeaturePageRouter.navigateHomeWithReset(this)
  }

  private var memoryContainer = mutableListOf<ByteArray>()

  private fun testMemory() {
    val b = ByteArray(100 * 1000 * 1000)
    memoryContainer.add(b)

    SystemUtils.showMemoryInfo(this)
  }

  private fun bindViewModel() {
    settingsViewModel.liveData.observe(
      this,
      Observer {
        val config = it
        binding.tvLoginLogout.setText(config.loginLabel)
        if (config.loginLabel == ResourcesUtil.getString(com.ricepo.style.R.string.login)) {
          // logout success
        }

        showLanguageSelected(config.lang)
      }
    )
  }

  /**
   * show current language selected icon
   */
  private fun showLanguageSelected(lang: String?) {
    binding.ivLanguageHans.isVisible = lang == LocaleConst.zhCN
    binding.ivLanguageHk.isVisible = lang == LocaleConst.zhHK
    binding.ivLanguageEn.isVisible = lang == LocaleConst.enUS
    binding.ivLanguageEs.isVisible = lang == LocaleConst.ES
  }
}
