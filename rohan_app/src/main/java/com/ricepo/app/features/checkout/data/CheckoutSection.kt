package com.ricepo.app.features.checkout.data

import com.ricepo.app.model.ExtraFee
import com.ricepo.base.model.Cart

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

sealed class CheckoutSectionItem

data class NormalSectionItem(val cart: Cart) : CheckoutSectionItem()
data class ExtraFeesSectionItem(val extraFee: ExtraFee) : CheckoutSectionItem()
data class CommentsSectionItem(val info: OrderCellInfo) : CheckoutSectionItem()
data class FeesSectionItem(val info: OrderCellInfo) : CheckoutSectionItem()
data class FeesDiscountItem(val info: OrderCellInfo) : CheckoutSectionItem()
data class FeesTipItem(val info: OrderCellInfo) : CheckoutSectionItem()

data class CheckoutSection(
  val items: List<CheckoutSectionItem>,
  val type: String? = null
) {
  companion object {
    const val TYPE_NORMAL = "normal"
    const val TYPE_EXTRA_FEES = "extra_fees"
    const val TYPE_COMMENT = "comment"
    const val TYPE_FEES = "fees"
  }
}
