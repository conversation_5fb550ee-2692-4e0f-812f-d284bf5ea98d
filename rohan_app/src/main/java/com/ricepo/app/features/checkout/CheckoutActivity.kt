package com.ricepo.app.features.checkout

import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ImageSpan
import android.util.Log
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.logEvent
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.ActivityCheckoutBinding
import com.ricepo.app.databinding.LayoutCardDividerBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.checkout.data.CheckoutSection
import com.ricepo.app.features.checkout.data.CheckoutSectionItem
import com.ricepo.app.features.checkout.data.CommentsSectionItem
import com.ricepo.app.features.checkout.data.ExtraFeesSectionItem
import com.ricepo.app.features.checkout.data.FeesDiscountItem
import com.ricepo.app.features.checkout.data.FeesSectionItem
import com.ricepo.app.features.checkout.data.FeesTipItem
import com.ricepo.app.features.checkout.data.NormalSectionItem
import com.ricepo.app.features.menu.MenuGroupViewModel
import com.ricepo.app.features.menu.base.MenuBaseActivity
import com.ricepo.app.features.payment.PaymentUseCase
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.DeliveryEstimate
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.Quote
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.QuoteResponseData
import com.ricepo.app.model.QuoteResponseError
import com.ricepo.app.model.QuoteWindows
import com.ricepo.app.model.localize
import com.ricepo.app.pattern.payment.PaymentGoogle
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.app.view.MenuFoodItemView
import com.ricepo.app.view.OrderItemView
import com.ricepo.base.analytics.ScrollDepthFacade
import com.ricepo.base.animation.CheckoutLoading
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.clickAllWithTrigger
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.extension.uiSubscribe
import com.ricepo.base.extension.underline
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.Discount
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.tools.AssetUtils
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.MapFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.monitor.firebase.FirebaseBaseEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseMonitor
import com.ricepo.network.EnvNetwork.RICEPO_URL_PRIVACY
import com.ricepo.network.EnvNetwork.RICEPO_URL_TERMS
import com.ricepo.network.resource.ErrorCode
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.ricepo.style.round.RoundLayout
import com.ricepo.style.sheet.BaseBottomSheetFragment
import com.ricepo.style.view.CenterAlignImageSpan
import com.ricepo.tripartite.alipay.AlipayClient
import com.ricepo.tripartite.wechat.WeChatPay
import com.warkiz.widget.IndicatorSeekBar
import com.warkiz.widget.OnSeekChangeListener
import com.warkiz.widget.SeekParams
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.addTo
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by Thomsen on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_CHECKOUT)
class CheckoutActivity : MenuBaseActivity() {

  companion object {
    const val ACTIVITY_NAME = "com.ricepo.app.features.checkout.CheckoutActivity"
  }

  val checkoutViewModel: CheckoutViewModel by viewModels()

  val menuGroupViewModel: MenuGroupViewModel by viewModels()

  @Inject
  lateinit var checkoutMapper: CheckoutMapper

  @Inject
  lateinit var paymentUseCase: PaymentUseCase

  lateinit var binding: ActivityCheckoutBinding

  lateinit var viewModelOutput: CheckoutViewModel.Output

  val didAppear: PublishSubject<Boolean> = PublishSubject.create()

  val willAppear: BehaviorSubject<Boolean> = BehaviorSubject.createDefault(true)

  val changeAddress: PublishSubject<Boolean> = PublishSubject.create()

  val placeOrder: PublishSubject<CheckoutViewModel.CheckoutOption> = PublishSubject.create()

  private var isAlreadyBackEvent = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityCheckoutBinding.inflate(layoutInflater)
    setContentView(binding.root)

    val deliveryMode = intent.getStringExtra(FeaturePageConst.PARAM_DELIVERY_MODE)
    if (deliveryMode == TabMode.MODE_PICKUP) {
      checkoutViewModel.deliveryMode = CheckoutViewModel.DeliveryMethod.pickup
    }
    checkoutViewModel.restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_RESTAURANT)

    checkoutViewModel.entrance = entrance
    bindInputViewModel()
    bindOutputViewModel()

    initMenuStatus()

    ScrollDepthFacade.computeScrollDepth(binding.nslCheckoutContent)
  }

  override fun onResume() {
    super.onResume()
    willAppear.onNext(true)
  }

  override fun onDestroy() {
    super.onDestroy()
    binding.rtvPool.onDestroy()
  }

  override fun onWindowFocusChanged(hasFocus: Boolean) {
    super.onWindowFocusChanged(hasFocus)
    if (hasFocus) {
      didAppear.onNext(true)
    }
  }

  private fun bindInputViewModel() {

    val input = CheckoutViewModel.Input(
      this,
      willAppear,
      didAppear,
      changeAddress,
      placeOrder
    )

    viewModelOutput = checkoutViewModel.transform(input)
  }

  private var compositeDisposable: CompositeDisposable = CompositeDisposable()

  private fun bindOutputViewModel() {

    viewModelOutput.observeTableError
      .uiSubscribe()
      .subscribe {
        val message = it.message
        if (!message.isNullOrEmpty()) {
          DialogFacade.showAlert(this, message) {
            checkoutViewModel.handleQuoteFailed(
              this@CheckoutActivity,
              it.errorData
            ) {
              binding.flCheckoutPage.isVisible = false
            }
            // reset fast express delivery before show dialog
            resetLastExpressDeliver()
          }
          // member menu not llCheckoutFees count
          if (checkoutViewModel.isAlreadyLoaded) {
          } else {
            showErrorNetworkView(message, it.errorData)
          }
          // maybe hide loading
          CheckoutLoading.hideLoading()
        }
      }

    viewModelOutput.observeTableUpdated
      .uiSubscribe()
      .subscribe {
        // check group and carts not empty
        // case: reward dish to get quote check
        if (it.restaurant == null || it.cartList.isNullOrEmpty()) {
          Log.i("thom", "r = ${it.restaurant == null} or c = ${it.cartList.isNullOrEmpty()}")
          if (!isAlreadyBackEvent) {
            compositeDisposable.dispose()
            onBackPressed()
            isAlreadyBackEvent = true
          }
          return@subscribe
        }
        val restaurantId = it.restaurant.id ?: return@subscribe
        val sections = it.sections
        // get the section is not empty to remove update
        val normalVisible = if (!sections?.filter {
          it.type == CheckoutSection.TYPE_NORMAL ||
            it.type == CheckoutSection.TYPE_EXTRA_FEES
        }.isNullOrEmpty()
        ) {
          binding.llCheckoutNormal.removeAllViews()
          true
        } else false
        val feesSection = sections?.filter { it.type == CheckoutSection.TYPE_FEES }
        var feesVisible = false
        var feesDiscountVisible = false
        var feesTipsVisible = false
        feesSection?.forEach {
          if (!feesVisible) {
            binding.llCheckoutFees.removeAllViews()
            feesVisible = (it.items.filterIsInstance<FeesSectionItem>().isNotEmpty())
          }
          if (!feesDiscountVisible) {
            binding.llCheckoutFeesDiscount.removeAllViews()
            feesDiscountVisible = (it.items.filterIsInstance<FeesDiscountItem>().isNotEmpty())
          }
          if (!feesTipsVisible) {
            feesTipsVisible = (it.items.filterIsInstance<FeesTipItem>().isNotEmpty())
          }
        }
        binding.llCheckoutNormal.isVisible = normalVisible
        binding.layCheckoutFees.isVisible = feesVisible
        binding.layCheckoutFeesDiscount.isVisible = feesDiscountVisible
        binding.layCheckoutTips.isVisible = feesTipsVisible

        sections?.forEachIndexed { sectionIndex, s ->
          val sectionSize = s.items.size
          s.items.forEachIndexed { index, item ->
            addSectionItemView(index, item, sectionIndex, sectionSize)
          }
        }

        checkGroupOrder(restaurantId, it)
      }.addTo(compositeDisposable)

    viewModelOutput.buttonEnabled
      .uiSubscribe()
      .subscribe { enable ->
        // disable place order style
        val alpha = if (enable) 1f else 0.2f
        binding.inPlaceOrder.alpha = alpha
      }

    viewModelOutput.orderPaymentCreated
      .uiSubscribe()
      .subscribe {
        val result = it
        handlePayment(result)
      }

//    viewModelOutput.paymentUpdated.uiSubscribe().subscribe {
//      (it as? PaymentOwnMethod)?.let { pm ->
//        setPaymentView(pm)
//      }
//    }
    checkoutViewModel.observePaymentUpdated.uiSubscribe().subscribe { payment ->
      if (payment != null) {
        setPaymentView(payment)
      }
    }

    viewModelOutput.qtyUpdated
      .uiSubscribe()
      .subscribe { restCart ->
        lifecycleScope.launch {
          // update group order cart
          val flow = menuGroupViewModel.updateGroupCartQuantity(
            this@CheckoutActivity,
            restCart.cartList, restCart.restaurant
          )
          if (flow == null) {
            checkoutViewModel.reloadQuote()
          } else {
            flow.collectLatest { orderGroup ->
              if (orderGroup != null) {
                menuGroupViewModel.updateLocalGroupInfo(restCart.restaurant, orderGroup)
                // refresh checkout cart list
                checkoutViewModel.observeGroupOrderUpdate.onNext(true)
              }
              // refresh quote
              checkoutViewModel.reloadQuote()
            }
          }
        }
      }
  }

  private fun checkGroupOrder(
    restaurantId: String,
    it: CheckoutViewModel.CheckoutOption
  ) {
    lifecycleScope.launch {
      if (GroupOrderCache.isOwner(restaurantId)) {
        // normal and group owner show options
        setFeesDiffView(it.quote, it.restaurant)
        setSubscriptionView(it.quote)
        if (it.deliveryMethod == CheckoutViewModel.DeliveryMethod.delivery) {
          setDeliveryView(it.address, it.quote, it.restaurant, it.quoteTimeWindows)
        } else if (it.deliveryMethod == CheckoutViewModel.DeliveryMethod.pickup) {
          setPickupView(it.restaurant, it.quote)
        }
        setExpressDelivery(it)
        // payment method view
        setPaymentView(it.paymentOwnMethod)
        setPolicyView(it.restaurant)
        setPlaceOrderButton(it, it.restaurant)
        // show payment view
        binding.inCheckoutPayment.clPaymentSelect.isVisible = true
        // show rice pool view
        if (it.quote is QuoteResponseData &&
          it.deliveryMethod == CheckoutViewModel.DeliveryMethod.delivery
        ) {
          val quotePool = (it.quote as QuoteResponseData).v1?.pool
          bindPool(quotePool)
        } else {
          binding.rtvPool.isVisible = false
        }
      } else {

        binding.layCheckoutDelivery.isVisible = false
        binding.layCheckoutPickup.isVisible = false
        binding.layCheckoutPayment.isVisible = false
        binding.layCheckoutPolicy.isVisible = false
        binding.inPlaceOrder.isVisible = false

        // member already loaded
        checkoutViewModel.isAlreadyLoaded = true
      }
      startGroupOrderCartTimer()
    }
  }

  private fun showErrorNetworkView(message: String?, errorData: ErrorData?) {
    binding.flCheckoutPage.isVisible = true
    val buttonId = if (errorData?.code == ErrorCode.ADDRESS_UNDELIVERABLE) {
      com.ricepo.style.R.string.support_change_address_title
    } else {
      com.ricepo.style.R.string.retry
    }
    showErrorView(
      binding.flCheckoutPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, buttonId,
      isCleanWhenClick = false,
      listener = {
        if (errorData?.code == ErrorCode.ADDRESS_UNDELIVERABLE) {
          FeaturePageRouter.navigateAddress(this)
        } else {
          binding.flCheckoutPage.isVisible = false
          checkoutViewModel.reloadQuote()
        }
      }
    )
  }

  private fun bindPool(pool: RestaurantPool?) {
    val pool = pool ?: return
    binding.rtvPool.isVisible = true
    binding.rtvPool.setMessage(pool?.message?.localize())
    binding.rtvPool.setExpireAt(pool?.expiresAt)
    binding.rtvPool.onExpiredFinish = {
      Handler().postDelayed(
        {
          checkoutViewModel.reloadQuote()
        },
        100
      )
    }
  }

  private fun handlePayment(result: Result<Pair<PaymentOwnMethod?, PaymentObj>?>) {
    result.fold(
      onSuccess = { data ->
        val data = data ?: return@fold
        val paymentMethod = data.first
        val paymentObj = data.second
        paymentUseCase.handlePayment(
          this@CheckoutActivity,
          paymentMethod,
          paymentObj
        ) { paymentError, html ->
          if (paymentError.isNullOrEmpty()) {
            // payment success
            paymentCompleted()
            if (!html.isNullOrEmpty()) {
              FeaturePageRouter.navigateHtmlWeb(html)
            }
          } else {
            paymentFailed(paymentError)
          }
        }
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.PURCHASE) {
          param(FirebaseAnalytics.Param.PAYMENT_TYPE, paymentMethod?.method ?: "")
          param(FirebaseAnalytics.Param.VALUE, paymentObj.order?.id ?: "")
          param(FirebaseAnalytics.Param.ITEM_NAME, paymentObj.order?.restaurant?.id ?: "")
        }
      },
      onFailure = { error ->
        paymentFailed(error.message)
      }
    )
  }

  private fun setSubscriptionView(quote: QuoteResponse?) {
    if (quote is QuoteResponseData) {
      val data = quote.v1
      val subscription = data.plan
      binding.layCheckoutMembership.isVisible = (subscription != null)
      if (subscription != null) {
        binding.inCheckoutMembership.tvMembershipTitle.text = subscription.title?.localize()
        binding.inCheckoutMembership.tvMembershipSubtitle.text = subscription.subtitle?.localize()
        binding.inCheckoutMembership.tvMembershipApply.text = subscription.button?.localize()
        binding.inCheckoutMembership.root.clickWithTrigger {
          FeaturePageRouter.navigateSubscription(
            this, subscription,
            false, entrance = entrance,
            restaurant = checkoutViewModel.restaurant
          )
        }
      }
    }
  }

  private fun paymentCompleted() {
    checkoutViewModel.placeOrderSuccess()
    CheckoutLoading.hideLoading()
  }

  private fun paymentFailed(msg: String?) {
    if (msg == WeChatPay.PENDING) {
      checkoutViewModel.placeOrderSuccess()
      return
    }
    val message = if (msg.isNullOrEmpty() ||
      msg == AlipayClient.CANCELED
    ) {
      ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed)
    } else {
      msg
    }
    DialogFacade.showAlert(this, message, canCancel = false) {
      paymentCompleted()
    }
    CheckoutLoading.hideLoading()
  }

  private fun setPaymentView(payment: PaymentOwnMethod?) {
    binding.layCheckoutPayment.isVisible = true
    // divider
    binding.inCheckoutPayment.tvDividerTop.isVisible = false
    if (checkoutViewModel.isValidPaymentMethod(payment)) {
      // payment info
      ImageLoader.load(
        binding.inCheckoutPayment.ivPaymentIcon,
        payment?.image?.localize(),
        com.ricepo.style.R.drawable.ic_payment_none
      )
      binding.inCheckoutPayment.tvPaymentTitle.text = payment?.name?.localize()
        ?: checkoutMapper.mapPaymentTitle(payment)
    } else {
      // payment none
      binding.inCheckoutPayment.ivPaymentIcon.setImageResource(com.ricepo.style.R.drawable.ic_payment_none)
      binding.inCheckoutPayment.tvPaymentTitle.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }

    binding.inCheckoutPayment.clPaymentSelect.clickWithTrigger {
      checkoutViewModel.cleanTempPayment()
      checkoutViewModel.isFromPayment = true
      FeaturePageRouter.navigatePayment(this)
    }
  }

  private fun setFeesDiffView(quoteResp: QuoteResponse?, restaurant: Restaurant?) {

    var quote: Quote? = if (quoteResp is QuoteResponseData) {
      quoteResp.v1
    } else return

    val diff = quote?.expenses?.delta ?: 0
    if (diff > 0) {
      val text = checkoutMapper.formatPriceByRestaurant(diff, restaurant)
      binding.tvFeesDiff?.visibility = View.VISIBLE
      binding.tvFeesDiff?.text = quote?.notes?.reachMinimum?.localize()
        ?: ResourcesUtil.getString(com.ricepo.style.R.string.not_reach_min, text)
    } else {
      binding.tvFeesDiff?.visibility = View.GONE
    }
  }

  private var mDeliverProgress = 0f
  private var mDeliveryOriginalProgress = 0f
  private var mDeliverOriginalEstimate: DeliveryEstimate? = null
  private var mDeliveryPosition = 0
  private var mDeliveryOriginalPosition: Int = 0
  private var mDeliveryOriginalCount: Int = 0

  private fun resetLastExpressDeliver() {
    checkoutViewModel.deliveryEstimate = mDeliverOriginalEstimate
    binding.inCheckoutFastDelivery.isbFastDelivery.setProgress(mDeliveryOriginalProgress)
    mDeliveryPosition = mDeliveryOriginalPosition
  }

  private fun setExpressDelivery(checkoutOption: CheckoutViewModel.CheckoutOption) {
    val quoteResp = checkoutOption.quote
    val quote = if (quoteResp is QuoteResponseData) {
      // save the original progress
      mDeliveryOriginalProgress = mDeliverProgress
      mDeliverOriginalEstimate = checkoutViewModel.deliveryEstimate
      mDeliveryOriginalPosition = mDeliveryPosition
      // init express delivery
      checkoutViewModel.deliveryEstimate = null
      binding.inCheckoutFastDelivery.clFastDelivery.isVisible = false
      quoteResp.v1
    } else {
      // restore original selection status after show dialog when quote error
      return
    }
    // return when express is empty
    val expressDelivery = quote.expenses?.expressDelivery
    val express = expressDelivery?.options ?: return
    // return if have window
    if (checkoutViewModel.quoteTimeWindow != null) return
    var tickCount = express.size

    // set the fast delivery layout visible
    binding.inCheckoutFastDelivery.clFastDelivery.isVisible = tickCount > 0

    // set the slider tick count
    if (tickCount > 50) {
      tickCount = 50
    }
    // solved discrete when tick count 2
    if (tickCount == 2) {
      tickCount = 3
    }
    binding.inCheckoutFastDelivery.isbFastDelivery.tickCount = tickCount

    // found the selected delivery
    val tickIndex = checkoutViewModel.setDeliveryTickIndex(expressDelivery)
    // change the selected and tick count by server
    if (tickIndex != mDeliveryOriginalPosition || mDeliveryOriginalCount != tickCount) {
      val progress = if (tickCount == 2) {
        if (tickIndex == 0) 0f else 100f
      } else {
        100f * (tickIndex / (tickCount - 1).toFloat())
      }
      binding.inCheckoutFastDelivery.isbFastDelivery.setProgress(progress)
      binding.inCheckoutFastDelivery.isbFastDeliveryBackgroundShadow.setProgress(progress)
      binding.inCheckoutFastDelivery.isbFastDeliveryShadow.setProgress(progress)
      mDeliveryOriginalPosition = tickIndex
    }
    mDeliveryOriginalCount = tickCount

    val estimate = checkoutViewModel.deliveryEstimate
    setFastDeliveryView(estimate, checkoutOption.restaurant)

    binding.inCheckoutFastDelivery.tvFastDeliveryStart.text = checkoutMapper
      .formatPriceByRestaurant(express.firstOrNull()?.amount ?: 0, checkoutOption.restaurant)

    binding.inCheckoutFastDelivery.tvFastDeliveryEnd.text = checkoutMapper
      .formatPriceByRestaurant(express.lastOrNull()?.amount ?: 0, checkoutOption.restaurant)

    binding.inCheckoutFastDelivery.isbFastDelivery.onSeekChangeListener =
      object : OnSeekChangeListener {
        override fun onSeeking(seekParams: SeekParams?) {
          val progress = seekParams?.progressFloat ?: 0f
          binding.inCheckoutFastDelivery.isbFastDeliveryBackgroundShadow.setProgress(progress)
          binding.inCheckoutFastDelivery.isbFastDeliveryShadow.setProgress(progress)

          mDeliveryPosition = if (express.size == 2) {
            if (progress > 49) 1 else 0
          } else {
            seekParams?.thumbPosition ?: 0
          }

          mDeliverProgress = progress
        }

        override fun onStartTrackingTouch(seekBar: IndicatorSeekBar?) {
        }

        override fun onStopTrackingTouch(seekBar: IndicatorSeekBar?) {
          val progress = seekBar?.progress ?: 0
          if (express.size == 2) {
            if (progress > 49) {
              binding.inCheckoutFastDelivery.isbFastDelivery.setProgress(100f)
            } else {
              binding.inCheckoutFastDelivery.isbFastDelivery.setProgress(0f)
            }
          }
          // reload quote when move other section
          if (mDeliveryOriginalPosition != mDeliveryPosition) {
            val selectedEstimate = express.getOrNull(mDeliveryPosition)
            checkoutViewModel.deliveryEstimate = selectedEstimate
            checkoutViewModel.reloadQuote()
          }
        }
      }
  }

  private fun setFastDeliveryView(estimate: DeliveryEstimate?, restaurant: Restaurant?) {
    binding.layCheckoutFastDelivery.isVisible = true
    binding.inCheckoutFastDelivery.tvDividerTop.isVisible = false
    binding.inCheckoutFastDelivery.tvFastDeliveryTime.text =
      checkoutViewModel.getDeliveryTime(estimate?.estimate)
    binding.inCheckoutFastDelivery.tvFastDeliveryFee.text =
      "+${checkoutMapper.formatPriceByRestaurant(estimate?.amount ?: 0, restaurant)}"
  }

  private fun setDeliveryView(
    address: FormatUserAddress?,
    quoteRes: QuoteResponse?,
    restaurant: Restaurant?,
    quoteWindowTime: QuoteWindows?
  ) {

    // hide pickup view show delivery view
    binding.layCheckoutDelivery.isVisible = true
    binding.layCheckoutPickup.isVisible = false
    binding.inCheckoutPickup.clPickupAddress.isVisible = false
    binding.inCheckoutDelivery.tvDividerTop.isVisible = false

    // set delivery time view
    setDeliveryTimeView(address, quoteRes, restaurant, quoteWindowTime)

    // listener address
    binding.inCheckoutDelivery.tvDeliveryAddressLeftClick.clickWithTrigger {
      // edit address
      FeaturePageRouter.navigateAddress(this)
    }
    binding.inCheckoutDelivery.tvDeliveryAddressRightClick.clickWithTrigger {
      // edit address
      FeaturePageRouter.navigateAddress(this)
    }
    binding.inCheckoutDelivery.tvDeliveryAddressErrorClick.clickWithTrigger {
      // reload quote
      checkoutViewModel.reloadQuote()
    }

    // listener delivery type
    binding.inCheckoutDelivery.groupDeliveryType?.clickAllWithTrigger {
      DialogFacade.showPrompt(
        this, com.ricepo.style.R.string.delivery_alert_to_package,
        positiveId = com.ricepo.style.R.string.confirm
      ) {
        checkoutViewModel.changeToPickup()
      }
    }

    if (address == null) return
    binding.inCheckoutDelivery.tvDeliveryAddress?.apply {
      text = address.name
    }
    binding.inCheckoutDelivery.tvDeliveryCity?.let {
      it.text = "${address.unit ?: ""} ${address.city ?: ""}".trim()
      it.visibility = if (it.text.isEmpty()) View.GONE else View.VISIBLE
    }

    // show address map view with alpha animator
    alphaAddressView(binding.inCheckoutDelivery.clDeliveryAddress)
    // map url image
    ImageLoader.load(
      binding.inCheckoutDelivery.ivDeliveryAddress,
      MapFacade.generateMapUrl(address, ThemeUtil.isDarkMode())
    )
  }

  /**
   * alpha animator for delivery address map
   */
  private fun alphaAddressView(view: View) {

    val addressView = view.findViewById<ImageView>(R.id.iv_delivery_address) ?: return
    if (addressView.alpha == 1f) return
    val addressRoundLayoutPlaceholder = view.findViewById<RoundLayout>(
      R.id.round_delivery_address_placeholder
    )
    val alphaAnimator = ObjectAnimator.ofFloat(
      addressView,
      "alpha", 0f, 1f
    )
    alphaAnimator.duration = 2000
    alphaAnimator.addListener(
      onEnd = {
        addressView.alpha = 1f
//            addressRoundLayoutPlaceholder.alpha = 0f
      }
    )
    alphaAnimator.start()

    val addressRoundLayout = view.findViewById<RoundLayout>(R.id.round_delivery_address)
    val roundAlphaAnimator = ObjectAnimator.ofFloat(
      addressRoundLayout,
      "alpha", 0f, 1f
    )
    roundAlphaAnimator.duration = 2000
    roundAlphaAnimator.start()
  }

  private fun setDeliveryTimeView(
    address: FormatUserAddress?,
    quoteRes: QuoteResponse?,
    restaurant: Restaurant?,
    quoteWindowTime: QuoteWindows?
  ) {
    binding.inCheckoutDelivery.ivDeliveryTimeArrow?.visibility = View.GONE
    if (address == null) {
      val message = ""
      binding.inCheckoutDelivery.tvDeliveryAddressError?.text = message
      binding.inCheckoutDelivery.tvDeliveryAddressError?.visibility = View.VISIBLE
      binding.inCheckoutDelivery.tvDeliveryAddressErrorClick?.visibility = View.VISIBLE
      binding.inCheckoutDelivery.tvDeliveryTime?.visibility = View.GONE
      binding.inCheckoutDelivery.groupDeliveryGuarantee.isVisible = false
    } else if (quoteRes is QuoteResponseData) {
      val quote = quoteRes.v1
      // delivery time
      val timeWindows = quote?.windows
      if (timeWindows != null && timeWindows.isNotEmpty()) {
        // set default if quote time already selected and first window when windows already changed
        val firstTimeWindow = timeWindows.firstOrNull()
        val defaultTimeWindow = if (quoteWindowTime == null ||
          timeWindows.find { it.formatted == quoteWindowTime.formatted } == null
        ) {
          // show the first timeWindow if windows not contained
          firstTimeWindow
        } else {
          // init quote time window
          quoteWindowTime
        }

        if (defaultTimeWindow?.ondemand == true) {
          // the delivery current time is set null
          checkoutViewModel.quoteTimeWindow = null
        } else {
          checkoutViewModel.quoteTimeWindow = defaultTimeWindow
        }

        // window time label
        binding.inCheckoutDelivery.tvDeliveryTime?.text = defaultTimeWindow?.formatted?.localize()
        binding.inCheckoutDelivery.ivDeliveryTimeArrow?.visibility = View.VISIBLE
        // not use group with visibility conflict
        binding.inCheckoutDelivery.tvDeliveryTime?.clickWithTrigger {
          // show bottom sheet
          showTimeWindowSheet(timeWindows)
        }
        binding.inCheckoutDelivery.ivDeliveryTimeArrow?.clickWithTrigger {
          showTimeWindowSheet(timeWindows)
        }
      } else {
        // need to clear quoteTimeWindow if `quote.windows is gone
        checkoutViewModel.quoteTimeWindow = null
      }
      binding.inCheckoutDelivery.tvDeliveryAddressError?.visibility = View.GONE
      binding.inCheckoutDelivery.tvDeliveryAddressErrorClick?.visibility = View.GONE
      binding.inCheckoutDelivery.tvDeliveryTime?.visibility = View.VISIBLE

      binding.inCheckoutDelivery.tvQuoteNote.isVisible = (quote?.notes?.warning != null)
      binding.inCheckoutDelivery.tvQuoteNote.text = quote?.notes?.warning?.localize()

      // delivery guarantee
      binding.inCheckoutDelivery.groupDeliveryGuarantee.isVisible = (quote?.notes?.guarantee != null)
      binding.inCheckoutDelivery.groupDeliveryGuarantee.clickAllWithTrigger {
        quote?.notes?.guarantee?.localize()?.let {
          DialogFacade.showAlert(this, it)
        }
      }
    } else if (quoteRes is QuoteResponseError) {
      // delivery address error
      val error = quoteRes.v1
      if (error.message() != null && error.code != ErrorData.CODE_INIT) {
        val message = error.message()
        binding.inCheckoutDelivery.tvDeliveryAddressError?.tag = error.code
        binding.inCheckoutDelivery.tvDeliveryAddressError?.text = message
        binding.inCheckoutDelivery.tvDeliveryAddressError?.visibility = View.VISIBLE
        binding.inCheckoutDelivery.tvDeliveryAddressErrorClick?.visibility = View.VISIBLE
        binding.inCheckoutDelivery.tvDeliveryTime?.visibility = View.GONE
        binding.inCheckoutDelivery.groupDeliveryGuarantee.isVisible = false
      }
    }
  }

  private fun showTimeWindowSheet(
    timeWindows: List<QuoteWindows>,
    isPickupWindow: Boolean = false
  ) {
    var formatted = timeWindows.map {
      it.formatted?.localize() ?: ""
    }.toMutableList()
    val sheet = BaseBottomSheetFragment(formatted, timeWindows)
    sheet.onItemClickListener = object : BaseBottomSheetFragment.OnItemClickListener<QuoteWindows> {
      override fun onItemClick(text: String, data: QuoteWindows?) {
        if (isPickupWindow) {
          checkoutViewModel.quoteTimeWindow = data
          binding.inCheckoutPickup.tvPickupTime?.text = data?.formatted?.localize() ?: ""
        } else {
          // the delivery is current time is not set window
          if (data?.ondemand == true) {
            checkoutViewModel.quoteTimeWindow = null
          } else {
            checkoutViewModel.quoteTimeWindow = data
          }
          binding.inCheckoutDelivery.tvDeliveryTime.text =
            data?.formatted?.localize() ?: ""
        }
        checkoutViewModel.deliveryEstimate = null
        // don't reload quote if not fast estimate
        if (mDeliverOriginalEstimate != null) {
          checkoutViewModel.reloadQuote()
        }
      }
    }
    sheet.show(supportFragmentManager, "time_windows_sheet")
  }

  private fun getNormalDeliveryLabel(text: String, restaurant: Restaurant?): SpannableStringBuilder {
    val spanText = SpannableStringBuilder(text)
    if (restaurant?.delivery?.provider != null) {
      spanText.append(" ")
      val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_delivery)
      drawable.setBounds(0, -6, 80, 30)
      val imageSpan = CenterAlignImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
      spanText.setSpan(imageSpan, spanText.length - 1, spanText.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
    }
    return spanText
  }

  private fun setPickupView(restaurant: Restaurant?, quoteResp: QuoteResponse?) {
    // hide delivery view show pickup view
    binding.layCheckoutDelivery.isVisible = false
    binding.layCheckoutPickup.isVisible = true
    binding.inCheckoutPickup.clPickupAddress.isVisible = true
    binding.inCheckoutPickup.tvDividerTop.isVisible = false

    val address = restaurant?.address
    if (address is AddressObj) {
      val pickupAddress = address.formatted
      if (!pickupAddress.isNullOrEmpty()) {
        binding.inCheckoutPickup.tvPickupAddress.text = pickupAddress
        binding.inCheckoutPickup.tvPickupAddress.visibility = View.VISIBLE
      } else {
        binding.inCheckoutPickup.tvPickupAddress.visibility = View.GONE
      }
    }

    binding.inCheckoutPickup.clPickupAddress.clickWithTrigger {
      // reload quote if error is verify failed and visible
      if (binding.inCheckoutPickup.tvPickupError.visibility == View.VISIBLE) {
        // reload quote
        checkoutViewModel.reloadQuote()
      }
    }

    // quote note
    if (quoteResp is QuoteResponseData) {
      val quote = quoteResp.v1
      binding.inCheckoutPickup.tvQuoteNote.let {
        it.isVisible = (quote.notes?.warning != null)
        it.text = quote.notes?.warning?.localize()
      }
      binding.inCheckoutPickup.tvPickupError.text = null
      binding.inCheckoutPickup.tvPickupError.isVisible = false

      binding.inCheckoutPickup.tvPickupTime.isVisible = false

      // estimated pickup time
      val pickupWindows = quote?.pickupWindows

      if (pickupWindows != null && pickupWindows.isNotEmpty()) {
        // set default window of window changed when first
        val quoteTimeWindow = checkoutViewModel.quoteTimeWindow
        val firstTimeWindow = pickupWindows.firstOrNull()
        val defaultWindow = if (quoteTimeWindow == null ||
          pickupWindows.find { it.formatted == quoteTimeWindow.formatted } == null
        ) {
          // show the first timeWindow if pickupWindows not contained
          firstTimeWindow
        } else {
          quoteTimeWindow
        }
        checkoutViewModel.quoteTimeWindow = defaultWindow
        binding.inCheckoutPickup.tvPickupTime.text = defaultWindow?.formatted?.localize() ?: ""
        binding.inCheckoutPickup.tvPickupTime.isVisible = true
        binding.inCheckoutPickup.ivPickupTimeArrow.visibility = View.VISIBLE

        // not use group with visibility conflict
        binding.inCheckoutPickup.tvPickupTime.clickWithTrigger {
          // show bottom sheet
          showTimeWindowSheet(pickupWindows, true)
        }
        binding.inCheckoutPickup.ivPickupTimeArrow.clickWithTrigger {
          showTimeWindowSheet(pickupWindows, true)
        }
      } else {
        // need to clear quoteTimeWindow if `quote.windows is gone
        checkoutViewModel.quoteTimeWindow = null
      }
    } else if (quoteResp is QuoteResponseError) {
      // pickup error
      val error = quoteResp.v1
      if (error.message() != null && error.code != ErrorData.CODE_INIT) {
        binding.inCheckoutPickup.tvPickupError.text = error.message()
        binding.inCheckoutPickup.tvPickupError.isVisible = true
        // hide the pickup time
        binding.inCheckoutPickup.tvPickupTime.isVisible = false
      }
    }

    binding.inCheckoutPickup.groupPickupType?.clickAllWithTrigger {
      DialogFacade.showPrompt(
        this, com.ricepo.style.R.string.delivery_alert_to_delivery,
        positiveId = com.ricepo.style.R.string.confirm
      ) {
        checkoutViewModel.changeToDelivery()
      }
    }
  }

  private fun setPlaceOrderButton(
    option: CheckoutViewModel.CheckoutOption,
    restaurant: Restaurant?
  ) {
    // reset option.cartList when delete food get quote error
    binding.inPlaceOrder.tag = option
    val quote = if (option.quote is QuoteResponseData) {
      val quoteResp = option.quote as QuoteResponseData
      quoteResp.v1
    } else return
    // show place order button
    binding.inPlaceOrder.isVisible = true

    if (checkoutViewModel.isValidPaymentMethod(option.paymentOwnMethod)) {
      // Set place order button text to place order
      binding.tvCheckoutOrderPlace.text = ResourcesUtil.getString(com.ricepo.style.R.string.confirm_payment)
    } else {
      // Show choose payment text on place order button
      binding.tvCheckoutOrderPlace.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }

    binding.tvCheckoutOrderPrice.text = checkoutMapper
      .formatPriceByRestaurant(quote.summary?.finalTotal ?: 0, restaurant)

    binding.inPlaceOrder.clickWithTrigger(800) {

      // place order
      if (checkoutViewModel.isOrdering) return@clickWithTrigger
      // don't click when disable
      if (binding.inPlaceOrder.alpha != 1f) return@clickWithTrigger
      val optObj = it.tag
      if (optObj is CheckoutViewModel.CheckoutOption) {
        placeOrRefreshOrder(true, optObj)

        // log event
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.BEGIN_CHECKOUT) {
          param(FirebaseAnalytics.Param.PAYMENT_TYPE, optObj.paymentOwnMethod?.method ?: "")
          param(FirebaseAnalytics.Param.ITEM_NAME, optObj.restaurant?.name?.localize() ?: "")
        }
        FirebaseMonitor.logEvent(FirebaseEventName.rPlaceOrder, FirebaseBaseEvent())
      }
    }
  }

  private fun addSectionItemView(
    itemIndex: Int,
    item: CheckoutSectionItem,
    sectionIndex: Int,
    sectionSize: Int
  ) {
    when (item) {
      is NormalSectionItem -> {
        if (sectionIndex > 0 || itemIndex > 0) {
          val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
          val params = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_2)
          )
          dividerBinding.root.layoutParams = params
          binding.llCheckoutNormal.addView(dividerBinding.root)
        }
        val view = MenuFoodItemView(this)
        view.initiate(
          item.cart, itemIndex, checkoutViewModel.restaurant,
          minus = { item, itemIndex ->
            checkoutViewModel.removeItemCart(item, itemIndex)
          },
          plus = { item, itemIndex ->
            checkoutViewModel.addItemCart(item, itemIndex)
          },
          editOption = { item, itemIndex ->
            checkoutViewModel.editOption(this, item, itemIndex)
          }
        )
        binding.llCheckoutNormal.addView(view)
      }
      is ExtraFeesSectionItem -> {
        if (sectionIndex > 0 || itemIndex > 0) {
          val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
          val params = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_2)
          )
          dividerBinding.root.layoutParams = params
          binding.llCheckoutNormal.addView(dividerBinding.root)
        }
        val view = MenuFoodItemView(this)
        view.initiate(item.extraFee, checkoutViewModel.restaurant)
        binding.llCheckoutNormal.addView(view)
      }
      is CommentsSectionItem -> {
        if (sectionIndex > 0 || itemIndex > 0) {
          val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
          val params = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_2)
          )
          dividerBinding.root.layoutParams = params
          binding.llCheckoutNormal.addView(dividerBinding.root)
        }
        val view = OrderItemView(this)
        view.initiate(item.info)
        view.binding.tvLeftText.isSingleLine = false
        view.binding.guidelineItemLeft.setGuidelineBegin(0)
        val paddingTop = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_18dp)
        val paddingBottom = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp)

        view.binding.guidelineItemTop.setGuidelineBegin(paddingTop)
        view.binding.guidelineItemBottom.setGuidelineEnd(paddingBottom)
        val params = view.binding.ivRightIcon.layoutParams
        if (params is ConstraintLayout.LayoutParams) {
          params.width = ResourcesUtil.getDimenPixelOffset(view, com.ricepo.style.R.dimen.sw_14dp)
          params.height = ResourcesUtil.getDimenPixelOffset(view, com.ricepo.style.R.dimen.sw_14dp)
          params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
          params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
          params.bottomMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_5dp)
          view.binding.ivRightIcon.layoutParams = params
        }
        view.setMarginEnd(ResourcesUtil.getDimenPixelOffset(view, com.ricepo.style.R.dimen.sw_14dp))
        binding.llCheckoutNormal.addView(view)
      }
      is FeesSectionItem -> {
        val view = OrderItemView(this)
        view.initiate(item.info)
        binding.llCheckoutFees.addView(view)
      }
      is FeesDiscountItem -> {
        val view = OrderItemView(this)
        view.initiate(item.info)
        binding.llCheckoutFeesDiscount.addView(view)
      }
      is FeesTipItem -> {
        val info = item.info
        with(binding.inCheckoutTips) {
          tvTipsTitle.text = info.leftText
          tvTipsInfo.text = info.leftDetail
          tvTipsInfo.isVisible = info.leftDetail?.isNotBlank() ?: false
          root.clickWithTrigger {
            info.click?.run { this() }
          }
        }
      }
    }
  }

  private fun setPolicyView(restaurant: Restaurant?) {

    binding.layCheckoutPolicy.isVisible = true

    binding.inCheckoutPolicy.tvPolicyCallSupport?.underline()
    binding.inCheckoutPolicy.tvPolicyTerms?.underline()
    binding.inCheckoutPolicy.tvPolicyInfo?.underline()

    binding.inCheckoutPolicy.tvPolicyCallSupport?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        RestaurantCartCache.getCountry(restaurant) { countryCode ->
          var phoneNumber = AssetUtils.getCountry(countryCode, "phone") as String
          phoneNumber = phoneNumber.replace(" ", "")
          DialogFacade.showPrompt(this@CheckoutActivity, phoneNumber) {
            IntentUtils.intentPhone(this, lifecycleScope, phoneNumber)
          }
        }
      }
    }

    binding.inCheckoutPolicy.tvPolicyTerms?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        IntentUtils.intentBrowser(this, RICEPO_URL_TERMS)
      }
    }

    binding.inCheckoutPolicy.tvPolicyInfo?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        IntentUtils.intentBrowser(this, RICEPO_URL_PRIVACY)
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT_COMMENT) {
        val comments = data?.getStringExtra(FeaturePageConst.PARAM_PAGE_CHECKOUT_COMMENT) ?: ""
        checkoutViewModel.updateComments(comments)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_ADDRESS) {
        // update address
        changeAddress.onNext(true)
        binding.flCheckoutPage.isVisible = false
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT_TIPS) {
        // update tips
        val tips: Discount? = data?.getParcelableExtra(
          FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS
        ) ?: null
        checkoutViewModel.updateTips(tips)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_COUPON) {
        // update coupon
        val coupon: Coupon? = data?.getParcelableExtra(
          FeaturePageConst.PARAM_PAGE_COUPON_SELECTED
        ) ?: null
        checkoutViewModel.updateCoupon(coupon)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_PAYMENT) {
        val payment: PaymentOwnMethod? = data?.getParcelableExtra(
          FeaturePageConst.PARAM_PAGE_PAYMENT_NEW_CARD
        )
        checkoutViewModel.updateTempPayment(payment)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_LOGIN) {
        checkoutViewModel.observeLoginSuccess(true)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_SUBSCRIPTION) {
        checkoutViewModel.reloadQuote()
      }
      // eidt options result
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_OPTIONS) {
        val originalFood = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD_ORIGINAL)
        val food = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
        lifecycleScope.launch {
          // options select position
          checkoutViewModel.updateItemCart(originalFood, food)
        }
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT_POINTS) {
        // ricepo points
        val value = data?.getIntExtra(FeaturePageConst.PARAM_POINTS_VALUE, -1) ?: -1
        if (value > -1) {
          checkoutViewModel.ricepoPoints = value
          checkoutViewModel.reloadQuote()
        }
      }
    }

    if (requestCode == PaymentGoogle.LOAD_PAYMENT_DATA_REQUEST_CODE) {
      PaymentRefer.onPaymentGoogleResult(
        resultCode, data,
        success = { pm ->
//                paymentCompleted()
          checkoutViewModel.createPayment(
            pm,
            null
          ).uiSubscribe().subscribe {
            handlePayment(it)
          }
        },
        failed = {
          paymentFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
        }
      )
    }

    // payment authentication completed, get result
    PaymentRefer.onPaymentCardResult(
      this, requestCode, data,
      capture = {
        // only applies to payment intents
        // after stripe create success, show success animation, auto close
        CheckoutLoading.afterStripeSuccess(com.ricepo.style.R.string.placing_order_success) {
          paymentCompleted()
        }
        // update temp payment card
        checkoutViewModel.saveTempPaymentCard()
      },
      completed = {
        paymentCompleted()
      },
      failed = {
        paymentFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
      }
    )

  }

  private fun initMenuStatus() {
    // init menu status get the restaurant
    lifecycleScope.launch {
      val groupId = GroupOrderCache.getGroupId()
      menuGroupViewModel.initGroupId(groupId)
      val restaurant = RestaurantCartCache.getRestaurantSuspend(
        checkoutViewModel.restaurant
      )
      menuGroupViewModel.checkGroupStatus(restaurant)
        .collectLatest { _ ->
        }
    }
  }

  private fun placeOrRefreshOrder(isPlace: Boolean, option: CheckoutViewModel.CheckoutOption? = null) {
    lifecycleScope.launch {
      // refresh group order quantity
      val flow = menuGroupViewModel.updateGroupCartQuantity(option?.restaurant)
      if (isPlace) {
        // place order
        if (flow == null) {
          if (option != null) {
            placeOrder.onNext(option)
          }
        } else {
          flow.collectLatest {
            if (option != null) {
              placeOrder.onNext(option)
            }
          }
        }
      } else {
        // refresh order
        flow?.collectLatest { result ->
//                    ToastUtil.showToast("checkout ${LocalDateTime.now()}")
          result.fold(
            onSuccess = {
              // refresh subtotal update
              checkoutViewModel.observeGroupOrderUpdate.onNext(true)
              // reload quote when group qty is changed
              if (it.second) {
                checkoutViewModel.reloadQuote()
              }
            },
            onFailure = { error ->
              // don't alert is ordering
              if (!checkoutViewModel.isOrdering) {
                menuGroupViewModel.handleGroupError(
                  this@CheckoutActivity,
                  option?.restaurant, error
                ) {
                  backResultEvent(intent)
                }
              }
            }
          )
        }
      }
    }
  }

  /**
   * refresh group order cart
   */
  override fun refreshGroupOrderCart() {
    super.refreshGroupOrderCart()
    placeOrRefreshOrder(false)
  }

  override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
    return if (event?.keyCode == KeyEvent.KEYCODE_BACK &&
      checkoutViewModel.isOrdering
    ) {
      true
    } else {
      super.dispatchKeyEvent(event)
    }
  }

  /**
   * trigger refresh of back
   */
  override fun onBackPressed() {
    val restaurant = checkoutViewModel.restaurant
    if ((
      entrance == FeaturePageConst.PAGE_SUBSCRIPTION ||
        entrance == FeaturePageConst.PAGE_RESTAURANT_SUBMORE
      ) &&
      restaurant != null
    ) {
      FeaturePageRouter.navigateMenuByEntrance(
        this, restaurant,
        entrance = entrance
      )
      backEvent()
    } else {
      backResultEvent(intent)
    }
  }
}
