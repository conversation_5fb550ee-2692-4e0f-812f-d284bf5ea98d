package com.ricepo.app.features.luckymenu.view

import android.widget.ImageView
import com.ricepo.app.R
import com.ricepo.style.ResourceApplication

//
// Created by <PERSON> on 19/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object FrameAnimationUtils {

  private var frameAnimation: FrameAnimation? = null

  fun init(
    vDice: ImageView,
  ): FrameAnimationUtils {
    frameAnimation = FrameAnimation(vDice, getRes(), 20, true)
    return this
  }

  fun play() {
    frameAnimation?.let {
      it.play(0)
    }
  }

  fun release() {
    frameAnimation?.let {
      it.release()
    }
  }

  fun stop() {
    frameAnimation?.let {
      it.pauseAnimation()
    }
  }

  fun getRes(): IntArray {
    val typedArray = ResourceApplication.getResources().obtainTypedArray(com.ricepo.style.R.array.dice)
    val len = typedArray.length()
    val resId = IntArray(len)
    for (i in 0 until len) {
      resId[i] = typedArray.getResourceId(i, -1)
    }
    typedArray.recycle()
    return resId
  }
}
