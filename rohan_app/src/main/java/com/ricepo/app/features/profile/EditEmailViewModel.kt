package com.ricepo.app.features.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.ricepo.app.model.EmailReq
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.isEmailValid
import com.ricepo.app.utils.log
import com.ricepo.base.model.Customer
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

data class EmailUiState(
  val isEdit: <PERSON>olean,
  val currentEmail: String? = null,
  val sendEnable: Boolean,
  val submitEnable: <PERSON><PERSON>an,
  val tickTime: Int = -1,
  val succeedEmail: String? = null
)

class EditEmailViewModel @AssistedInject constructor(
  private val riceApi: RiceApi,
  @Assisted private val customer: Customer?,
) : ViewModel() {

  val uiState = MutableStateFlow(
    EmailUiState(
      isEdit = customer?.email?.address?.isBlank() ?: true,
      sendEnable = true,
      submitEnable = false,
      succeedEmail = customer?.email?.address
    )
  )

  private var inputEmail: String? = null
  private var inputCode: String? = null

  fun submit(
    onStart: () -> Unit,
    onSuccess: () -> Unit,
    onError: () -> Unit,
  ) {
    if (!uiState.value.isEdit) {
      uiState.value = uiState.value.copy(isEdit = true)
    } else {
      inputEmail?.takeIf { it.isNotBlank() }?.let {
        editEmail(
          EmailReq(
            email = it,
            vcode = inputCode
          ),
          onStart, onSuccess, onError
        )
      }
    }
  }

  fun sendCode(
    onStart: () -> Unit,
    onSuccess: () -> Unit,
    onError: () -> Unit,
  ) {
    if (uiState.value.sendEnable) {
      inputEmail?.let {
        editEmail(
          EmailReq(
            email = it,
          ),
          onStart, onSuccess, onError
        )
      }
    }
  }

  @OptIn(FlowPreview::class)
  fun combineState(
    emailState: StateFlow<String>,
    codeState: StateFlow<String>,
  ) {
    viewModelScope.launch {
      combine(emailState, codeState) { email, code ->
        if (code.isNotBlank()) {
          inputCode = code
        }
        inputEmail = email
        uiState.value = uiState.value.copy(
          submitEnable = email.isEmailValid() && code.isNotBlank(),
          sendEnable = email.isEmailValid() && uiState.value.tickTime == -1
        )
      }.debounce(1000).collect()
    }
  }

  private fun editEmail(
    emailReq: EmailReq,
    onStart: () -> Unit,
    onSuccess: () -> Unit,
    onError: () -> Unit,
  ) {
    viewModelScope.launch {
      onStart.invoke()
      riceApi.userEmail(emailReq)
        .onSuccess {
          "email success".log()
          if (
            emailReq.vcode.isNullOrEmpty()
          ) {
            startTick()
          } else {
            uiState.value = uiState.value.copy(
              isEdit = false,
              succeedEmail = emailReq.email
            )
          }
          onSuccess.invoke()
        }.onError {
          "email error".log()
          onError.invoke()
        }.onException {
          "email exception".log()
          onError.invoke()
        }
    }
  }

  private fun startTick() {
    viewModelScope.launch {
      for (i in 60 downTo -1) {
        uiState.value = uiState.value.copy(tickTime = i, sendEnable = false)
        delay(1000)
      }
      uiState.value = uiState.value.copy(
        tickTime = -1,
        sendEnable = inputEmail?.isEmailValid() ?: false
      )
    }
  }

  @dagger.assisted.AssistedFactory
  interface AssistedFactory {
    fun create(customer: Customer?): EditEmailViewModel
  }

  companion object {
    fun provideFactory(
      assistedFactory: AssistedFactory,
      customer: Customer?
    ): ViewModelProvider.Factory = object : ViewModelProvider.Factory {
      @Suppress("UNCHECKED_CAST")
      override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return assistedFactory.create(customer) as T
      }
    }
  }
}
