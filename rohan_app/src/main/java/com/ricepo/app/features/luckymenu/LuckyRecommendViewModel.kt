package com.ricepo.app.features.luckymenu

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.luckymenu.data.LuckyModel
import com.ricepo.app.features.luckymenu.repository.LuckyRecommendUseCase
import com.ricepo.app.features.luckymenu.view.LuckyFoodView
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Food
import com.ricepo.base.model.LuckRecommendBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by Mark on 11/16/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class LuckyRecommendViewModel @Inject constructor(private val useCase: LuckyRecommendUseCase) :
  BaseViewModel() {

  companion object {
    const val TAG = "LuckyRecommendViewModel"
  }

  var mapper: MenuMapper = MenuMapper()
  private var luckyModel = LuckyModel()

  var response: LuckRecommendBean? = null
  var luckyModelCommand = MutableLiveData<LuckyModel>()

  var catch = MutableLiveData<String>()
  var refresh = MutableLiveData<Boolean>()
  var emptySelectedFood = MutableLiveData<Boolean>()
  var clearCartTips = MutableLiveData<String>()
  var forward = MutableLiveData<Restaurant>()
  var createPao = MutableLiveData<List<Food>>()

  var selectFoodsCommand = MutableLiveData<MutableList<LuckyFoodView>>().apply {
    value = mutableListOf()
  }
  var selectedFoodView = mutableListOf<LuckyFoodView>()
  var selectedFoodIds = mutableListOf<String>()
//    var emptySelectedFood = Transformations.map(selectFoodsCommand) { input -> input.isEmpty() }
//    var empty = Transformations.map(itemsList) { input -> input.isEmpty() }

  var allFoodList = mutableListOf<Food>()

  fun clearFoodList(): MutableList<Food> {
    allFoodList.clear()
    return allFoodList
  }

  /**
   * return lucky menu recommendation data by server
   */
  fun requestRecommendMenu(num: String?) {
    viewModelScope.async {
      flow<LuckRecommendBean?> {
        emit(
          useCase.requestLuckyRecommend(
            num,
            response?.restaurant?.id,
            selectedFoodIds
          )
        )
      }.onStart {
        refresh.value = true
      }.catch { value ->
//                Log.d(TAG, "catch-${value.message}")
        value.message?.let {
          catch.value = it
        }
      }.onCompletion { value ->
//                Log.d(TAG, "onCompletion-$value")
        refresh.value = false
      }.collectLatest { result ->
        response = result
        responseAction(result)
      }
    }
  }

  private val cartList = mutableListOf<Cart>()

  private fun responseAction(result: LuckRecommendBean?) {
    viewModelScope.launch {
//            Log.d(TAG, "result-$result")
      result?.let {
        val foods = result.items
        allFoodList.clear()
        allFoodList.addAll(foods)
        // name
        luckyModel.restaurantNameContent = result.restaurant.name
        luckyModel.restaurantName = result.restaurant.name?.localize()
        // tags
        val bindMapper = RestaurantBindMapper(result.restaurant)
        val pairInfo = bindMapper.bindInfo(true)
        luckyModel.restaurantTags = pairInfo.first
        luckyModelCommand.value = luckyModel
        if (allFoodList.isNotEmpty()) {
//                    Log.d(TAG, "value-${result.items.size}")
          // create paopao
          createPao.value = allFoodList
        }
      }
    }
  }

  fun foodConvertCart(): MutableList<Cart> {
    cartList.clear()
    selectedFoodView.forEach { foodView ->
      foodView.mFood?.let { it ->
//                if (!it.options.isNullOrEmpty()) {
//                    val reFood = mapper.mapOptionsLucky(it)
//                    cartList.add(mapper.mapCart(reFood, luckyModel.restaurantNameContent))
//                } else {
//                    cartList.add(mapper.mapCart(it, luckyModel.restaurantNameContent))
//                }
        cartList.add(mapper.mapCart(it, luckyModel.restaurantNameContent))
      }
    }
//        Log.d(TAG, "cartList-${cartList.size}")
    return cartList
  }

  fun updatePriceAndDelivery() {
    // create cart
    foodConvertCart()
    // total price
    val mapCarts = mapper.mapCarts(cartList, null)
//        Log.d(TAG, "mapCarts-${mapCarts.second}")

    response?.let {
      luckyModel.price = showPrice(mapCarts.second, it.restaurant)
      if (mapCarts.second > 0) luckyModel.deliveryText =
        mapper.toDeliveryMessage(mapCarts.second, it.restaurant)
      else luckyModel.deliveryText = ""
//            Log.d(TAG, "price-${luckyModel.price},deliveryText-${luckyModel.deliveryText}")
      luckyModelCommand.value = luckyModel
    }
  }

  /**
   * total price
   */
  private fun showPrice(price: Int, restaurant: Restaurant): String {
    val toTotalPrice = mapper.toTotalPrice(price, restaurant)
//        Log.d(TAG, "toTotalPrice::$toTotalPrice")
    return toTotalPrice
  }

  /**
   * is add and return of menu option
   */
  fun addShoppingCart() {
    viewModelScope.launch {

      // first check the group status
      val cacheCart = RestaurantCartCache.getRestaurantCartSuspend(forward.value, true)
      if (cacheCart != null && cacheCart?.restaurant?.id != response?.restaurant?.id &&
        cacheCart?.cartList?.size ?: 0 > 0
      ) {
        // alert the cart will be cleared
        val message = ResourcesUtil.getString(
          com.ricepo.style.R.string.hint_cart_will_be_cleared,
          cacheCart?.restaurant?.name?.localize() ?: ""
        )
        clearCartTips.value = message
      } else {
        // Added shopping cart cache cart
        val toMutableList = cacheCart?.cartList?.toMutableList()
        toMutableList?.let {
          cartList.addAll(it)
        }
        saveRestaurantCart(cartList, cacheCart?.comments)
        // forward to checkout page
        forward.value = response?.restaurant
      }
    }
  }

  /**
   * Store shopping cart data in database
   */
  suspend fun saveRestaurantCart(toMutableList: MutableList<Cart>?, comments: String?) {
    val restaurantCart = RestaurantCart(toMutableList, response?.restaurant, comments)
    RestaurantCartCache.saveRestaurantCart(restaurantCart)
  }

  suspend fun deleteRestaurantCart(restaurant: Restaurant?) {
    RestaurantCartCache.deleteRestaurantCart(restaurant)
  }
}
