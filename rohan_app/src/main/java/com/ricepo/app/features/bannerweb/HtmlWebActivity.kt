package com.ricepo.app.features.bannerweb

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.webkit.WebViewClientCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.databinding.ActivityBannerWebBinding
import com.ricepo.app.databinding.ActivityHtmlWebBinding
import com.ricepo.app.features.BaseWebActivity
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.base.model.Banner

//
// Created by <PERSON><PERSON> on 21/8/2023.
//

@Route(path = FeaturePageConst.PAGE_HTML_WEB)
class HtmlWebActivity : BaseWebActivity() {

  lateinit var binding: ActivityHtmlWebBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityHtmlWebBinding.inflate(layoutInflater)
    setContentView(binding.root)


    binding.webView.webViewClient = HtmlWebViewClient(binding, loginLauncher)
    binding.webView.webChromeClient = HtmlWebChromeClient(binding) {
      onBackPressed()
    }

    binding.webView.settings.cacheMode = WebSettings.LOAD_NO_CACHE
    binding.webView.settings.javaScriptEnabled = true
    binding.webView.settings.javaScriptCanOpenWindowsAutomatically = true

    binding.webView.setBackgroundColor(Color.TRANSPARENT)

    intent.getStringExtra(FeaturePageConst.PARAM_WEB_CONTENT)?.let {
      binding.webView.loadDataWithBaseURL(null, it, "text/html", "UTF-8", null)
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    binding.webView.loadDataWithBaseURL(null, "", "text/html", "UTF-8", null);
  }

  // activity:1.2.0-alpha04 prepareCall renamed to registerForActivityResult
  private val loginLauncher = registerForActivityResult(
    ActivityResultContracts.StartActivityForResult()
  ) { activityResult ->
    if (activityResult.resultCode == Activity.RESULT_OK) {
      finish()
    }
  }

  class HtmlWebViewClient internal constructor(
    private val binding: ActivityHtmlWebBinding,
    private val loginLauncher: ActivityResultLauncher<Intent>
  ) :
    WebViewClientCompat() {

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
      super.onPageStarted(view, url, favicon)
    }

    override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
//            view.loadUrl(request.url.toString())
      dispatchUrlLoading(view, request.url)
      return true
    }

    override fun onPageFinished(view: WebView?, url: String?) {
      super.onPageFinished(view, url)
      binding.webProgress.hide()
    }

    private fun dispatchUrlLoading(view: WebView, uri: Uri) {
      val title = uri.getQueryParameter("title")
      if (!title.isNullOrEmpty()) {
        binding.tvTitle.text = title
      }
    }
  }

  class HtmlWebChromeClient internal constructor(
      val binding: ActivityHtmlWebBinding, val close: () -> Unit) : WebChromeClient() {
    override fun onProgressChanged(view: WebView?, newProgress: Int) {
      super.onProgressChanged(view, newProgress)
      binding.webProgress.setWebProgress(newProgress)
    }

    override fun onCloseWindow(window: WebView?) {
      super.onCloseWindow(window)
      binding.webView.destroy()
      close()
    }
  }


  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
  }
}
