package com.ricepo.app.features.subscription

import androidx.activity.ComponentActivity
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.login.repository.AuthUseCase
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.base.extension.bindLoading
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.kotlin.Observables
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 15/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class SubscriptionViewModel @Inject constructor(
  private val useCase: SubscriptionUseCase,
  private val authUseCase: AuthUseCase
) : BaseViewModel() {

  val onSubscriptionPlan = BehaviorSubject.create<SubscriptionPlan>()
  val onResume = PublishSubject.create<Boolean>()

  val onSubscribeVip = PublishSubject.create<Boolean>()

  private var paymentTempOwnMethod: PaymentOwnMethod? = null

  /**
   * the flag of direct back (case: banner subscription)
   */
  var isOnlySubscribe: Boolean = true

  /**
   * profile subscription
   */
  var isProfileSubscribe: Boolean = true

  var restaurant: Restaurant? = null

  class SubscriptionOption(
    var paymentOwnMethod: PaymentOwnMethod? = null,
    var subscriptionPlan: SubscriptionPlan? = null,
    var paymentObj: PaymentObj? = null,
    var message: String? = null
  )

  var subscriptionOption: SubscriptionOption = SubscriptionOption()

  fun updateTempPayment(payment: PaymentOwnMethod?) {
    paymentTempOwnMethod = payment
  }

  fun cleanTempPayment() {
    paymentTempOwnMethod = null
  }

  fun saveTempPaymentCard() {
    val payment = paymentTempOwnMethod
    if (payment != null) {
      CustomerCache.savePayment(payment) {}
    }
  }

  /**
   * return PaymentOwnMethod if get payment
   * otherwise return false
   */
  fun observePaymentUpdated(): Observable<SubscriptionOption> {
    return Observables.combineLatest(
      onSubscriptionPlan.map {
        subscriptionOption.subscriptionPlan = it
      },
      onResume
    ).flatMap {
      Observable.create<SubscriptionOption> { emitter ->
        CustomerCache.getPayment {
          val paymentOwnMethod = paymentTempOwnMethod ?: it
          // clear the payment multiple updating
          subscriptionOption.paymentObj = null

          // get the only stripe payment
          if (PaymentOwnMethod.CREDIT == paymentOwnMethod?.method ||
            PaymentOwnMethod.CREDIT_PREVIOUSLY == paymentOwnMethod?.method
          ) {
            subscriptionOption.paymentOwnMethod = paymentOwnMethod
          }
          emitter.onNext(subscriptionOption)

          // if only update subscribe not check the subscription (from banner)
          if (!isOnlySubscribe) {
            // auto check subscription success
            CustomerCache.liveCustomer {
              val customerId = it?.id
              if (customerId != null) {
                // checkout subscribe
                authUseCase.getCustomer(
                  object :
                    DisposableSingleObserver<Customer>() {
                    override fun onSuccess(t: Customer) {
                      if (t != null && t.subscription?.currentPeriodEndAt != null) {
                        // subscribe success
                        subscriptionOption.paymentObj =
                          PaymentObj(signed = null)
                        emitter.onNext(subscriptionOption)
                      }
                    }

                    override fun onError(e: Throwable) {
                      e?.printStackTrace()
                    }
                  },
                  customerId
                )
              }
            }
          }
        }
      }
    }
  }

  /**
   * for subscription update
   */
  fun observePaymentByUpdated(): Observable<SubscriptionOption> {
    return Observables.combineLatest(
      onSubscriptionPlan.map {
        subscriptionOption.subscriptionPlan = it
      },
      onResume
    ).flatMap {
      Observable.create<SubscriptionOption> { emitter ->
        CustomerCache.liveCustomer {
          val customer = it
          val paymentOwnMethod = customer?.subscription?.paymentMethod

          // get the only stripe payment
          if (paymentOwnMethod?.applePay == false) {
            subscriptionOption.paymentOwnMethod = PaymentOwnMethod(
              method = PaymentOwnMethod.CREDIT,
              brand = paymentOwnMethod.brand,
              last4 = paymentOwnMethod.last4,
              stripeId = paymentOwnMethod.id
            )
          }
          emitter.onNext(subscriptionOption)
        }
      }
    }
  }

  fun observeVip(activity: ComponentActivity): Observable<SubscriptionOption> {
    return onSubscribeVip.flatMap {
      // clear option message
      subscriptionOption.message = null

      val paymentOwnMethod = subscriptionOption.paymentOwnMethod
      val plan = subscriptionOption.subscriptionPlan
      if (paymentOwnMethod == null) {
        Observable.just(subscriptionOption)
      } else if (PaymentOwnMethod.CREDIT == paymentOwnMethod?.method ||
        PaymentOwnMethod.CREDIT_PREVIOUSLY == paymentOwnMethod?.method
      ) {
        val planId = plan?.id
        val stripeId = paymentOwnMethod?.stripeId

        useCase.createSubscription(subscriptionOption, planId, stripeId)
          .bindLoading(activity)
      } else {
        Observable.just(subscriptionOption)
      }
    }
  }

  fun cancelSubscription(activity: ComponentActivity, subscriptionId: String?):
    Observable<SubscriptionOption> {
    return useCase.cancelSubscription(subscriptionId)
      .bindLoading(context = activity)
  }
}
