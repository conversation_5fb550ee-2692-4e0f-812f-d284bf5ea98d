package com.ricepo.app.features.recommend

import androidx.activity.ComponentActivity
import com.ricepo.app.R
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class RecommendViewModel @Inject constructor(
  private val useCase: RecommendUseCase
) : BaseViewModel() {

  /**
   * get the recommend or rank data list
   */
  fun observeRecommend(context: ComponentActivity, type: String):
    Flow<List<RecommendUiModel>> {
    return flowLoading<List<RecommendUiModel>>(
      context,
      { error ->
        DialogFacade.showAlert(context, error.parseByBuzNetwork().message ?: "")
      }
    ) {

      val uiModels = mutableListOf<RecommendUiModel>()
      if (type == RecommendType.TYPE_RECOMMEND) {
        uiModels.add(
          RecommendUiModel.TitleItem(
            ResourcesUtil.getString(com.ricepo.style.R.string.weekly_menu),
            dateLabel = useCase.getRecommendDateInfo(type)
          )
        )
      } else if (type == RecommendType.TYPE_RANK) {
        uiModels.add(
          RecommendUiModel.TitleItem(
            ResourcesUtil.getString(com.ricepo.style.R.string.leaderboard),
            dateLabel = useCase.getRecommendDateInfo(type)
          )
        )
      }

      emit(uiModels)

      uiModels.addAll(
        useCase.getRecommendList(type).mapIndexed { index, it ->
          RecommendUiModel.FoodItem(it, index)
        }
      )

      emit(uiModels)
    }
  }
}
