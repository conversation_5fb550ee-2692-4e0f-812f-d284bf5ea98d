package com.ricepo.app.features.settings

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.ActivityFeedbackBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 26/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_USER_FEEDBACK)
class FeedbackActivity : BaseActivity() {

  val viewModel: SettingsViewModel by viewModels()

  private lateinit var binding: ActivityFeedbackBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityFeedbackBinding.inflate(layoutInflater)
    setContentView(binding.root)

    setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.feedback))

    setupListener()

    lifecycleScope.launchWhenCreated {
      val customer = withContext(Dispatchers.IO) { CustomerCache.getCustomerSuspend() }
      customer?.phone?.let {
        binding.etFeedbackPhone.setText(it)
      }
    }
  }

  override fun onAttachedToWindow() {
    super.onAttachedToWindow()
    KeyboardUtil.showKeyboard(this, binding.etFeedbackInput)
  }

  private fun setupListener() {
    // submit feedback content
    binding.btnFeedback.clickWithTrigger {
      viewModel.submitSubject(
        this@FeedbackActivity,
        binding.etFeedbackInput.text?.toString(),
        binding.etFeedbackPhone.text?.toString()
      ) {
        backEvent()
      }
    }
  }
}
