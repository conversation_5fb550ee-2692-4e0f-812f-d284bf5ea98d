package com.ricepo.app.features.support

import androidx.lifecycle.viewModelScope
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.OrderItem
import com.ricepo.app.model.SupportRule
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.SingleLiveEvent
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.EnvNetwork
import com.ricepo.style.ThemeUtil
import com.skydoves.sandwich.suspendOnSuccess
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 9/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class OrderSupportViewModel @Inject constructor(
  private val repository: RiceApi
) : BaseViewModel() {

  val chatUrl = SingleLiveEvent<String>()

  init {
    getSupportChatToken()
  }

  /**
   * get support chat token
   */
  private fun getSupportChatToken() {

    viewModelScope.launch {
      val customer = withContext(Dispatchers.IO) {
        CustomerCache.getCustomer()
      }
      repository.getSupportChatToken().suspendOnSuccess {
        data?.token?.let { token ->
          val userId = customer?.id ?: ""
          val phone = customer?.phone ?: ""
          val themeMode = if (ThemeUtil.isDarkMode()) {
            "dark"
          } else {
            "light"
          }
          val url = StringBuilder(EnvNetwork.CHAT_URL)
            .append("/?token=$token")
            .append("&channelId=$phone")
            .append("&channelName=$phone")
            .append("&sourceType=customer&sourceId=$userId")
            .append("&mode=$themeMode")
            .toString()
          chatUrl.postValue(url)
        }
      }
    }
  }
}

sealed class OrderSupportItem(open val issue: SupportRule) {
  data class Normal(override val issue: SupportRule) : OrderSupportItem(issue)
  data class Other(override val issue: SupportRule) : OrderSupportItem(issue)
}

sealed class ChoosedItem {
  abstract var choosed: Boolean

  data class Food(val item: OrderItem, override var choosed: Boolean) : ChoosedItem()
  data class Method(val methodString: String, override var choosed: Boolean) : ChoosedItem()
  data class Problem(val problem: String, override var choosed: Boolean) : ChoosedItem()
}
