package com.ricepo.app.features.support

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderItem
import com.ricepo.app.model.SupportItem
import com.ricepo.app.model.SupportItemComponent
import com.ricepo.app.model.SupportReq
import com.ricepo.app.model.SupportRuleItem
import com.ricepo.app.model.cancelOrderType
import com.ricepo.app.model.driverProblemType
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.localized
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 13/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@HiltViewModel
class OrderSupportIssueViewModel @Inject constructor(
  private val useCase: OrderSupportRepo
) : BaseViewModel() {

  private lateinit var order: Order

//    private lateinit var supportRule: SupportRule
//
//    private lateinit var supportRules: ArrayList<SupportRule>

  private lateinit var supportRuleItem: SupportRuleItem

  private lateinit var supportRuleItems: List<SupportRuleItem>

  /**
   * the text that user entered on the help page
   */
  private var subject: String? = null

  /**
   * food in the support page
   */
  private var selectedFoods: MutableList<OrderItem> = mutableListOf()

  /**
   * selected driver problem in the support page
   */
  private var selectedProblems: MutableList<String> = mutableListOf()

  /**
   * which processing method did customer choose
   */
  private var selectedMethodItem: String? = null

  /**
   * url returned after images upload is successful
   */
  private var chooseImageUrl: MutableList<String> = mutableListOf()

  /**
   * local image path
   */
  var imageList: MutableList<String> = mutableListOf()

  var mapPathUri: MutableMap<String, Uri?> = mutableMapOf()

  private var chooseItems = mutableListOf<ChoosedItem>()

//    fun initData(order: Order, rule: SupportRule, rules: ArrayList<SupportRule>) {
//        this.order = order
//        this.supportRule = rule
//        this.supportRules = rules
//    }

  fun initData(
    order: Order,
    ruleItem: SupportRuleItem,
    items: ArrayList<SupportRuleItem>
  ) {
    this.order = order
    this.supportRuleItem = ruleItem
    this.supportRuleItems = items
  }

  fun getItemSection(order: Order): List<ChoosedItem>? {
    val items = useCase.getItemSection(order)
    if (items != null) {
      chooseItems.addAll(items)
    }
    return items
  }

  fun getProblemSection(): List<ChoosedItem>? {
    val items = listOf(
      com.ricepo.style.R.string.support_problem_with_drivers_bad_attitudes.localized(),
      com.ricepo.style.R.string.support_problem_with_drivers_nonresponse.localized(),
      com.ricepo.style.R.string.support_problem_with_drivers_nonprofessional.localized(),
      com.ricepo.style.R.string.support_problem_with_drivers_poor_communication.localized()
    ).map {
      ChoosedItem.Problem(it, false)
    }
    if (items != null) {
      chooseItems.addAll(items)
    }
    return items
  }

  fun getProcessingMethodSection(): List<ChoosedItem>? {
    val items = listOf(
      com.ricepo.style.R.string.support_processing_method_refund.localized(),
      com.ricepo.style.R.string.support_processing_method_refund_order.localized(),
      com.ricepo.style.R.string.support_processing_method_redelivery.localized(),
      com.ricepo.style.R.string.support_processing_method_other.localized()
    ).map {
      ChoosedItem.Method(it, false)
    }
    if (items != null) {
      chooseItems.addAll(items)
    }
    return items
  }

  fun getProcessingMethodSection(item: SupportItem): List<ChoosedItem>? {
    val items = useCase.getProcessingMethodSection(item)
    if (items != null) {
      chooseItems.addAll(items)
    }
    return items
  }

  fun getProblemSection(item: SupportItem): List<ChoosedItem>? {
    val items = useCase.getProblemSection(item)
    if (items != null) {
      chooseItems.addAll(items)
    }
    return items
  }

  /**
   * change the choose item input subject
   */
  fun changeSubject(text: String) {
    this.subject = text
  }

  /**
   * create a ticket
   */
  private fun createTicket(
    context: ComponentActivity,
    component: SupportItemComponent?,
    isCancel: Boolean = false
  ) {
    val orderId = order.id
    val supportReq = SupportReq(
      type = supportRuleItem.type ?: "",
      subject = subject ?: "",
      phone = order.customer.phone,
      items = if (selectedFoods.size == 0) null else selectedFoods,
      solution = selectedMethodItem,
      driverIssues = if (selectedProblems.size == 0) null else selectedProblems,
      images = if (chooseImageUrl.size == 0) null else chooseImageUrl,
      rule = supportRuleItem.rule,
      event = component?.ticket
    )

    val handler = CoroutineExceptionHandler { _, exception ->
      val e = exception.parseByBuzNetwork()
      Loading.hideLoading()
      DialogFacade.showAlert(context, e.message ?: "")
    }

    viewModelScope.launch(handler) {
      Loading.showLoading(context)
      val resp = withContext(Dispatchers.IO) {
        useCase.createTicket(orderId, supportReq)
      }
      if (isCancel) {
        LifecycleNetworkListener.updateCustomer()
      }
      Loading.hideLoading()

      // for example call restaurant phone
      if (component == null || component?.ticket == true) return@launch

      // display response info
      val successInfo = if (resp is String) resp else ResourcesUtil.getString(com.ricepo.style.R.string.ticket_received)
      DialogFacade.showAlert(context, successInfo) {
        // back from support to order
        FeaturePageRouter.navigateOrder(context, order)
      }
    }
  }

  /**
   * submit order support issue
   */
  fun submitSubject(context: ComponentActivity, component: SupportItemComponent) {
    // check text required and subject is not entered
    if (component.optional != true && subject.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.enter_detail)
      return
    }

    createTicket(context, component)
  }

  /**
   * change order item need cancel your existing order
   */
  fun linkToCancel(): Flow<SupportRuleItem>? {
//        var cancelRule: SupportRule? = supportRules.firstOrNull {
//            it.item.type == cancelOrderType
//        }
//        if (cancelRule != null) {
//            this.supportRule = cancelRule
//            return flowOf(supportRule)
//        }
    val ruleItem = supportRuleItems.firstOrNull {
      it.type == cancelOrderType
    }
    if (ruleItem != null) {
      this.supportRuleItem = ruleItem
      return flowOf(supportRuleItem)
    }
    return null
  }

  /**
   * cancel your order
   */
  fun cancelOrder(context: ComponentActivity, component: SupportItemComponent) {
    // check text required and subject is not entered
    if (component.optional != true && subject.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.enter_detail)
      return
    }
    createTicket(context, component, true)
  }

  /**
   * call phone by restaurant
   */
  fun callRestaurant(context: ComponentActivity, component: SupportItemComponent) {
    val phoneNumber = order.restaurant?.phone
    if (phoneNumber.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.support_no_phone_number)
      return
    }
    if (component.ticket == true) {
      createTicket(context, component)
    }
    DialogFacade.showAlert(context, phoneNumber) {
      IntentUtils.intentPhone(context, context.lifecycleScope, phoneNumber = phoneNumber)
    }
  }

  /**
   * show chat view by support service
   */
  fun callSupport(context: ComponentActivity, component: SupportItemComponent?) {
    if (component?.ticket == true) {
      createTicket(context, component)
    }

    FeaturePageRouter.navigateOrderSupportChat(context)
  }

  /**
   * call phone by delivery driver
   */
  fun callDriver(context: ComponentActivity, component: SupportItemComponent) {
    val phoneNumber = order.delivery?.courier?.phone
    if (phoneNumber.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.support_no_phone_number)
      return
    }
    if (component.ticket == true) {
      createTicket(context, component)
    }
    DialogFacade.showAlert(context, phoneNumber) {
      IntentUtils.intentPhone(context, context.lifecycleScope, phoneNumber = phoneNumber)
    }
  }

  fun submitChooseSubject(context: ComponentActivity, component: SupportItemComponent) {
    if (supportRuleItem.type == driverProblemType) {
      // current page is problem with drivers, require input detail
      if (subject.isNullOrEmpty()) {
        DialogFacade.showAlert(context, com.ricepo.style.R.string.enter_detail)
        return
      }
      createTicket(context, component)
    } else {
      // current page is choose item, require choose item and upload pics
      if (selectedFoods.size == 0) {
        DialogFacade.showAlert(context, com.ricepo.style.R.string.please_choose_item)
        return
      }
      if (imageList.size == 0) {
        DialogFacade.showAlert(context, com.ricepo.style.R.string.please_choose_photo)
        return
      }
      if (subject.isNullOrEmpty()) {
        DialogFacade.showAlert(context, com.ricepo.style.R.string.enter_detail)
        return
      }

      val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        val e = exception.parseByBuzNetwork()
        // Only the original thread that created a view hierarchy can touch its views.
        context.runOnUiThread {
          try {
            Loading.hideLoading()
            DialogFacade.showAlert(context, e.message ?: "")
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }
      }
      // upload pics
      viewModelScope.launch(exceptionHandler) {
        Loading.showLoading(context)
        val defZip = mutableListOf<Deferred<String>>()
        imageList.forEach { path ->
          defZip.add(
            async(Dispatchers.IO) {
              useCase.uploadImage(path)
            }
          )
        }

        chooseImageUrl.clear()
        defZip.map { def ->
          chooseImageUrl.add(def.await())
        }

        createTicket(context, component)
      }
    }
  }

  fun addImagePath(context: Context, imageUri: Uri? = null, imagePath: String? = null) {
    val filePathColumns = arrayOf<String>(MediaStore.Images.Media.DATA)
    var path = imagePath
    if (imageUri != null) {
      val cursor = context.contentResolver.query(imageUri, filePathColumns, null, null, null)
      if (cursor != null) {
        cursor.moveToFirst()
        val columnIndex = cursor.getColumnIndex(filePathColumns.first())
        path = cursor.getString(columnIndex)
      }
    }
    if (path != null) {
      imageList.add(path)
      mapPathUri.put(path, imageUri)
    }
  }

  fun removePath(path: String) {
    if (imageList.contains(path)) {
      imageList.remove(path)
      mapPathUri.remove(path)
    }
  }

  fun resetChooseItemMethod() {
    chooseItems.forEach { item ->
      if (item is ChoosedItem.Method) {
        item.choosed = false
      }
    }
  }

  fun changeChooseItem() {
    selectedFoods.clear()
    selectedProblems.clear()
    chooseItems.forEach { item ->
      when (item) {
        is ChoosedItem.Food -> {
          if (item.choosed) {
            selectedFoods.add(item.item)
          }
        }
        is ChoosedItem.Problem -> {
          if (item.choosed) {
            selectedProblems.add(item.problem)
          }
        }
        is ChoosedItem.Method -> {
          if (item.choosed) {
            selectedMethodItem = item.methodString
          }
        }
      }
    }
  }
}
