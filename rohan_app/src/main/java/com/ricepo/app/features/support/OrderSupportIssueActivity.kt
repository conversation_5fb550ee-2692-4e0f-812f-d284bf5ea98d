package com.ricepo.app.features.support

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.viewModels
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityOrderSupportIssueBinding
import com.ricepo.app.databinding.LayoutOrderSupportIssueChooseBinding
import com.ricepo.app.databinding.LayoutOrderSupportIssueUppicBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.support.adapter.UploadImageAdapter
import com.ricepo.app.model.Order
import com.ricepo.app.model.SupportComponentType
import com.ricepo.app.model.SupportItemComponent
import com.ricepo.app.model.SupportRuleItem
import com.ricepo.app.model.cancelOrderType
import com.ricepo.app.model.changeItemType
import com.ricepo.app.model.driverProblemType
import com.ricepo.app.model.feedbackType
import com.ricepo.app.model.missingOrderType
import com.ricepo.app.model.packagingIssueType
import com.ricepo.app.model.tastingBadType
import com.ricepo.app.model.wrongOrderType
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.localized
import com.ricepo.base.inputmanager.AndroidBug5497Workaround
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.model.localize
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.BaseBottomSheetFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

//
// Created by Thomsen on 10/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_ORDER_SUPPORT_ISSUE)
class OrderSupportIssueActivity : BaseActivity() {

  private lateinit var binding: ActivityOrderSupportIssueBinding

  private lateinit var imageAdpater: UploadImageAdapter

  val supportIssueViewModel: OrderSupportIssueViewModel by viewModels()

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityOrderSupportIssueBinding.inflate(layoutInflater)
    setContentView(binding.root)
    // need to be on the after set content view
    AndroidBug5497Workaround.assistActivity(this)

    imageAdpater = UploadImageAdapter(
      supportIssueViewModel.imageList,
      supportIssueViewModel.mapPathUri
    ) { path ->
      supportIssueViewModel.removePath(path)
      imageAdpater.notifyDataSetChanged()
    }

    val order = intent.getParcelableExtra<Order>(FeaturePageConst.PARAM_ORDER_SUPPORT_ORDER)

    val ruleItem = intent.getParcelableExtra<SupportRuleItem>(
      FeaturePageConst.PARAM_ORDER_SUPPORT_RULE_ITEM
    )
    val ruleItems = intent.getParcelableArrayListExtra<SupportRuleItem>(
      FeaturePageConst.PARAM_ORDER_SUPPORT_RULE_ITEMS
    )
    if (order != null && ruleItem != null && ruleItems != null) {
      supportIssueViewModel.initData(order, ruleItem, ruleItems)
    }
    // set the title from enter rule item
    setTitleSubText(ruleItem?.title?.localize())

    showIssueComponentView(order, ruleItem)
  }

  private fun showIssueComponentView(order: Order?, ruleItem: SupportRuleItem?) {
    val order = order ?: return
    val ruleItem = ruleItem ?: return

    val components = ruleItem.components
    val normalBinding = binding.inSupportNormal
    components?.forEach { component ->
      when (component.type) {
        SupportComponentType.TEXT -> {
          val desc = component.content?.localize()
          normalBinding.tvSupportDesc.text = desc?.trim()
          normalBinding.tvSupportDesc.isVisible = !desc.isNullOrEmpty()
        }
        SupportComponentType.TEXTAREA -> {
          val inputTitle = component.content?.localize()
          normalBinding.tvSupportInputLargeTitle.text = inputTitle
          normalBinding.tvSupportInputLargeTitle.isVisible = !inputTitle.isNullOrEmpty()
          normalBinding.etSupportInput.isVisible = true
          normalBinding.etSupportInput.doAfterTextChanged { supportIssueViewModel.changeSubject(it.toString()) }
        }
        SupportComponentType.BUTTON -> {
          normalBinding.btnSubmit.isVisible = true
          normalBinding.btnSubmit.text = component.label?.localize()
          normalBinding.btnSubmit.clickWithTrigger {
            val itemType = ruleItem.type
            if (itemType == changeItemType) {
              lifecycleScope.launch {
                supportIssueViewModel.linkToCancel()?.collectLatest {
                  showIssueComponentView(order, it)
                }
              }
            } else if (itemType == missingOrderType ||
              itemType == wrongOrderType ||
              itemType == tastingBadType ||
              itemType == packagingIssueType ||
              itemType == driverProblemType
            ) {
              supportIssueViewModel.submitChooseSubject(this, component)
            } else if (ruleItem.type == cancelOrderType) {
              supportIssueViewModel.cancelOrder(this, component)
            } else {
              supportIssueViewModel.submitSubject(this, component)
            }
          }
        }
        SupportComponentType.CALL_RESTAURANT_BUTTON -> {
          normalBinding.btnCallRestaurant.isVisible = true
          val text = order.restaurant?.name?.localize()
          normalBinding.btnCallRestaurant.text = "${ResourcesUtil.getString(com.ricepo.style.R.string.call)} $text"
          normalBinding.btnCallRestaurant.clickWithTrigger { supportIssueViewModel.callRestaurant(this, component) }
        }
        SupportComponentType.CALL_DRIVER_BUTTON -> {
          normalBinding.btnCallDriver.isVisible = true
          normalBinding.btnCallDriver.text = ResourcesUtil.getString(com.ricepo.style.R.string.call_driver)
          normalBinding.btnCallDriver.clickWithTrigger { supportIssueViewModel.callDriver(this, component) }
        }
        SupportComponentType.SELECT_ITEMS -> {
          renderSupportFood(order, component)
        }
        SupportComponentType.SELECT_DRIVER_PROBLEM -> {
          renderSupportProblem(order, component)
        }
        SupportComponentType.SELECT_SOLUTION -> {
          renderSupportMethod(order, component)
        }
        SupportComponentType.UPLOAD_IMAGES -> {
          renderSupportUploadPic(component)
        }
      }
    }

    // with textarea keyboard
    val inputs = components?.filter { it.type == SupportComponentType.TEXTAREA }
    val chooseItems = components?.filter {
      it.type == SupportComponentType.SELECT_ITEMS ||
        it.type == SupportComponentType.SELECT_DRIVER_PROBLEM ||
        it.type == SupportComponentType.SELECT_SOLUTION
    }
    if (!inputs.isNullOrEmpty() && chooseItems.isNullOrEmpty()) {
      KeyboardUtil.showKeyboard(this, normalBinding.etSupportInput)
    }

    // feedback type chat service
    if (ruleItem.type == feedbackType) {
      normalBinding.btnCallSupport.isVisible = true
      normalBinding.btnCallSupport.text = ResourcesUtil.getString(com.ricepo.style.R.string.call_cs)
      normalBinding.btnCallSupport.clickWithTrigger {
        supportIssueViewModel.callSupport(
          this,
          components?.firstOrNull { it.type == SupportComponentType.BUTTON }
        )
      }
    }
  }

  private fun renderSupportFood(order: Order, component: SupportItemComponent) {
    binding.llSupportFoodGroup.isVisible = true
    binding.tvSupportFood.text = component.content?.localize()
    supportIssueViewModel.getItemSection(order)?.forEachIndexed { index, item ->
      addChooseView(binding.llSupportFood, index, item)
    }
  }

  private fun renderSupportProblem(order: Order, component: SupportItemComponent) {
    binding.llSupportProblemGroup.isVisible = true
    binding.tvSupportProblem.text = component.content?.localize()
    supportIssueViewModel.getProblemSection()?.forEachIndexed { index, item ->
      addChooseView(binding.llSupportProblem, index, item)
    }
  }

  private fun renderSupportMethod(order: Order, component: SupportItemComponent) {
    binding.llSupportMethodGroup.isVisible = true
    binding.tvSupportMethod.text = component.content?.localize()
    supportIssueViewModel.getProcessingMethodSection()?.forEachIndexed { index, item ->
      addChooseView(binding.llSupportMethod, index, item)
    }
  }

  private fun renderSupportUploadPic(component: SupportItemComponent) {
    binding.llSupportUppic.isVisible = true
    binding.tvSupportUppic.text = com.ricepo.style.R.string.support_packaging_issue_upload_placeholder.localized()
    binding.tvSupportUppicSubtitle.text = component.content?.localize()
    val picBinding = LayoutOrderSupportIssueUppicBinding.inflate(layoutInflater)

    picBinding.rvSupportPic.apply {
      adapter = imageAdpater
    }
    if (picBinding.rvSupportPic.layoutManager is GridLayoutManager) {
      (picBinding.rvSupportPic.layoutManager as GridLayoutManager)
        .isSmoothScrollbarEnabled = true
    }
    picBinding.rvSupportPic.setHasFixedSize(true)
    picBinding.rvSupportPic.isNestedScrollingEnabled = false

    picBinding.btnSupportPic.clickWithTrigger {
      showPicActionSheet()
    }

    binding.llSupportUppic.addView(picBinding.root)
  }

  private fun addChooseView(layout: ViewGroup, index: Int, normal: ChoosedItem) {
    val normalBinding = LayoutOrderSupportIssueChooseBinding.inflate(layoutInflater)
    normalBinding.tvSupportDivider.isVisible = (index != 0)

    when (normal) {
      is ChoosedItem.Food -> {
        normalBinding.tvSupportItem.text = normal.item.name?.localize() ?: ""
      }
      is ChoosedItem.Method -> {
        normalBinding.tvSupportItem.text = normal.methodString
      }
      is ChoosedItem.Problem -> {
        normalBinding.tvSupportItem.text = normal.problem
      }
    }

    normalBinding.root.clickWithTrigger {
      if (normal is ChoosedItem.Method) {
        if (!normal.choosed) {
          supportIssueViewModel.resetChooseItemMethod()
          for (i in 0 until binding.llSupportMethod.size) {
            binding.llSupportMethod[i].findViewById<ImageView>(
              R.id.iv_support_item
            )?.isVisible = false
          }
          normal.choosed = true
        }
      } else {
        normal.choosed = !normal.choosed
      }
      normalBinding.ivSupportItem.isVisible = normal.choosed
      supportIssueViewModel.changeChooseItem()
    }

    layout.addView(normalBinding.root)
  }

  private fun showPicActionSheet() {
    if (supportIssueViewModel.imageList.count() == 4) {
      DialogFacade.showAlert(this, com.ricepo.style.R.string.maxinum_select_pics)
      return
    }
    val datas = listOf<String>(
      ResourcesUtil.getString(com.ricepo.style.R.string.camera),
      ResourcesUtil.getString(com.ricepo.style.R.string.choose_from_album)
    )
    val bottomSheet = BaseBottomSheetFragment.newInstance<String>(datas)
    bottomSheet.onItemTextClickListener = object : BaseBottomSheetFragment.OnItemTextClickListener {
      override fun onItemClick(text: String) {
        if (ResourcesUtil.getString(com.ricepo.style.R.string.camera) == text) {
          IntentUtils.intentCamera(this@OrderSupportIssueActivity, 100) { path, uri ->
            imagePath = path
            imageUri = uri
          }
        }
        if (ResourcesUtil.getString(com.ricepo.style.R.string.choose_from_album) == text) {
          IntentUtils.intentAlbum(this@OrderSupportIssueActivity, 101)
        }
      }
    }
    bottomSheet.show(supportFragmentManager, "select_pic")
  }

  private var imagePath: String? = null

  private var imageUri: Uri? = null

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == 100) {
        // camera
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
          supportIssueViewModel.addImagePath(this, imageUri = imageUri)
        } else {
          supportIssueViewModel.addImagePath(this, imagePath = imagePath)
        }
      }
      if (requestCode == 101) {
        // album
        val selectedImage = data?.data
        supportIssueViewModel.addImagePath(this, imageUri = selectedImage)
      }
      imageAdpater.notifyDataSetChanged()
    }
  }
}
