package com.ricepo.app.features.recommend.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.RecommendItemTitleBinding
import com.ricepo.app.features.recommend.RecommendUiModel

class RecommendTitleHolder(private val binding: RecommendItemTitleBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: RecommendUiModel.TitleItem) {
    binding.tvRecommendTitle.text = uiModel.title
    binding.tvRecommendDate.text = uiModel.dateLabel
  }

  companion object {
    fun create(parent: ViewGroup): RecommendTitleHolder {
      val binding = RecommendItemTitleBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return RecommendTitleHolder(binding)
    }
  }
}
