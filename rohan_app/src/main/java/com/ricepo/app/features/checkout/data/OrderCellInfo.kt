package com.ricepo.app.features.checkout.data

import android.graphics.drawable.Drawable
import android.text.SpannableStringBuilder
import androidx.annotation.ColorInt

//
// Created by <PERSON><PERSON> on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class OrderCellInfo(
  // left text
  var leftText: String,
  // right text
  var rightText: String,
  // right spannable optional
  var rightSpan: SpannableStringBuilder? = null,
  // text color id
  @ColorInt
  var color: Int? = null,
  // text size: large/nil
  val large: Boolean? = null,
  // right navigate icon image
  var rightIcon: Drawable? = null,
  var rightIconSize: Int = 0,
  var rightIconEndMargin: Int =0,
  // cell click callback function
  var click: (() -> Unit)? = null,
  // right icon click callback function
  var rightIconClick: (() -> Unit)? = null,
  // left navigate icon image
  var leftIcon: Drawable? = null,
  // for tax info tips
  var leftTips: String? = null,
  // item alpha
  var alpha: Float = 1f,
  // left detail
  var leftDetail: String? = null,
  // pool expired at time
  var expiredAt: String? = null,
  // coupon
  var coupon: String? = null,
  // delivery fee
  var deliveryFee: Int? = null,
  var enable: Boolean = true,
)
