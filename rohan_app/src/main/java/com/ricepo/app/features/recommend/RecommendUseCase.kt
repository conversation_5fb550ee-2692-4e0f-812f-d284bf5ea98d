package com.ricepo.app.features.recommend

import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Food
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Calendar
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RecommendUseCase @Inject constructor(
  private val repository: CombineRestApi
) : BaseUseCase() {

  suspend fun getRecommendList(type: String): List<Food> {
    var foods = mutableListOf<Food>()
    if (type == RecommendType.TYPE_RECOMMEND) {
      val cacheCustomer = CustomerCache.getCustomerSuspend()
      val customer = if (cacheCustomer != null) {
        repository.getCustomer(cacheCustomer.id)
      } else {
        null
      }
      val recentImageFood = filterFoodWithImage(customer?.recommendation?.recent)
      val crossImageFood = filterFoodWithImage(customer?.recommendation?.cross)
      val popularImageFood = filterFoodWithImage(customer?.recommendation?.popular)
      val featuredImageFood = filterFoodWithImage(customer?.recommendation?.featured)

      foods.addAll(recentImageFood)
      foods.addAll(crossImageFood)
      foods.addAll(popularImageFood)
      foods.addAll(featuredImageFood)
    } else if (type == RecommendType.TYPE_RANK) {
      val address = withContext(Dispatchers.IO) {
        AddressCache.getAddressSuspend()
      }
      val loc = address?.location?.coordinates?.joinToString(",")

      val ranks = repository.getRanks(loc)
      foods.addAll(ranks)
    }
    return foods
  }

  /**
   * filter out foods with image
   */
  private fun filterFoodWithImage(foods: List<Food>?): List<Food> {
    val foods = foods ?: return listOf()
    return foods.filter { it.image != null }
  }

  /**
   * for rank: get last 7 days until yesterday
   * for recommend: get last Monday to Sunday
   */
  fun getRecommendDateInfo(type: String): String {
    val dateFormat = SimpleDateFormat(ResourcesUtil.getString(com.ricepo.style.R.string.banner_time))
    return if (type == RecommendType.TYPE_RANK) {
      val sevenDays = Calendar.getInstance()
      sevenDays.add(Calendar.DAY_OF_WEEK, -7)
      val oneDays = Calendar.getInstance()
      oneDays.add(Calendar.DAY_OF_WEEK, -1)
      val sevenDaysAgo = dateFormat.format(sevenDays.time)
      val oneDaysAgo = dateFormat.format(oneDays.time)

      "$sevenDaysAgo - $oneDaysAgo"
    } else {
      val lastWeekIndex = -1
      val currentWeekIndex = 0
      val lastWeekMonday = Calendar.getInstance()
      lastWeekMonday.add(Calendar.DATE, lastWeekIndex * 7)
      lastWeekMonday.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
      val lastWeekSunday = Calendar.getInstance()
      lastWeekSunday.add(Calendar.DATE, currentWeekIndex * 7)
      lastWeekSunday.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
      val lastMonday = dateFormat.format(lastWeekMonday.time)
      val lastSunday = dateFormat.format(lastWeekSunday.time)
      "$lastMonday - $lastSunday"
    }
  }
}

object RecommendType {
  const val TYPE_RECOMMEND = "recommend"
  const val TYPE_RANK = "rank"
}

sealed class RecommendUiModel {

  /**
   * the banner title info
   * for rank: get last 7 days until yesterday
   * for recommend: get last Monday to Sunday
   */
  class TitleItem(
    val title: String,
    val dateLabel: String
  ) : RecommendUiModel()

  /**
   * the recommend & rank food
   */
  class FoodItem(val food: Food, val position: Int) : RecommendUiModel()
}
