package com.ricepo.app.features.recommend.adapter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RecommendItemFoodBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.app.features.recommend.RecommendUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper

class RecommendFoodHolder(private val binding: RecommendItemFoodBinding) :
  RecyclerView.ViewHolder(binding.root) {

  private val baseMapper = BaseMapper()

  init {
    binding.ivRecommendDish.setOnLongClickListener { view ->
      val food = view.tag as Food
//            FeaturePageRouter.navigateMenuFeedback(food, view.context)
      transitionToNormal(view.context, food, null)
      false
    }

    binding.ivRecommendDish.clickWithTrigger { view ->
      navigateMenu(view)
    }

    binding.root.clickWithTrigger { view ->
      navigateMenu(view)
    }
  }

  private fun transitionToNormal(
    context: Context,
    food: Food,
    restaurant: Restaurant?
  ) {
    food.image?.url ?: return
    if (context is Activity) else return
    val intent = Intent(context, MenuNormalPreviewActivity::class.java)

    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RECOMMEND, true)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        binding.ivRecommendDish,
        MenuNormalPreviewActivity.VIEW_MENU_IMAGE
      ),
      Pair(
        binding.ivRecommendDishBg,
        MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }

  private fun navigateMenu(view: View) {
    val food = view.tag as Food
    val restaurantId = food.restaurant?.id ?: return
    val restaurant = Restaurant(id = restaurantId)
    val searches = arrayListOf(food.name.localize())
    FeaturePageRouter.navigateMenu(restaurant, searches)
  }

  fun bind(uiModel: RecommendUiModel.FoodItem) {
    binding.tvRecommendFood.text = uiModel.food.name.localize()
    binding.tvRecommendRestaurant.text = uiModel.food.restaurant?.name?.localize()
    binding.tvRecommendPrice.text = baseMapper.formatPrice(uiModel.food.price, "")

    binding.root.tag = uiModel.food
    binding.ivRecommendDish.tag = uiModel.food
    bindImage(uiModel.food.image)

    binding.tvRecommendDivider.isVisible = !(uiModel.position == 0)
  }

  private fun bindImage(image: FoodImage?) {
    val imageUrl = image?.url

    ImageLoader.load(binding.ivRecommendDish, imageUrl, com.ricepo.style.R.drawable.dish_placeholder)
    if ((image == null || image?.noPlate == true)) {
      binding.ivRecommendDishBg.visibility = View.INVISIBLE
    } else {
      binding.ivRecommendDishBg.setImageResource(com.ricepo.style.R.drawable.leaderboard_dish)
      binding.ivRecommendDishBg.visibility = View.VISIBLE
    }
  }

  companion object {
    fun create(parent: ViewGroup): RecommendFoodHolder {
      val binding = RecommendItemFoodBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      val params = RecyclerView.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return RecommendFoodHolder(binding)
    }
  }
}
