package com.ricepo.app.features.luckymenu

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityLuckyRecommendBinding
import com.ricepo.app.databinding.LuckyShadowItemBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.luckymenu.data.LuckyModel
import com.ricepo.app.features.luckymenu.view.LuckyFoodView
import com.ricepo.app.features.luckymenu.view.RandomLayout
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.base.BaseActivity
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.model.Food
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseLuckyEvent
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.shadow.LuckyShadowLayout
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by Mark on 11/13/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_LUCKY_RECOMMEND)
class LuckyRecommendActivity : BaseActivity(), RandomLayout.OnRandomItemClickListener {

  companion object {
    const val TAG = "LuckyRecommendActivity"
    const val NUM = "1"
    const val REFRESH = "lucky_refresh"
  }

  lateinit var binding: ActivityLuckyRecommendBinding
  var isFreshExit: Boolean = false
  private var num: String? = NUM

  val viewModel: LuckyRecommendViewModel by viewModels()

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityLuckyRecommendBinding.inflate(layoutInflater)
    binding.lifecycleOwner = this
    binding.bean = LuckyModel()
    binding.viewmodel = viewModel
    setContentView(binding.root)
    clickListener()
    bindViewModelObserve()
    init()
    bindData()
  }

  private fun init() {
    viewModel.luckyModelCommand.value = LuckyModel()
    viewModel.refresh.value = true
  }

  private fun bindData() {
    num = intent.getStringExtra(FeaturePageConst.PAGE_LUCKY_GROUP_SIZE)
    refreshMenu()
  }

  private fun refreshMenu() {
    viewModel.requestRecommendMenu(num)
  }

  private fun bindViewModelObserve() {
    // show dialog when no recommended menu data
    viewModel.catch.observe(this) { errorMessage ->
      DialogFacade.showAlert(this@LuckyRecommendActivity, errorMessage) {
//                Log.d(LuckyRecommendViewModel.TAG, "allFoodList::${viewModel.allFoodList.size}")
        binding.luckyMenu.itemClickable = true
        if (viewModel.allFoodList.isEmpty()) {
          val paoRefresh = LuckyFoodView(this)
          val size = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_77dp)
          val params = FrameLayout.LayoutParams(size, size)
          paoRefresh.layoutParams = params
          paoRefresh.setRefreshView(com.ricepo.style.R.drawable.lucky_refresh)

          val paoRefreshLayout = LuckyShadowItemBinding.inflate(layoutInflater)
          paoRefreshLayout.root.setShadowColor(com.ricepo.style.R.color.luckyBottomShadow)
          paoRefreshLayout.root.addView(paoRefresh)

//                    val maxSize = paoRefresh.height
//                    val dx = 0f
//                    val dy = DisplayUtil.dp2Px(3f + maxSize.div(2))
//                    val radius = DisplayUtil.dp2Px(3f + maxSize.div(2))
//                    val color = ResourcesUtil.getColorWithAlpha((0.15 + 0.05 * maxSize / 60).toFloat(), com.ricepo.style.R.color.red1)
//                    paoRefresh.setShadowLayer(radius, dx, dy, color)

          paoRefresh.mid = REFRESH
          binding.luckyMenu.initXY()
          binding.luckyMenu.addViewAtRandomXY2(paoRefreshLayout.root, Food())
        }
      }
    }
    viewModel.clearCartTips.observe(this) { it ->
      DialogFacade.showPrompt(
        this@LuckyRecommendActivity, it
      ) {
        // clear the other cache cart
        lifecycleScope.launch {
          withContext(Dispatchers.IO) {
            viewModel.deleteRestaurantCart(viewModel.response?.restaurant)
            viewModel.saveRestaurantCart(viewModel.foodConvertCart(), null)
          }
          viewModel.response?.let {
            viewModel.forward.value = it.restaurant
          }
        }
      }
    }
    viewModel.forward.observe(this) {
      FeaturePageRouter.navigateCheckoutAfterSubscription(this@LuckyRecommendActivity, it)
    }

    // request successfully and create paopao
    viewModel.createPao.observe(this) { it ->
      binding.luckyMenu.itemClickable = true
      binding.luckyMenu.initXY()

      it.forEachIndexed { index, food ->
        val txv = LuckyFoodView(this)
//                txv.setBackgroundResource(com.ricepo.style.R.drawable.lucky_paopao)
//                txv.gravity = Gravity.CENTER
//                txv.text = food.name.localize()
//                txv.setTextColor(getColor(com.ricepo.style.R.color.luckyMenuTextColor))
//                txv.textSize = 14F
//                txv.setPadding(40, 0, 40, 0)
        txv.mFood = food
        val mod = index % 3
        txv.setFoodView(viewModel.response?.restaurant, food, mod + 1)

        val paoMenuLayout = LuckyShadowItemBinding.inflate(layoutInflater)
        paoMenuLayout.root.addView(txv)

        txv.clickMenu = {
          clickMenu(it, txv, paoMenuLayout.root)
        }

        foodViews.add(paoMenuLayout.root)
      }

      val size = foodViews.size

      // first add food layout
      if (size > 2) {
        foodViews.take(3).forEach { layout ->
          val luckyFoodView = layout.getChildAt(0) as LuckyFoodView
          luckyFoodView.mFood?.let {
            binding.luckyMenu.addViewAtRandomXY2(layout, it)
          }
        }
      } else {
        foodViews.forEach { layout ->
          val luckyFoodView = layout.getChildAt(0) as LuckyFoodView
          luckyFoodView.mFood?.let {
            binding.luckyMenu.addViewAtRandomXY2(layout, it)
          }
        }
      }

      // add refresh layout
      if (!isFreshExit) {
        val paoRefresh = LuckyFoodView(this)
        val size = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_77dp)
        val params = FrameLayout.LayoutParams(size, size)
        paoRefresh.layoutParams = params
        paoRefresh.setRefreshView(com.ricepo.style.R.drawable.lucky_refresh)
        paoRefresh.mid = REFRESH

        val paoRefreshLayout = LuckyShadowItemBinding.inflate(layoutInflater)
        paoRefreshLayout.root.setShadowColor(com.ricepo.style.R.color.luckyBottomShadow)
        paoRefreshLayout.root.addView(paoRefresh)

        binding.luckyMenu.addViewAtRandomXY2(paoRefreshLayout.root, Food())
      }

      if (size > 3) {
        // second add food layout
        foodViews.subList(3, foodViews.size).forEach { layout ->
          val luckyFoodView = layout.getChildAt(0) as LuckyFoodView
          luckyFoodView.mFood?.let {
            binding.luckyMenu.addViewAtRandomXY2(layout, it)
          }
        }
      }

      foodViews.clear()

//            Log.d(TAG, "randomViewList_size::${binding.luckyMenu.randomViewList.size}")
    }
  }

  var foodViews: MutableList<LuckyShadowLayout> = mutableListOf()

  private fun clickListener() {
    binding.rlShoppingCar.setOnClickListener {
      if (viewModel.selectedFoodView.isNotEmpty()) {
        viewModel.addShoppingCart()
        logLuckyEvent(FirebaseEventName.rLuckyCheckout)
      }
    }
    binding.luckyMenu.setOnRandomItemClickListener(this)
  }

  override fun onPause() {
    super.onPause()
    LuckyBindAdapter.releaseAnimation()
  }

  private fun getFoodView(view: View?): LuckyFoodView? {
    return when (view) {
      is LuckyFoodView -> {
        view
      }
      is LuckyShadowLayout -> {
        val cv = view.getChildAt(0)
        if (cv is LuckyFoodView) {
          cv
        } else null
      }
      else -> null
    }
  }

  private var mLuckyFoodLayout: LuckyShadowLayout? = null
  private var mLuckyFoodView: LuckyFoodView? = null

  override fun onRandomItemClick(view: View?, food: Food) {
    val luckyFoodView = getFoodView(view) ?: return
    val luckyFoodLayout = if (view is LuckyShadowLayout) {
      view
    } else {
      null
    }

    if (REFRESH == luckyFoodView.mid) {
      isFreshExit = true
      if (viewModel.selectedFoodView.isEmpty()) {
        binding.luckyMenu.removeRandomViewFromList(luckyFoodView)
      }
      if (viewModel.selectedFoodView.isNotEmpty()) {
        val toMutableList = binding.luckyMenu.randomViewList.toMutableList()
        toMutableList.forEach {
          val foodView = getFoodView(it) ?: return
//                    Log.d(
//                        TAG,
//                        "isSelected::${foodView.isSelected},$${foodView.food?.name?.localize()}"
//                    )
          if (!foodView.isSelected && foodView.mid != REFRESH) {
            binding.luckyMenu.removeRandomViewFromList(it)
            binding.luckyMenu.removeView(it)
          }
        }
        logLuckyEvent(FirebaseEventName.rLuckyRefresh, true)
      } else {
        isFreshExit = false
        viewModel.clearFoodList()
        binding.luckyMenu.clearViewList()
        binding.luckyMenu.removeAllViews()
        logLuckyEvent(FirebaseEventName.rLuckyRefresh, false)
      }
      // prevents quick click refresh
      binding.luckyMenu.itemClickable = false
      refreshMenu()
    } else {
      clickMenu(food, luckyFoodView, luckyFoodLayout)
    }
  }

  private fun clickMenu(
    food: Food?,
    luckyFoodView: LuckyFoodView?,
    luckyFoodLayout: LuckyShadowLayout?
  ) {
    val food = food ?: return
    val luckyFoodView = luckyFoodView ?: return
    if (!luckyFoodView.isSelected) {
      if (food.options?.isNotEmpty() == true) {
        // add options food
        viewModel.response?.restaurant?.let { restaurant ->
          mLuckyFoodLayout = luckyFoodLayout
          mLuckyFoodView = luckyFoodView
          FeaturePageRouter.navigateOptions(
            this, food,
            restaurant, null, null
          )
        }
        // update price after back options
        return
      } else {
        // add normal food
        addFoodSelectedView(luckyFoodLayout, luckyFoodView, food)
      }
    } else {
      luckyFoodView.isSelected = false
      luckyFoodView.setSelectedView(false)
      viewModel.selectedFoodView.remove(luckyFoodView)
      viewModel.selectedFoodIds.remove(food.id)

      logLuckyEvent(FirebaseEventName.rLuckyRemove)
    }
    viewModel.updatePriceAndDelivery()
    viewModel.emptySelectedFood.value = viewModel.selectedFoodView.size > 0
  }

  private fun addFoodSelectedView(
    luckyFoodLayout: LuckyShadowLayout?,
    luckyFoodView: LuckyFoodView?,
    food: Food
  ) {
//        luckyFoodLayout?.isShowShadow = false
//        luckyFoodLayout?.invalidateShadow()
    val luckyFoodView = luckyFoodView ?: return
    luckyFoodView.isSelected = true
//        luckyFoodView.background = ContextCompat.getDrawable(
//            this@LuckyRecommendActivity,
//            com.ricepo.style.R.drawable.lucky_paopao_selected
//        )
//        luckyFoodView.setPadding(40, 0, 40, 0)
//        luckyFoodView.setTextColor(getColor(com.ricepo.style.R.color.white))
    luckyFoodView.setSelectedView(true)

    viewModel.selectedFoodView.add(luckyFoodView)
    viewModel.selectedFoodIds.add(food.id)

    logLuckyEvent(FirebaseEventName.rLuckyAdd)
    // log add food event
    val foodIds = listOf(food.id)
    LifecycleNetworkListener.logAddFood(viewModel.response?.restaurant?.id, foodIds)

    // reset the options cache view
    mLuckyFoodLayout = null
    mLuckyFoodView = null
  }

  fun logLuckyEvent(eventName: String, refreshWithSelected: Boolean? = null) {
    AnalyticsFacade.logEvent(FirebaseLuckyEvent(refreshWithSelected), eventName)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_OPTIONS) {
        val food = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
        food?.let {
          mLuckyFoodView?.mFood = food
          addFoodSelectedView(mLuckyFoodLayout, mLuckyFoodView, it)
          // update price and view
          viewModel.updatePriceAndDelivery()
          viewModel.emptySelectedFood.value = viewModel.selectedFoodView.size > 0
        }
      }
    }
  }
}
