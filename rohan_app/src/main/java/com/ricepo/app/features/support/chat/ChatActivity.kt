package com.ricepo.app.features.support.chat

import android.graphics.Rect
import android.os.Bundle
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.activity.viewModels
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivitySupportChatBinding
import com.ricepo.app.features.BaseWebActivity
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.support.OrderSupportViewModel
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 13/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_ORDER_SUPPORT_CHAT)
class ChatActivity : BaseWebActivity() {

  companion object {
    val ACTIVITY_NAME = "com.ricepo.app.features.support.chat.ChatActivity"
  }

  lateinit var binding: ActivitySupportChatBinding

  val viewModel: OrderSupportViewModel by viewModels()

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivitySupportChatBinding.inflate(layoutInflater)
    setContentView(binding.root)

    // set title and color
    setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.ricepo_service_chat))

    setupWebView(binding.wvChat)
    showChatView()

    setKeyboardChange()
  }

  override fun onDestroy() {
    super.onDestroy()
    window.decorView.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
  }

  var windowHeight = 0

  private val globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
    val r = Rect()
    window.decorView.getWindowVisibleDisplayFrame(r)
    val height = r.height()
    if (windowHeight == 0) {
      windowHeight = height
    } else {
      val softKeyboardHeight = windowHeight - height
      val params = binding.wvChat.layoutParams as FrameLayout.LayoutParams
      params.setMargins(0, 0, 0, softKeyboardHeight)
      binding.wvChat.layoutParams = params
    }
  }

  private fun setKeyboardChange() {
    window.decorView.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
  }

  private fun showChatView() {
    viewModel.chatUrl.observe(this) {
      binding.wvChat.loadUrl(it)
    }
  }
}
