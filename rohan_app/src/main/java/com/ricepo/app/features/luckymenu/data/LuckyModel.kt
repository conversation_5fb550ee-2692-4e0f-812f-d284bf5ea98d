package com.ricepo.app.features.luckymenu.data

import android.text.SpannableStringBuilder
import com.ricepo.base.model.InternationalizationContent

data class LuckyModel(
  var isRefresh: Boolean = true,
  var price: String = "$0.00",
  var restaurantName: String? = "",
  var restaurantTags: SpannableStringBuilder? = null,
  var deliveryText: String? = "",
  var clickRefresh: Boolean = false,
  var hasSelected: Boolean = false,
  var restaurantNameContent: InternationalizationContent? = null,
)
