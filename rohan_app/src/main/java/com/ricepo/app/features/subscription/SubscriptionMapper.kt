package com.ricepo.app.features.subscription

import com.ricepo.app.R
import com.ricepo.app.features.checkout.CheckoutMapper
import com.ricepo.base.model.CustomerSubscription
import com.ricepo.style.ResourcesUtil
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 8/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class SubscriptionMapper @Inject constructor() : CheckoutMapper() {

  fun mapUpdateSubtitle(customerSubscription: CustomerSubscription): String? {

    val formatter = "MM/dd/yyyy"
    val cancelAt = formatTime(customerSubscription.cancelAt, formatter)
    if (cancelAt != null) {
      return ResourcesUtil.getString(com.ricepo.style.R.string.subscription_valid_until, cancelAt)
    }
    val currentPeriodEndAt = formatTime(customerSubscription.currentPeriodEndAt, formatter)
    if (currentPeriodEndAt != null) {
      return ResourcesUtil.getString(com.ricepo.style.R.string.subscription_current_period, currentPeriodEndAt)
    }
    return ""
  }
}
