package com.ricepo.app.features.checkout.points

import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityCheckoutPointsBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithClickTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 2/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_CHECKOUT_POINTS)
class PointsActivity : BaseActivity() {

  val viewModel: PointsInputViewModel by viewModels()

  private lateinit var binding: ActivityCheckoutPointsBinding

  private var restaurant: Restaurant? = null

  private var customer: Customer? = null

  private var lastPointValue: Int = 0
  private var pointValue: Int = 0

  private lateinit var mapper: BaseMapper

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityCheckoutPointsBinding.inflate(layoutInflater)
    setContentView(binding.root)

    mapper = BaseMapper()
    restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_CHECKOUT_RESTAURANT)
    lastPointValue = intent.getIntExtra(FeaturePageConst.PARAM_POINTS_VALUE, 0)

    setupListener()
    observeLoginLoad()
  }

  private fun observeLoginLoad() {
    viewModel.loginState.observe(
      this
    ) {
      customer = it.savedCustomer
      val isLogined = (customer != null)
      binding.btnLogin.isVisible = !isLogined
      binding.layPoints.isVisible = isLogined
      if (customer != null) {
        lifecycleScope.launch {
          // refresh customer
          val newCust = withContext(Dispatchers.IO) {
            LifecycleNetworkListener.updateCustomer(customer)
          }
          if (newCust != null) {
            customer = newCust
            setCurrentPoints(customer)
          }
        }
        setPointsView(customer)
        KeyboardUtil.showKeyboard(this, binding.etPoints)
      }
    }
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkLoginChange()
  }

  private fun setPointsView(customer: Customer?) {

    setCurrentPoints(customer)
    if (lastPointValue > 0) {
      val text = "$lastPointValue"
      binding.etPoints.setText(text)
      binding.etPoints.setSelection(text.length)
    } else {
      setPointValueView()
    }
  }

  private fun setCurrentPoints(customer: Customer?) {
    val currentPoint = customer?.point?.balance?.available ?: 0
    binding.tvCurrentPoint.text = ResourcesUtil.getString(
      com.ricepo.style.R.string.redeem_current_point, currentPoint
    )
  }

  private fun setupListener() {

    binding.btnSaveComment.clickWithTrigger {
      if (binding.btnSaveComment.alpha == 1f) {
        val resultIntent = intent.putExtra(
          FeaturePageConst.PARAM_POINTS_VALUE,
          pointValue
        )
        backResultEvent(resultIntent)
      }
    }

    binding.etPoints.doAfterTextChanged {
      try {
        pointValue = it.toString().toInt()
      } catch (e: Exception) {
        e.printStackTrace()
        pointValue = 0
      } finally {
        setPointValueView()
      }
    }

    binding.btnLogin.clickWithTrigger {
      FeaturePageRouter.navigateLogin()
    }

    binding.tvPoints.touchWithClickTrigger { _, event ->
      KeyboardUtil.showKeyboard(this, binding.etPoints)
    }
  }

  private fun setPointValueView() {
    val total = customer?.point?.balance?.available ?: 0
    if (pointValue > total) {
      // point over
      binding.tvPointValue.text = ResourcesUtil.getString(com.ricepo.style.R.string.redeem_points_over)
      binding.tvPointValue.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.alert, this))
      binding.btnSaveComment.alpha = 0.4f
    } else {
      binding.tvPointValue.text = ResourcesUtil.getString(
        com.ricepo.style.R.string.redeem_equal_to,
        mapper.formatPriceByRestaurant(pointValue, restaurant)
      )
      binding.tvPointValue.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, this))
      binding.btnSaveComment.alpha = 1f
    }
  }
}
