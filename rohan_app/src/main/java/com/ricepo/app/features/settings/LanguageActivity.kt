package com.ricepo.app.features.settings

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.core.view.isVisible
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityLanguageBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.base.BaseSupperActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.tools.IntentUtils
import com.ricepo.style.LocaleConst
import com.ricepo.style.LocaleUtil

//
// Created by <PERSON><PERSON> on 4/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_LANGUAGE)
class LanguageActivity : BaseSupperActivity() {

  lateinit var binding: ActivityLanguageBinding

  private var isSplash: Boolean = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityLanguageBinding.inflate(layoutInflater)
    setContentView(binding.root)

    splashAnimation(binding)

    setupListener()
  }

  override fun onBackPressed() {
    IntentUtils.backHome(this)
  }

  private fun setupListener() {
    // language section
    with(binding) {
      tvLanguageHans.clickWithTrigger {
        changeLanguage(LocaleConst.zhCN)
      }
      tvLanguageHk.clickWithTrigger {
        changeLanguage(LocaleConst.zhHK)
      }
      tvLanguageEn.clickWithTrigger {
        changeLanguage(LocaleConst.enUS)
      }
      tvLanguageEs.clickWithTrigger {
        changeLanguage(LocaleConst.ES)
      }
    }
  }

  private fun changeLanguage(lang: String) {
    LocaleUtil.saveLanguage(this, lang)
    FeaturePageRouter.navigateHomeWithReset(this)
  }

  override fun onWindowFocusChanged(hasFocus: Boolean) {
    super.onWindowFocusChanged(hasFocus)
    if (hasFocus && isSplash) {
      hideSystemUIAndNavigation(this)
    }
  }

  private fun hideSystemUIAndNavigation(activity: Activity) {
    val decorView: View = activity.window.decorView
    decorView.systemUiVisibility =
      (
        View.SYSTEM_UI_FLAG_IMMERSIVE
          // Set the content to appear under the system bars so that the
          // content doesn't resize when the system bars hide and show.
          or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
          or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN // Hide the nav bar and status bar
          or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
  }

  private fun splashAnimation(binding: ActivityLanguageBinding) {
    isSplash = intent.getBooleanExtra(FeaturePageConst.PARAM_LAUNCH_ENTRANCE, false)
    binding.flMotionLaunch.isVisible = isSplash
    if (isSplash) {
      val originVisibility = this.window.decorView.systemUiVisibility
      hideSystemUIAndNavigation(this)
      binding.motionLaunch.setTransitionListener(object : MotionLayout.TransitionListener {
        override fun onTransitionTrigger(
          motionLayout: MotionLayout?,
          triggerId: Int,
          positive: Boolean,
          progress: Float
        ) {}

        override fun onTransitionStarted(
          motionLayout: MotionLayout?,
          startedId: Int,
          endId: Int
        ) {}

        override fun onTransitionChange(
          motionLayout: MotionLayout?,
          startId: Int,
          endId: Int,
          progress: Float
        ) {}

        override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
          if (currentId == R.id.launch_end) {
            binding.flMotionLaunch.isVisible = false
            isSplash = false
          }
          <EMAIL> = originVisibility
          fitStatusBar()
        }
      })
      binding.motionLaunch.startLayoutAnimation()
    }
  }
}
