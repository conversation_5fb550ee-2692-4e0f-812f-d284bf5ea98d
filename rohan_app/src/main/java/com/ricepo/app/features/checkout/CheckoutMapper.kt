package com.ricepo.app.features.checkout

import android.content.Context
import android.graphics.drawable.Drawable
import com.ricepo.app.R
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.utils.log
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class CheckoutMapper @Inject constructor() : MenuMapper() {

  fun mapPaymentIcon(context: Context?, payment: PaymentOwnMethod?): Drawable {
    var drawableId = com.ricepo.style.R.drawable.ic_payment_none
    val payment = payment ?: return ResourcesUtil.getDrawable(drawableId, context)
    payment.brand?.log("brand:  ")
    payment.method.log("method:  ")
    if ((
      payment.method == PaymentOwnMethod.CREDIT ||
        payment.method == PaymentOwnMethod.CREDIT_PREVIOUSLY
      ) &&
      payment.brand != null
    ) {
      // select a appropriate icon for payment image view
      val methodBrand = PaymentOwnMethod.Brand(payment.brand)
      drawableId = when (methodBrand) {
        PaymentOwnMethod.Brand.amex ->
          com.ricepo.style.R.drawable.ic_payment_amex
        PaymentOwnMethod.Brand.discover ->
          com.ricepo.style.R.drawable.ic_payment_discover
        PaymentOwnMethod.Brand.masterCard ->
          com.ricepo.style.R.drawable.ic_payment_master
        PaymentOwnMethod.Brand.unionPay ->
          com.ricepo.style.R.drawable.ic_payment_unionpay
        PaymentOwnMethod.Brand.visa ->
          com.ricepo.style.R.drawable.ic_payment_visa
        else -> com.ricepo.style.R.drawable.ic_payment_none
      }
    } else {
      drawableId = when (payment.method) {
        PaymentOwnMethod.WECHAT_PAY_PREVIOUSLY,
        PaymentOwnMethod.WECHAT_PAY ->
          com.ricepo.style.R.drawable.ic_payment_wechatpay
        PaymentOwnMethod.ALIPAY ->
          com.ricepo.style.R.drawable.ic_payment_alipay
        PaymentOwnMethod.UNION_PAY ->
          com.ricepo.style.R.drawable.ic_payment_unionpay
        PaymentOwnMethod.PAYPAL_PAY ->
          com.ricepo.style.R.drawable.ic_payment_paypal
        else -> com.ricepo.style.R.drawable.ic_payment_none
      }
    }
    return ResourcesUtil.getDrawable(drawableId, context)
  }

  fun mapPaymentTitle(payment: PaymentOwnMethod?): String {
    var title = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    val payment = payment ?: return title
    if (payment.method == PaymentOwnMethod.CREDIT ||
      payment.method == PaymentOwnMethod.CREDIT_PREVIOUSLY
    ) {
      if (payment.brand != null && payment.last4 != null) {
        title = "${payment.brand.capitalize()} ${payment.last4}"
      }
    } else if (payment.method != null) {
//            title = ResourcesUtil.getString(payment.method)
      // compatible with previously saved data
      title = payment.name?.localize() ?: ResourcesUtil.getString(payment.method)
    }

    return title
  }
}
