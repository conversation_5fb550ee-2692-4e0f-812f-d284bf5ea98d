package com.ricepo.app.features.luckymenu.repository

import android.util.Log
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.LuckRecommendBean
import javax.inject.Inject

class LuckyRecommendUseCase @Inject constructor(
  private val repository: CombineRestApi
) : BaseUseCase() {

  /**
   * lucky recommendation
   */
  suspend fun requestLuckyRecommend(
    people: String?,
    restaurant: String?,
    selectFoods: MutableList<String>?
  ): LuckRecommendBean? {
    val location = AddressCache.getLocationSuspend()
    Log.d("LuckyRecommendUseCase", location + "")
    location?.let {
      if (selectFoods.isNullOrEmpty()) {
        return repository.requestLuckyRecommend(selectFoods, people, location, null)
      }
      return repository.requestLuckyRecommend(selectFoods, people, location, restaurant)
    }
    return null
  }
}
