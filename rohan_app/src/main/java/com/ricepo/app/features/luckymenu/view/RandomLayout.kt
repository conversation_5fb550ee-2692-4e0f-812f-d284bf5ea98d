package com.ricepo.app.features.luckymenu.view

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import com.ricepo.app.features.luckymenu.data.LuckyCoordinate
import com.ricepo.base.model.Food
import com.ricepo.style.DisplayUtil
import com.ricepo.style.shadow.LuckyShadowLayout
import java.util.Random
import kotlin.Comparator
import kotlin.collections.ArrayList
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

//
// Created by <PERSON> on 11/23/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RandomLayout : RelativeLayout {

  companion object {
    const val TAG = "RandomLayout"
  }

  var randomViewList: MutableList<View> = ArrayList()
  var itemClickable: Boolean = true

  private var onRandomItemClickListener: OnRandomItemClickListener? = null
  private var listIndex: MutableList<LuckyCoordinate> = mutableListOf()

  constructor(context: Context?) : super(context) {}
  constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}

  fun initXY() {
    listIndex.clear()
    createCoordinate()
  }

  /**
   * Add to a random XY position and do not repeat.
   */
  fun addViewAtRandomXY(view: View?, food: Food) {
    if (view == null) return
    view.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    post {
//            Log.d(TAG, "randomViewList::" + randomViewList.size)
      randomViewList.remove(view)
      // 500 random upper bound
//            Log.d(TAG, "listIndex::" + listIndex.size)
      listIndex.forEach { it ->
        val xy = intArrayOf(0, 0)
        xy[0] = it.pointX
        xy[1] = it.pointY
//                Log.d(TAG, "xy[0]::" + xy[0] + ",xy[1]::" + xy[1])
        if (randomViewList.size == 0) {
          addViewAndSetXY(view, xy[0], xy[1], food)
        } else {
          var isRepeat = false
//                    if (xy[0] + view.measuredWidth > measuredWidth || xy[0] - view.measuredWidth < 0
//                        || xy[1] + view.measuredHeight > measuredHeight - 100 || xy[1] - view.measuredHeight - 100 < 0
//                    ) {
//                        return@forEach
//                    }
          // iterate over the existing views and determine if they overlap
          for (subView in randomViewList) {
            val x = subView.x.toInt()
            val y = subView.y.toInt()
            val width = subView.measuredWidth
            val height = subView.measuredHeight
            // create a rectangle
            val v1Rect = Rect(x, y, width + x, height + y)
            val v2Rect = Rect(
              xy[0],
              xy[1],
              view.measuredWidth + xy[0],
              view.measuredHeight + xy[1]
            )
            if (Rect.intersects(v1Rect, v2Rect)) {
//                            Log.d(TAG, "rec_food::${food.name.localize()}")
              isRepeat = true
              break
            }

            val dx = it.pointX - x
            val dy = it.pointY - y
            val dis = sqrt((dx * dx + dy * dy).toDouble())
            val space = width / 2 + view.measuredWidth / 2 + 80
//                        Log.d(TAG, "dis::$dis,space::$space")
            if (dis < space) {
//                            Log.d(TAG, "dis_food::${food.name.localize()}")
              isRepeat = true
              break
            }
          }
          if (!isRepeat) {
            addViewAndSetXY(view, xy[0], xy[1], food)
            return@post
          }
        }
      }
    }
  }

  fun addViewAtRandomXY2(view: View?, food: Food) {
    if (view == null) return
    view.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    post {
//            Log.d(TAG, "randomViewList::" + randomViewList.size)
      randomViewList.remove(view)
      // 500 random upper bound
//            Log.d(TAG, "listIndex::" + listIndex.size)
      for (i in 1..500) {
        val xy = createXY(
          view.measuredHeight,
          view.measuredWidth,
          i
        )
//                Log.d(TAG, "xy[0]::" + xy[0] + ",xy[1]::" + xy[1])
        if (randomViewList.size == 0) {
          addViewAndSetXY(view, xy[0], xy[1], food)
        } else {
          var isRepeat = false
          // iterate over the existing views and determine if they overlap
          for (subView in randomViewList) {
            val x = subView.x.toInt()
            val y = subView.y.toInt()
//                        Log.d(TAG, "x::$x,y::$y")
            val width = subView.measuredWidth
            val height = subView.measuredHeight
            // create a rectangle
            val v1Rect = Rect(x, y, width + x, height + y)
            val v2Rect = Rect(
              xy[0],
              xy[1],
              view.measuredWidth + xy[0],
              view.measuredHeight + xy[1]
            )
            if (Rect.intersects(v1Rect, v2Rect)) {
//                            Log.d(TAG, "rec_food::${food.name.localize()}")
              isRepeat = true
              break
            }

//                        val dx = xy[0] - x
//                        val dy = xy[1] - y
//                        val dis = sqrt((dx * dx + dy * dy).toDouble())
//                        val space = width / 2 + view.measuredWidth / 2 + 20
// //                        Log.d(TAG, "dis::$dis,space::$space")
//                        if (dis < space) {
// //                            Log.d(TAG, "dis_food::${food.name.localize()}")
//                            isRepeat = true
//                            break
//                        }
          }
          if (!isRepeat) {
            addViewAndSetXY(view, xy[0], xy[1], food)
            if (view is LuckyShadowLayout) {
              view.isAdded = true
            }
            return@post
          }
        }
      }
    }
  }

  fun addViewAndSetXY(view: View, x: Int, y: Int, t: Food) {
    val alphaAnim = ObjectAnimator.ofFloat(view, "alpha", 0.1f, 1.0f)
    alphaAnim.duration = 500
    alphaAnim.start()
    removeView(view)
    addView(view)
    randomViewList.add(view)
    view.x = x.toFloat()
    view.y = y.toFloat()
    view.setOnClickListener { v ->
      onRandomItemClickListener?.let {
        if (itemClickable) {
          it.onRandomItemClick(v, t)
        }
      }
    }
  }

  /**
   * add a view to a random list to prevent overwriting
   */
  fun addViewToRandomList(view: View) {
    randomViewList.add(view)
  }

  /**
   * Clear all random views
   */
  fun removeAllRandomView() {
    for (v in randomViewList) {
      removeView(v)
    }
    randomViewList.clear()
  }

  fun clearViewList() {
    randomViewList.clear()
  }

  /**
   * Removes a random view from the list
   */
  fun removeRandomViewFromList(view: View) {
    randomViewList.remove(view)
  }

  private fun random(max: Int): Int {
    return if (max <= 0) {
      0
    } else {
      Random().nextInt(max)
    }
  }

  /**
   * Returns a random coordinate based on the width and height passed in
   */
  @SuppressLint("NewApi")
  private fun createXY(height: Int, width: Int, i: Int): IntArray {
    val xyRet = intArrayOf(0, 0)
    // the screen width
    val layoutWidth = measuredWidth
    val layoutHeight = measuredHeight

    val centerX = (layoutWidth - width) / 2
    val centerY = (layoutHeight - height) / 2.5

    val xMargin = DisplayUtil.dp2PxOffset(2f)
    val yMargin = DisplayUtil.dp2PxOffset(1.5f)
    val xRatio = i % 200
    val yRatio = i % 250
    val xStart = min(layoutWidth / 4, (xRatio * xMargin))
    val xEnd = (xRatio * xMargin)

    val yStart = min(layoutHeight / 2, (yRatio * yMargin))
    val yEnd = (yRatio * yMargin)

    val xMax = layoutWidth - width - xMargin
    val yMax = layoutHeight - height - yMargin

    xyRet[0] = min(max(xMargin * 4, centerX + (-xStart..xEnd).random()), xMax)
    xyRet[1] = min(max(yMargin * 6, centerY.toInt() + (-yStart..yEnd).random()), yMax)

    return xyRet
  }

  /**
   * sort by nearest to the center
   */
  private fun createCoordinate() {
    val centerX = measuredWidth / 2
    val centerY = (measuredHeight - 100) / 2
    for (i in 0..999) {
      val pointX = random(measuredWidth)
      val pointY = random(measuredHeight - 100)
      val d =
        (centerX - pointX) * (centerX - pointX) + (centerY - pointY) * (centerY - pointY)
      val number = kotlin.math.abs(d)
      listIndex.add(LuckyCoordinate(pointX, pointY, kotlin.math.sqrt(number.toDouble())))
    }
    listIndex.sortWith(Comparator { o1, o2 -> o1.distance.compareTo(o2.distance) })
  }

  fun setOnRandomItemClickListener(onRandomItemClickListener: OnRandomItemClickListener) {
    this.onRandomItemClickListener = onRandomItemClickListener
  }

  interface OnRandomItemClickListener {
    fun onRandomItemClick(view: View?, food: Food)
  }
}
