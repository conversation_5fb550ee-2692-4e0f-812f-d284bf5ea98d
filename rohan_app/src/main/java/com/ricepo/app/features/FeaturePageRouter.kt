package com.ricepo.app.features

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.alibaba.android.arouter.facade.Postcard
import com.alibaba.android.arouter.launcher.ARouter
import com.ricepo.app.MainActivity
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.coupon.CouponActivity
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.app.features.menu.MenuActivity
import com.ricepo.app.features.order.OrderActivity
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.features.support.chat.ChatActivity
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.Order
import com.ricepo.app.model.Quote
import com.ricepo.app.model.SupportRuleGroup
import com.ricepo.app.model.SupportRuleItem
import com.ricepo.app.model.TipModel
import com.ricepo.app.restaurant.datasource.RestaurantRequest
import com.ricepo.app.utils.astActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.model.Banner
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Category
import com.ricepo.base.model.Customer
import com.ricepo.base.model.CustomerSubscription
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.aspect.SingleClick
import com.ricepo.style.ResourcesUtil

//
// Created by Thomsen on 1/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object FeaturePageRouter {

  @SingleClick
  private fun postcard(routeName: String): Postcard {
    return ARouter.getInstance()
      .build(routeName)
  }

  private fun postcardBottomIn(routeName: String): Postcard {
    return ARouter.getInstance()
      .build(routeName)
      .withTransition(com.ricepo.style.R.anim.activity_bottom_in, com.ricepo.style.R.anim.activity_no)
  }

  /**
   * prepare for intent new page
   */
  private fun prepareNavigate(isHide: Boolean = true) {
    if (isHide) else return
    // maybe hide loading
    Loading.hideLoading()
  }

  /**
   * navigate home
   */
  fun navigateHome(isLaunch: Boolean) {
    ARouter.getInstance()
      .build(FeaturePageConst.PAGE_HOME_MAIN)
      .withBoolean(FeaturePageConst.PARAM_HOME_DYNAMIC_LINK, false)
      .withBoolean(FeaturePageConst.PARAM_LAUNCH_ENTRANCE, isLaunch)
      .navigation()
  }

  /**
   * navigate login page
   */
  fun navigateLogin() {
    prepareNavigate()
    postcardBottomIn(FeaturePageConst.PAGE_LOGIN)
      .navigation()
  }

  fun navigateLogin(context: Context, isChat: Boolean = false) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_LOGIN)
        .withBoolean(FeaturePageConst.PARAM_LOGIN_CHAT, isChat)
        .navigation(it, FeaturePageConst.REQUEST_CODE_LOGIN)
    }
  }

  /**
   * navigate user profile page
   */
  fun navigateProfile() {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_PROFILE)
      .navigation()
  }

  fun navigateProfileAfterCheckout(context: Context, deliveryMode: String?) {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_PROFILE)
      .withTransition(com.ricepo.style.R.anim.activity_fade_in, com.ricepo.style.R.anim.activity_no)
      .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, FeaturePageConst.PAGE_CHECKOUT)
      .withString(FeaturePageConst.PARAM_DELIVERY_MODE, deliveryMode)
      .navigation(context)
  }

  /**
   * navigate address search page
   */
  fun navigateAddress() {
    prepareNavigate()
    postcardBottomIn(FeaturePageConst.PAGE_ADDRESS)
      .navigation()
  }

  fun navigateAddress(context: Context) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_ADDRESS)
        .navigation(it, FeaturePageConst.REQUEST_CODE_ADDRESS)
    }
  }

  /**
   * navigate menu page by busy alert
   */
  fun navigateMenuForBusy(
    context: Context?,
    restaurant: Restaurant,
    searches: ArrayList<String>? = null,
    groupId: String? = null,
    deliveryMode: String? = null,
    location: String? = null
  ) {
    if (context != null && (restaurant.delivery?.busy == true)) {
      DialogFacade.showPrompt(
        context,
        ResourcesUtil.getString(
          com.ricepo.style.R.string.restaurant_busy_to_menu,
          restaurant.name?.localize() ?: ""
        )
      ) {
        navigateMenu(restaurant, searches, groupId, deliveryMode, location)
      }
    } else {
      navigateMenu(restaurant, searches, groupId, deliveryMode, location)
    }
  }

  /**
   * navigate menu page
   * [searches] is the keyword of search page or food name
   * [groupId]  is firebase dynamic link parameter
   * [deliveryMode] delivery or pickup
   */
  fun navigateMenu(
    restaurant: Restaurant,
    searches: ArrayList<String>? = null,
    groupId: String? = null,
    deliveryMode: String? = null,
    location: String? = null
  ) {
    val postcard = postcard(FeaturePageConst.PAGE_MENU)
      .withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, restaurant)
    if (searches != null) {
      postcard.withStringArrayList(
        FeaturePageConst.PARAM_MENU_RESTAURANT_SEARCH_KEYWORD,
        searches
      )
    }
    if (groupId != null) {
      postcard.withString(FeaturePageConst.PARAM_MENU_GROUP_ID, groupId)
    }
    if (deliveryMode != null) {
      postcard.withString(FeaturePageConst.PARAM_DELIVERY_MODE, deliveryMode)
    }
    location?.let {
      postcard.withString(FeaturePageConst.PARAM_RESTAURANT_LOCATION, it)
    }
    prepareNavigate()
    postcard.navigation()
  }

  /**
   * navigate lucky page
   */
  fun navigateLuckyMenu() {
    val postcard = postcard(FeaturePageConst.PAGE_LUCKY)
    prepareNavigate()
    postcard.navigation()
  }

  /**
   * navigate lucky page
   */
  fun navigateLuckyRecommendMenu(groupSize: String) {
    val postcard = postcard(FeaturePageConst.PAGE_LUCKY_RECOMMEND)
    postcard.withString(FeaturePageConst.PAGE_LUCKY_GROUP_SIZE, groupSize)
    prepareNavigate()
    postcard.navigation()
  }

  /**
   * navigate menu options page
   */
  fun navigateOptions(
    context: Context,
    food: Food?,
    restaurant: Restaurant,
    position: Int?,
    foodIndex: Int?,
    cart: Cart? = null
  ) {
    context.astActivity {
      prepareNavigate()
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_MENU_OPTIONS)
        .withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, restaurant)
        .withFlags(
          Intent.FLAG_ACTIVITY_CLEAR_TOP
            or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
      food?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD, it)
      }
      position?.let {
        postcard.withInt(FeaturePageConst.PARAM_MENU_OPTIONS_POSITION, it)
      }
      foodIndex?.let {
        postcard.withInt(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD_INDEX, it)
      }
      cart?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_OPTIONS_CART, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_MENU_OPTIONS)
    }
  }

  fun navigateCombo(
    context: Context,
    food: Food?,
    restaurant: Restaurant?,
    position: Int?,
    foodIndex: Int?,
    cart: Cart? = null
  ) {
    context.astActivity {
      prepareNavigate()
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_MENU_COMBO)
        .withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, restaurant)
        .withFlags(
          Intent.FLAG_ACTIVITY_CLEAR_TOP
            or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
      food?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_COMBO_FOOD, it)
      }
      position?.let {
        postcard.withInt(FeaturePageConst.PARAM_MENU_COMBO_POSITION, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_MENU_COMBO)
    }
  }

  /**
   * navigate menu feedback page
   */
  fun navigateMenuFeedback(food: Food, context: Context) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_MENU_FEEDBACK)
        .withParcelable(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD, food)
        .navigation(it, FeaturePageConst.REQUEST_CODE_MENU_FEEDBACK)
    }
  }

  /**
   * navigate settings page
   */
  fun navigateSettings() {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_SETTINGS)
      .navigation()
  }

  /**
   * navigate to checkout page
   */
  fun navigateCheckout(
    context: Context,
    deliveryMode: String?,
    restaurant: Restaurant? = null
  ) {
    context.astActivity {
      prepareNavigate()
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT)
      deliveryMode?.let {
        postcard.withString(FeaturePageConst.PARAM_DELIVERY_MODE, it)
      }
      restaurant?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT)
    }
  }

  fun navigateCheckoutAfterSubscription(
    context: Context,
    restaurant: Restaurant,
    deliveryMode: String? = null
  ) {
    navigateCheckoutByEntrance(
      context, restaurant,
      FeaturePageConst.PAGE_SUBSCRIPTION, deliveryMode
    )
  }

  /**
   * navigate to checkout by entrance page
   */
  fun navigateCheckoutByEntrance(
    context: Context,
    restaurant: Restaurant?,
    entrance: String? = null,
    deliveryMode: String? = null,
  ) {
    context.astActivity {
      prepareNavigate()
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT)
        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, entrance)
        .withString(FeaturePageConst.PARAM_DELIVERY_MODE, deliveryMode)
      restaurant?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT)
    }
  }

  fun navigateMenuAfterSubscription(
    context: Context,
    restaurant: Restaurant,
    groupId: String? = null
  ) {
    navigateMenuByEntrance(
      context, restaurant, groupId,
      entrance = FeaturePageConst.PAGE_SUBSCRIPTION
    )
  }

  fun navigateMenuByEntrance(
    context: Context,
    restaurant: Restaurant,
    groupId: String? = null,
    entrance: String? = null
  ) {
    context.astActivity {
      // need use rest id to refresh restaurant in menu page
      val rest = Restaurant(id = restaurant.id)
      prepareNavigate()
      // flag need activity param
      val postcard = postcard(FeaturePageConst.PAGE_MENU)
        .withTransition(com.ricepo.style.R.anim.activity_fade_in, com.ricepo.style.R.anim.activity_no)
        .withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, rest)
        .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, entrance)
        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
      if (groupId != null) {
        postcard.withString(FeaturePageConst.PARAM_MENU_GROUP_ID, groupId)
      }
      postcard.navigation(it, 0)
    }
  }

  fun navigateHomeWithReset(context: Context) {
    context.astActivity {
      prepareNavigate()
      postcard(FeaturePageConst.PAGE_HOME_MAIN)
        .withTransition(com.ricepo.style.R.anim.activity_fade_in, com.ricepo.style.R.anim.activity_no)
        .withBoolean(FeaturePageConst.PARAM_HOME_DYNAMIC_LINK, false)
        .withBoolean(FeaturePageConst.PARAM_LAUNCH_ENTRANCE, false)
        .navigation(it, 0)
    }
  }

  /**
   * navigate to checkout comments page
   */
  fun navigateComment(context: Context, comments: String?, restaurantId: String?) {
    context.astActivity {
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT_COMMENT)
      if (comments != null) {
        postcard.withString(FeaturePageConst.PARAM_PAGE_CHECKOUT_COMMENT, comments)
      }
      restaurantId?.let {
        postcard.withString(FeaturePageConst.PARAM_PAGE_CHECKOUT_RESTAURANT_ID, it)
      }
      prepareNavigate()
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT_COMMENT)
    }
  }

  /**
   * navigate to checkout tips page
   */
  fun navigateTips(
    context: Context,
    tips: ArrayList<TipModel>?,
    tipPrompt: String?,
    restaurant: Restaurant?
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT_TIPS)
        .withParcelableArrayList(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_OPTIONS, tips)
        .withString(FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_PROMPT, tipPrompt)
        .withParcelable(
          FeaturePageConst.PARAM_PAGE_CHECKOUT_TIPS_RESTAURANT,
          restaurant ?: Restaurant()
        )
        .navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT_TIPS)
    }
  }

  /**
   * navigate to checkout tips enter page
   */
  fun navigateTipsEnter(context: Context) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT_TIPS_ENTER)
        .navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT_TIPS_ENTER)
    }
  }

  /**
   * navigate to coupon page
   */
  fun navigateCoupon(
    context: Context,
    showCouponCase: ShowCouponCase = ShowCouponCase.all,
    restaurant: Restaurant?,
    subtotal: Int = 0,
    options: ArrayList<Coupon>? = null,
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_COUPON)
        .withParcelable(FeaturePageConst.PARAM_PAGE_COUPON_SHOWCASE, showCouponCase)
        .withInt(FeaturePageConst.PARAM_PAGE_COUPON_SUBTOTAL, subtotal)
        .withParcelableArrayList(FeaturePageConst.PARAM_PAGE_COUPON_OPTIONS, options)
        .withParcelable(FeaturePageConst.PARAM_RESTAURANT, restaurant)
        .navigation(it, FeaturePageConst.REQUEST_CODE_COUPON)
    }
  }

  /**
   * navigate to payment page
   */
  fun navigatePayment(
    context: Context,
    showPaymentCase: ShowPaymentCase = ShowPaymentCase.all,
    paymentHandle: PaymentHandleMode = PaymentHandleMode.none,
    order: Order? = null
  ) {
    context.astActivity {
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_PAYMENT)
        .withParcelable(FeaturePageConst.PARAM_PAGE_PAYMENT_SHOWCASE, showPaymentCase)
        .withParcelable(FeaturePageConst.PARAM_PAGE_PAYMENT_HANDLE, paymentHandle)
      if (order != null) {
        postcard.withParcelable(FeaturePageConst.PARAM_PAGE_PAYMENT_ORDER, order)
      }
      prepareNavigate()
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_PAYMENT)
    }
  }

  /**
   * navigate to payment add card page
   */
  fun navigatePaymentCard(
    context: Context,
    showPaymentCase: ShowPaymentCase = ShowPaymentCase.all,
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_PAYMENT_CARD)
        .withParcelable(FeaturePageConst.PARAM_PAGE_PAYMENT_SHOWCASE, showPaymentCase)
        .navigation(it, FeaturePageConst.REQUEST_CODE_PAYMENT_CARD)
    }
  }

  /**
   * navigate to order page from profile page
   */
  fun navigateRecentOrder(context: Context, order: Order?, requestCode: Int = 0) {
    if (order != null) {
      context.astActivity {
        prepareNavigate()
        postcard(FeaturePageConst.PAGE_ORDER)
          .withParcelable(FeaturePageConst.PARAM_ORDER, order)
          .navigation(it, requestCode)
      }
    }
  }

  /**
   * navigate to order page
   */
  fun navigateOrderFromProfile(
    context: Context,
    order: Order?,
    requestCode: Int = 0,
    position: Int = 0
  ) {
    if (order != null) {
      context.astActivity {
        prepareNavigate()
        postcard(FeaturePageConst.PAGE_ORDER)
          .withParcelable(FeaturePageConst.PARAM_ORDER, order)
          .withInt(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, position)
          .navigation(it, requestCode)
      }
    }
  }

  /**
   * navigate to order page
   */
  fun navigateOrder(context: Context, order: Order?, requestCode: Int = 0) {
    if (order != null) {
      context.astActivity {
        prepareNavigate()
        postcard(FeaturePageConst.PAGE_ORDER)
          .withParcelable(FeaturePageConst.PARAM_ORDER, order)
          .withFlags(
            Intent.FLAG_ACTIVITY_CLEAR_TOP
              or Intent.FLAG_ACTIVITY_NEW_TASK
              or Intent.FLAG_ACTIVITY_SINGLE_TOP
          )
          .navigation(it, requestCode)
      }
    }
  }

  /**
   * navigate to order page by order id
   */
  fun navigateOrder(context: Context, orderId: String?, requestCode: Int = 0) {
    if (orderId != null) {
      context.astActivity {
        prepareNavigate()
        postcard(FeaturePageConst.PAGE_ORDER)
          .withString(FeaturePageConst.PARAM_ORDER_ID, orderId)
          .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, FeaturePageConst.PAGE_CHECKOUT)
          .withFlags(
            Intent.FLAG_ACTIVITY_CLEAR_TOP
              or Intent.FLAG_ACTIVITY_NEW_TASK
              or Intent.FLAG_ACTIVITY_SINGLE_TOP
          )
          .navigation(it, requestCode)
      }
    }
  }

  /**
   * navigate to order page from checkout
   * paymentError is wechat error information
   */
  fun navigateOrderAfterCheckout(
    order: Order?,
    paymentError: String? = null,
    restaurant: Restaurant? = null
  ) {
    if (order != null) {
      prepareNavigate()
      postcard(FeaturePageConst.PAGE_ORDER)
        .withParcelable(FeaturePageConst.PARAM_ORDER, order)
        .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, FeaturePageConst.PAGE_CHECKOUT)
        .withString(FeaturePageConst.PARAM_PAGE_ORDER_PAYMENT_ERROR, paymentError)
        .withParcelable(FeaturePageConst.PARAM_RESTAURANT, restaurant)
        .withFlags(
          Intent.FLAG_ACTIVITY_CLEAR_TOP
            or Intent.FLAG_ACTIVITY_NEW_TASK
            or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
        .navigation()
    }
  }

  /**
   * navigate to subscription page
   */
  fun navigateSubscription(
    context: Context,
    subscriptionPlan: SubscriptionPlan,
    isOnlySubscribe: Boolean,
    isProfile: Boolean = false,
    entrance: String? = null,
    restaurant: Restaurant? = null
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_SUBSCRIPTION)
        .withParcelable(FeaturePageConst.PARAM_SUBSCRIPTION_PLAN, subscriptionPlan)
        .withBoolean(FeaturePageConst.PARAM_SUBSCRIPTION_ONLY_SUBSCRIBE, isOnlySubscribe)
        .withBoolean(FeaturePageConst.PARAM_SUBSCRIPTION_PROFILE_SUBSCRIBE, isProfile)
        .withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, entrance)
        .withParcelable(FeaturePageConst.PARAM_RESTAURANT, restaurant)
        .navigation(it, FeaturePageConst.REQUEST_CODE_SUBSCRIPTION)
    }
  }

  /**
   * navigate to subscription page update
   */
  fun navigateSubscriptionUpdate(context: Context, subscription: CustomerSubscription) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_SUBSCRIPTION_UPDATE)
        .withParcelable(FeaturePageConst.PARAM_SUBSCRIPTION_CUSTOMER, subscription)
        .navigation(it, FeaturePageConst.REQUEST_CODE_SUBSCRIPTION)
    }
  }

  /**
   * navigate to driver rating page
   */
  fun navigateDriverRating(
    context: Context,
    order: Order,
    position: Int,
    requestCode: Int = FeaturePageConst.REQUEST_CODE_DRIVER_RATING,
    isHide: Boolean = true
  ) {
    context.astActivity {
      prepareNavigate(isHide)
      postcardBottomIn(FeaturePageConst.PAGE_DRIVER_RATING)
        .withParcelable(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER, order)
        .withInt(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, position)
        .navigation(it, requestCode)
    }
  }

  /**
   * navigate to order support
   */
  fun navigateOrderSupport(context: Context, order: Order) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_ORDER_SUPPORT)
        .withParcelable(FeaturePageConst.PARAM_ORDER_SUPPORT_ORDER, order)
        .navigation(it, FeaturePageConst.REQUEST_CODE_ORDER_SUPPORT)
    }
  }

  fun navigateOrderSupport(
    context: Context,
    order: Order,
    supportItems: ArrayList<SupportRuleGroup>
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_ORDER_SUPPORT)
        .withParcelable(FeaturePageConst.PARAM_ORDER_SUPPORT_ORDER, order)
        .withParcelableArrayList(FeaturePageConst.PARAM_ORDER_SUPPORT_ITEMS, supportItems)
        .navigation(it, FeaturePageConst.REQUEST_CODE_ORDER_SUPPORT)
    }
  }

  fun navigateOrderSupportIssue(
    context: Context,
    order: Order,
    ruleItem: SupportRuleItem,
    ruleItems: ArrayList<SupportRuleItem>
  ) {
    context.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_ORDER_SUPPORT_ISSUE)
        .withParcelable(FeaturePageConst.PARAM_ORDER_SUPPORT_ORDER, order)
        .withParcelable(FeaturePageConst.PARAM_ORDER_SUPPORT_RULE_ITEM, ruleItem)
        .withParcelableArrayList(FeaturePageConst.PARAM_ORDER_SUPPORT_RULE_ITEMS, ruleItems)
        .navigation(it, FeaturePageConst.REQUEST_CODE_ORDER_SUPPORT_ISSUE)
    }
  }

  /**
   * navigate to order support chat
   */
  fun navigateOrderSupportChat(context: Context) {
    context.astActivity {
      CustomerCache.liveCustomer {
        prepareNavigate()
        if (it == null) {
          navigateLogin(context, isChat = true)
        } else {
          postcardBottomIn(FeaturePageConst.PAGE_ORDER_SUPPORT_CHAT)
            .withFlags(
              Intent.FLAG_ACTIVITY_CLEAR_TOP
                or Intent.FLAG_ACTIVITY_NEW_TASK
                or Intent.FLAG_ACTIVITY_SINGLE_TOP
            )
            .navigation(context)
        }
      }
    }
  }

  /**
   * navigate to restaurant search
   */
  fun navigateRestaurantSearch(context: Context, location: String?, regionId: String?, miles: Int?) {
    context.astActivity {
      val postcard = postcard(FeaturePageConst.PAGE_RESTAURANT_SEARCH)
      if (location != null) {
        postcard.withString(FeaturePageConst.PARAM_SEARCH_LOCATION, location)
      }
      if (regionId != null) {
        postcard.withString(FeaturePageConst.PARAM_SEARCH_REGION_ID, regionId)
      }
      if (miles != null) {
        postcard.withInt(FeaturePageConst.PARAM_SEARCH_MILES, miles)
      }
      prepareNavigate()
      postcard.navigation(context)
    }
  }

  /**
   * navigate to region search
   */
  fun navigateRegionExplore(context: Context?) {
    context?.astActivity {
      prepareNavigate()
      postcardBottomIn(FeaturePageConst.PAGE_REGION_EXPLORE)
        .navigation(context)
    }
  }

  /**
   * navigate to banner web page
   */
  fun navigateBannerWeb(url: String, banner: Banner? = null) {
    prepareNavigate()
    val postcard = postcard(FeaturePageConst.PAGE_BANNER_WEB)
      .withString(FeaturePageConst.PARAM_BANNER_URL, url)
    banner?.let {
      postcard.withParcelable(FeaturePageConst.PARAM_BANNER_DATA, it)
    }
    postcard.navigation()
  }

  fun navigateReferWeb(url: String, order: Order? = null) {
    prepareNavigate()
    val postcard = postcard(FeaturePageConst.PAGE_WEB_REFER)
      .withString(FeaturePageConst.PARAM_BANNER_URL, url)
    order?.let {
      postcard.withParcelable(FeaturePageConst.PARAM_WEB_ORDER, it)
    }
    postcard.navigation()
  }

  fun navigateHtmlWeb(html: String?) {
    val htmlContent = html ?: return
    prepareNavigate()
    val postcard = postcard(FeaturePageConst.PAGE_HTML_WEB)
      .withString(FeaturePageConst.PARAM_WEB_CONTENT, htmlContent)
    postcard.navigation()
  }


  /**
   * navigate to recommend & rank page
   */
  fun navigateRecommend(type: String) {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_RECOMMEND)
      .withString(FeaturePageConst.PARAM_RECOMMEND_TYPE, type)
      .navigation()
  }

  /**
   * navigate to language select page
   */
  fun navigateLanguage(isLaunch: Boolean) {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_LANGUAGE)
      .withBoolean(FeaturePageConst.PARAM_LAUNCH_ENTRANCE, isLaunch)
      .navigation()
  }

  /**
   * navigate to my coin
   */
  fun navigateRewardSummary() {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_REWARD_SUMMARY)
      .navigation()
  }

  /**
   * navigate restaurant submore
   */
  fun navigateRestaurantSubmore(
    restaurantRequest: RestaurantRequest? = null,
    scrollPosition: Int = 0
  ) {
    prepareNavigate()
    val postcard = postcard(FeaturePageConst.PAGE_RESTAURANT_SUBMORE)
    if (restaurantRequest != null) {
      postcard.withParcelable(
        FeaturePageConst.PARAM_RESTAURANT_SUBMORE_REQUEST,
        restaurantRequest
      )
    }
    postcard.withInt(FeaturePageConst.PARAM_RESTAURANT_SUBMORE_SCROLL_POSITION, scrollPosition)
    postcard.navigation()
  }

  /**
   * navigate user feedback
   */
  fun navigateUserFeedback() {
    prepareNavigate()
    postcardBottomIn(FeaturePageConst.PAGE_USER_FEEDBACK)
      .navigation()
  }

  /**
   * navigate menu submore
   */
  fun navigateMenuSubMore(
    context: Context,
    restaurant: Restaurant?,
    category: Category?,
    promoFoods: ArrayList<Food>?,
    entrance: String?
  ) {
    prepareNavigate()
    context.astActivity {
      val postcard = postcard(FeaturePageConst.PAGE_MENU_SUBMORE)
      restaurant?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, it)
      }
      category?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_SUBMORE_CATEGORY, it)
      }
      promoFoods?.let {
        postcard.withParcelableArrayList(
          FeaturePageConst.PARAM_MENU_PROMO_FOODS,
          it
        )
      }
      entrance?.let {
        postcard.withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_MENU_SUBMORE)
    }
  }

  /**
   * navigate menu search
   */
  fun navigateMenuSearch(
    context: Context,
    restaurant: Restaurant?,
    promoFoods: ArrayList<Food>?,
    mapMaxItem: HashMap<String, Int?>?,
    bundles: ArrayList<String>?,
    entrance: String?
  ) {
    prepareNavigate()
    context.astActivity {
      val postcard = postcard(FeaturePageConst.PAGE_MENU_SEARCH)
      restaurant?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, it)
      }
      promoFoods?.let {
        postcard.withParcelableArrayList(FeaturePageConst.PARAM_MENU_PROMO_FOODS, it)
      }
      mapMaxItem?.let {
        postcard.withSerializable(FeaturePageConst.PARAM_MENU_CATEGORY_MAXITEM, it)
      }
      bundles?.let {
        postcard.withStringArrayList(FeaturePageConst.PARAM_MENU_SEARCH_BUNDLES, it)
      }
      entrance?.let {
        postcard.withString(FeaturePageConst.PARAM_PAGE_ENTRANCE, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_MENU_SEARCH)
    }
  }

  /**
   * navigate to refer share
   */
  fun navigateRefer(context: Context) {
    prepareNavigate()
    val postcard = postcardBottomIn(FeaturePageConst.PAGE_REFER)
    context.astActivity {
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_REFER)
    }
  }

  /**
   * navigate to diary image
   */
  fun navigateReferImage(context: Context, order: Order) {
    prepareNavigate()
    val postcard = postcardBottomIn(FeaturePageConst.PAGE_REFER_DIARY)
    postcard.withParcelable(FeaturePageConst.PARAM_DIARY_ORDER, order)

    context.astActivity {
      postcard.navigation(it, 0)
    }
  }

  /**
   * navigate to ricepo points
   */
  fun navigatePointsSummary(customer: Customer) {
    prepareNavigate()
    postcard(FeaturePageConst.PAGE_POINTS_SUMMARY)
      .withParcelable(FeaturePageConst.PARAM_CUSTOMER, customer)
      .navigation()
  }

  fun navigatePoints(
    context: Context,
    quote: Quote?,
    restaurant: Restaurant?,
    points: Int?
  ) {
    context.astActivity {
      val postcard = postcardBottomIn(FeaturePageConst.PAGE_CHECKOUT_POINTS)
      prepareNavigate()
      quote?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_CHECKOUT_QUOTE, it)
      }
      restaurant?.let {
        postcard.withParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, it)
      }
      points?.let {
        postcard.withInt(FeaturePageConst.PARAM_POINTS_VALUE, it)
      }
      postcard.navigation(it, FeaturePageConst.REQUEST_CODE_CHECKOUT_POINTS)
    }
  }

  /**
   * handle notification for onesignal
   */
  fun handleNotificationIntent(context: Context, data: Map<String, String?>): Intent {
    val page = data["page"]
    val restaurantId = data["restaurantId"]
    val orderId = data["orderId"]
    return when (page) {
      "menu" -> {
        if (!restaurantId.isNullOrEmpty()) {
          val restaurant = Restaurant(restaurantId)
          val intent = Intent(context, MenuActivity::class.java)
          val bundle = Bundle()
          bundle.putParcelable(FeaturePageConst.PARAM_MENU_RESTAURANT, restaurant)
          bundle.putString(
            FeaturePageConst.PARAM_PAGE_ENTRANCE,
            FeaturePageConst.PARAM_SUBSCRIPTION_PLAN
          )
          intent.putExtras(bundle)
          intent
        } else {
          defaultHomeIntent(context)
        }
      }
      "order" -> {
        if (!orderId.isNullOrEmpty()) {
          val intent = Intent(context, OrderActivity::class.java)
          intent.putExtra(FeaturePageConst.PARAM_ORDER_ID, orderId)
          intent.putExtra(
            FeaturePageConst.PARAM_PAGE_ENTRANCE,
            FeaturePageConst.PAGE_CHECKOUT
          )
          intent
        } else {
          defaultHomeIntent(context)
        }
      }
      "coupon" -> {
        val intent = Intent(context, CouponActivity::class.java)
        val bundle = Bundle()
        bundle.putSerializable(
          FeaturePageConst.PARAM_PAGE_COUPON_SHOWCASE,
          ShowCouponCase.customer
        )
        intent.putExtras(bundle)
        intent
      }
      "chat" -> {
        val intent = Intent(context, ChatActivity::class.java)
        intent
      }
      else -> {
        defaultHomeIntent(context)
      }
    }
  }

  fun defaultHomeIntent(context: Context): Intent {
    val intent = Intent(context, MainActivity::class.java)
    intent.putExtra(FeaturePageConst.PARAM_HOME_DYNAMIC_LINK, false)
    return intent
  }

  fun defaultHomePendingIntent(context: Context, params: HashMap<String, String>): PendingIntent {
    val intent = Intent(context, MainActivity::class.java)
    intent.putExtra(FeaturePageConst.PARAM_HOME_DYNAMIC_LINK, false)
    intent.putExtra(FeaturePageConst.PARAM_HOME_NOTIFICATION, true)
    intent.putExtra(FeaturePageConst.PARAM_HOME_NOTIFICATION_DATA, params)
    return PendingIntent.getActivity(
      context, 0 /* Request code */, intent,
      PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_ONE_SHOT
    )
  }
}
