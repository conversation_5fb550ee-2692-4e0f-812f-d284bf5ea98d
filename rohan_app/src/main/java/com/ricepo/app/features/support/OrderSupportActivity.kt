package com.ricepo.app.features.support

import android.os.Bundle
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityOrderSupportBinding
import com.ricepo.app.databinding.LayoutOrderSupportGroupBinding
import com.ricepo.app.databinding.LayoutOrderSupportNormalBinding
import com.ricepo.app.databinding.LayoutOrderSupportOtherBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.model.Order
import com.ricepo.app.model.SupportRuleGroup
import com.ricepo.app.model.SupportRuleItem
import com.ricepo.app.model.feedbackType
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 9/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_ORDER_SUPPORT)
class OrderSupportActivity : BaseActivity() {

  val viewModel: OrderSupportViewModel by viewModels()

  private lateinit var binding: ActivityOrderSupportBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityOrderSupportBinding.inflate(layoutInflater)
    setContentView(binding.root)

    val supportGroups = intent.getParcelableArrayListExtra<SupportRuleGroup>(
      FeaturePageConst.PARAM_ORDER_SUPPORT_ITEMS
    )
    intent.getParcelableExtra<Order>(FeaturePageConst.PARAM_ORDER_SUPPORT_ORDER)?.let {
      showOrderView(it)
      showSupportGroupView(it, supportGroups)
    }
  }

  private fun showOrderView(order: Order) {
    // set title and color
    setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.get_help))

    // set order code
    binding.tvOrderCode.text = ResourcesUtil.getString(com.ricepo.style.R.string.support_order_number, order.passcode)
  }

  private fun showSupportGroupView(order: Order, ruleGroups: List<SupportRuleGroup>?) {
    ruleGroups?.forEachIndexed { index, group ->
      if (group.name != null) {
        addSupportGroupView(index, group.name)
      }
      if (group.items != null) {
        showSupportView(order, group.items)
      }
    }
  }

  private fun addSupportGroupView(index: Int, name: InternationalizationContent) {
    val groupBinding = LayoutOrderSupportGroupBinding.inflate(layoutInflater)
    groupBinding.tvSupportGroupTitle.text = name.localize()
    if (index == 0) {
      val params = groupBinding.tvSupportGroupTitle.layoutParams as ConstraintLayout.LayoutParams
      params.topMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_10)
      groupBinding.tvSupportGroupTitle.layoutParams = params
    }
    binding.llOrderSupport.addView(groupBinding.root)
  }

  private fun showSupportView(order: Order, items: List<SupportRuleItem>) {
    val normalRules = items.filterNot { feedbackType.equals(it.type, true) }
    val nr = arrayListOf<SupportRuleItem>()
    nr.addAll(normalRules)
    normalRules.forEachIndexed { index, it ->
      addSupportRuleView(order, it, index, nr)
    }

    val feedbackRules = items.filter { feedbackType.equals(it.type, true) }
    val fr = arrayListOf<SupportRuleItem>()
    fr.addAll(feedbackRules)
    feedbackRules.forEach {
      addSupportFeedbackView(order, it, fr)
    }
  }

  private fun addSupportRuleView(
    order: Order,
    item: SupportRuleItem,
    position: Int,
    items: ArrayList<SupportRuleItem>
  ) {
    val normalBinding = LayoutOrderSupportNormalBinding.inflate(layoutInflater)
    normalBinding.tvSupportNormal.text = item.title?.localize()
    val params = LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    )
    params.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
    params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
    normalBinding.root.layoutParams = params

    val size = items.size
    normalBinding.tvSupportDivider.isVisible = ((size - 1) != position)

    when (size) {
      1 -> {
        normalBinding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
      }
      2 -> {
        when (position) {
          0 -> {
            normalBinding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_top)
          }
          1 -> {
            normalBinding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_bottom)
          }
        }
      }
      else -> {
        when (position) {
          0 -> {
            normalBinding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_top)
          }
          (size - 1) -> {
            normalBinding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_bottom)
          }
          else -> {
            normalBinding.root.setBackgroundResource(com.ricepo.style.R.color.card_background)
          }
        }
      }
    }

    binding.llOrderSupport.addView(normalBinding.root)

    normalBinding.root.clickWithTrigger {
      FeaturePageRouter.navigateOrderSupportIssue(this, order, item, items)
    }
  }

  private fun addSupportFeedbackView(order: Order, item: SupportRuleItem, items: ArrayList<SupportRuleItem>) {
    val otherBinding = LayoutOrderSupportOtherBinding.inflate(layoutInflater)
    otherBinding.tvSupportOther.text = item.title?.localize()

    binding.llOrderSupport.addView(otherBinding.root)

    otherBinding.root.clickWithTrigger {
      FeaturePageRouter.navigateOrderSupportIssue(this, order, item, items)
    }
  }
}
