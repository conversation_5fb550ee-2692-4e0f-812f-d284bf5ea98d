package com.ricepo.app.features.checkout

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StrikethroughSpan
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.features.checkout.data.CalculatedResult
import com.ricepo.app.features.checkout.data.CheckoutSection
import com.ricepo.app.features.checkout.data.CheckoutSectionItem
import com.ricepo.app.features.checkout.data.CommentsSectionItem
import com.ricepo.app.features.checkout.data.ExtraFeesSectionItem
import com.ricepo.app.features.checkout.data.FeesDiscountItem
import com.ricepo.app.features.checkout.data.FeesSectionItem
import com.ricepo.app.features.checkout.data.FeesTipItem
import com.ricepo.app.features.checkout.data.NormalSectionItem
import com.ricepo.app.features.checkout.data.OrderCellInfo
import com.ricepo.app.features.coupon.data.CouponValidator
import com.ricepo.app.features.menu.MenuGroupUseCase
import com.ricepo.app.features.menu.MenuUseCase
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.Fees
import com.ricepo.app.model.FeesTip
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderReq
import com.ricepo.app.model.OrderReqGroup
import com.ricepo.app.model.OrderReqItem
import com.ricepo.app.model.OrderReqStripe
import com.ricepo.app.model.OrderReqTip
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.Quote
import com.ricepo.app.model.QuoteRequest
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.QuoteResponseData
import com.ricepo.app.model.QuoteResponseError
import com.ricepo.app.model.ValidateCouponReq
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseApplication
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.deepCopy
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Discount
import com.ricepo.base.model.DiscountCondition
import com.ricepo.base.model.LatLon
import com.ricepo.base.model.RequireTip
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.base.tools.tilde.removeElementsAtIndexes
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebasePoolEvent
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourcesUtil
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableOnSubscribe
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.lang.Exception
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.roundToInt

//
// Created by Thomsen on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CheckoutUseCase @Inject constructor(
  private val addressDao: AddressDao,
  private val repository: RestaurantRemote,
  private val combineRepo: CombineRestApi,
  private val groupUseCase: MenuGroupUseCase,
  private val mapper: CheckoutMapper,
  private val postExecutionThread: PostExecutionThread
) :
  MenuUseCase(repository, combineRepo, mapper, postExecutionThread) {

  /**
   * check restaurant cart with menu or group menu
   */
  fun checkRestaurantCart(restaurant: Restaurant?): Observable<RestaurantCart> {
    return Observable.create<RestaurantCart> { emitter ->
      var restaurantCart = RestaurantCartCache.getRestaurantCart(restaurant)

      // get restaurant by menu or group
      val restaurant: Restaurant? = if (restaurantCart?.restaurant == null) {
        val groupOrder = GroupOrderCache.getOrderGroup()
        if (groupOrder != null) {
          Restaurant(groupOrder.restaurant.id)
        } else {
          null
        }
      } else {
        restaurantCart?.restaurant
      }

      // restaurant carts
      val cartList = restaurantCart?.cartList

      // check the restaurant and self cart list
      restaurantCart = RestaurantCart(
        cartList, restaurant,
        comments = restaurantCart?.comments
      )

      emitter.onNext(restaurantCart)
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  /**
   * observe the login success
   */
  fun observeLoginSuccess(): Observable<Any> {
    return Observable.create(
      ObservableOnSubscribe<Any> { emitter ->
        val customer = CustomerCache.getCustomer()
        if (customer != null) {
          emitter.onNext(customer)
        } else {
          emitter.onNext(Any())
        }
      }
    ).subscribeOn(postExecutionThread.ioScheduler)
  }

  fun getQuote(
    carts: List<Cart>?,
    restaurant: Restaurant?,
    deliveryType: CheckoutViewModel.DeliveryMethod,
    quoteReq: QuoteRequest?
  ):
    Observable<Pair<FormatUserAddress, QuoteResponse>> {
    return Observable.create(
      ObservableOnSubscribe<Pair<FormatUserAddress, QuoteResponse>> { emitter ->

        val address = addressDao.getAddressLatest()
        if (address != null) {
          // member of group don't get quote
          if (groupUseCase.memberInGroup(restaurant)) {
            emitter.onNext(Pair(address, QuoteResponseError(ErrorData())))
          } else {
            val items = getCartItems(carts, restaurant)
            // return when qty reduce to zero
            if (items.isNullOrEmpty()) {
              emitter.onNext(Pair(address, QuoteResponseError(ErrorData())))
              return@ObservableOnSubscribe
            }
            val reqAddress = FormatUserAddress(
              unit = address.unit,
              number = address.number,
              street = address.street,
              note = address.note,
              zipcode = address.zipcode,
              location = address.location,
              city = address.city,
              country = address.country,
              formatted = address.formatted,
              state = address.state
            )
            val location = LatLon(
              lat = address?.location?.coordinates?.getOrNull(1) ?: 0.0,
              lon = address?.location?.coordinates?.getOrNull(0) ?: 0.0
            )
            val poolId = restaurant?.pool?.id
            val body = if (deliveryType == CheckoutViewModel.DeliveryMethod.pickup) {
              QuoteRequest(
                null, null, items, null,
                quoteReq?.couponCode, quoteReq?.tipOption, quoteReq?.payment,
                points = quoteReq?.points
              )
            } else {
              QuoteRequest(
                location, reqAddress, items, poolId,
                quoteReq?.couponCode, quoteReq?.tipOption,
                quoteReq?.payment, quoteReq?.expressDelivery,
                points = quoteReq?.points
              )
            }
            val restaurantId = restaurant?.id ?: ""
            val single = repository.getQuote(restaurantId, body)

            addDisposable(
              single.subscribeWith(object :
                  DisposableSingleObserver<QuoteResponse>() {
                  override fun onSuccess(t: QuoteResponse) {
                    if (t != null) {
                      emitter.onNext(Pair(address, t))
                    }
                  }

                  override fun onError(e: Throwable) {
                    emitter.onNext(
                      Pair(
                        address,
                        QuoteResponseError(
                          ErrorData(e, ErrorData.CODE_REQUEST)
                        )
                      )
                    )
                  }
                })
            )
          }
        }
      }
    ).subscribeOn(postExecutionThread.ioScheduler).share()
  }

  fun getAllCartsGroup(cartList: List<Cart>?, restaurant: Restaurant?, isMapper: Boolean): List<Cart> {
    return groupUseCase.getAllCartsGroup(cartList, restaurant, isMapper)
  }

  fun checkMinimum(restaurantCart: RestaurantCart?): List<Cart>? {
    val allCarts = getAllCartsGroup(
      restaurantCart?.cartList,
      restaurantCart?.restaurant, false
    )
    val cartList = mapper.groupByCarts(allCarts)

    val total = cartList?.fold(0) { acc, item ->
      val sum = acc + (item.price * (item.qty ?: 0))
      sum
    } ?: 0

    val result = mutableListOf<Cart>()
    cartList?.filter {
      it.minimum != null
    }?.forEach { cart ->
      if ((cart.minimum ?: 0) > total) {
        result.add(cart)
      }
    }
    result.sortBy { it.minimum }
    return result
  }

  fun constructCartSection(restaurantCart: RestaurantCart?): CheckoutSection? {
    // get the restaurant cart list
    var cartList = RestaurantCartCache.getCartListByGroup(restaurantCart)

    // if have group and get all carts
    cartList = getAllCartsGroup(cartList, restaurantCart?.restaurant, true)

    val cartSectionItems = cartList.map {
      NormalSectionItem(cart = it)
    }

    return CheckoutSection(items = cartSectionItems, type = CheckoutSection.TYPE_NORMAL)
  }

  fun constructExtraSection(quote: Quote?): CheckoutSection? {
    if (quote == null) return null

    val extraFees = quote.extraFees
    if (extraFees != null) {
      val extraFeesItems = extraFees.map {
        ExtraFeesSectionItem(extraFee = it)
      }
      return CheckoutSection(
        items = extraFeesItems,
        type = CheckoutSection.TYPE_EXTRA_FEES
      )
    }
    return null
  }

  fun constructCommentSection(
    input: CheckoutViewModel.Input,
    comments: String?,
    restaurant: Restaurant?
  ): CheckoutSection {
    // default comment message
    var commentMessage = ResourcesUtil.getString(com.ricepo.style.R.string.add_comments)

    if (!comments.isNullOrEmpty()) {
      commentMessage = comments
    }

    val commentsCells = listOf(
      OrderCellInfo(
        leftText = commentMessage,
        rightText = "",
        rightIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_right_arrow, input.context),
        click = {
          // intent to comment page
          FeaturePageRouter.navigateComment(input.context, comments, restaurant?.id)
        }
      )
    )

    val commentSectionItem = commentsCells.map {
      CommentsSectionItem(it)
    }

    return CheckoutSection(commentSectionItem, type = CheckoutSection.TYPE_COMMENT)
  }

  fun constructQuoteSection(
    input: CheckoutViewModel.Input,
    quote: Quote?,
    restaurantCart: RestaurantCart?,
    deliveryMode: CheckoutViewModel.DeliveryMethod,
    entrance: String? = null,
    removeCoupon: () -> Unit,
    selectPoints: (price: Int) -> Unit,
  ): CheckoutSection? {
    val quote = quote ?: return null

    val fees = quote.expenses
    val coupon = quote.coupons?.selected?.coupon

    val delta = fees?.delta ?: 0

    val service = fees?.service ?: 0
    val tax = fees?.tax ?: 0

    val delivery = fees?.delivery?.delivery ?: 0

    val tip = fees?.tip

    val subtotal = quote.summary?.finalSubtotal ?: 0
    val total = quote.summary?.finalTotal ?: 0

    val restaurant = restaurantCart?.restaurant
    val address = restaurant?.address
    var country = "US"
    if (address is AddressObj) {
      country = address.country ?: "US"
    }

    // init fees list
    var feesInfoList = mutableListOf<OrderCellInfo>()
    var feesInfoTip = mutableListOf<OrderCellInfo>()

    // delta
    if (delta > 0) {
      feesInfoList.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_delta),
          rightText = mapper.formatPrice(delta, country),
          large = false
        )
      )
    }

    // subtotal
    feesInfoList.add(
      OrderCellInfo(
        leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_subtotal),
        rightText = mapper.formatPrice(subtotal, country),
        large = false
      )
    )

    // tax
    if (service > 0) {
      feesInfoList.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_and_service),
          rightText = mapper.formatPrice(service.plus(tax), country),
          leftTips = quote?.notes?.taxAndService?.localize()
        )
      )
    } else {
      feesInfoList.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax),
          rightText = mapper.formatPrice(tax, country)
        )
      )
    }

    // checkout discount
    val discounts = quote.discount

    // delivery
    if (deliveryMode == CheckoutViewModel.DeliveryMethod.delivery) {
      val deliveryFee = fees?.delivery?.amount ?: 0
      val leftDetail = if (deliveryFee == 0) {
        null
      } else {
        quote?.notes?.deliveryFee?.localize()
      }
      val df = mapper.formatPrice(delivery, country)
      var spanText: SpannableStringBuilder? = null

      // free delivery case
      val freeDelivery = discounts?.firstOrNull { "delivery".equals(it.type, ignoreCase = true) }

      if (freeDelivery != null) {
        spanText = SpannableStringBuilder()

        df.let {
          val strikethroughSpan = StrikethroughSpan()
          spanText.append(" $it")
          spanText.setSpan(
            strikethroughSpan,
            spanText.length - it.length,
            spanText.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
          )
        }

        spanText.append(" ")
        val dfResult = delivery - (freeDelivery.amount ?: 0)
        spanText.append(mapper.formatPrice(dfResult, country))
      }

      var deliveryCell = OrderCellInfo(
        leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_delivery),
        leftDetail = leftDetail,
        rightText = df,
        rightSpan = spanText,
        deliveryFee = deliveryFee
      )

      feesInfoList.add(deliveryCell)

      // if user update tips with percent, show percent beside the tips title
      val tipPercent = tip?.percent
      val tipPrice = mapper.formatPrice(tip?.amount ?: 0, country)
      val tipsTitle = if (tipPercent?.isNotEmpty() == true) {
        "${ResourcesUtil.getString(com.ricepo.style.R.string.fees_tip)} : $tipPercent"
      } else {
        "${ResourcesUtil.getString(com.ricepo.style.R.string.fees_tip)} : $tipPrice"
      }

      // always show tip when delivery
      feesInfoTip.add(
        OrderCellInfo(
          leftText = tipsTitle,
          leftDetail = tip?.notes?.tip?.localize(),
          rightText = tipPrice,
          rightIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_right_arrow, input.context),
          click = {
            val requireTip = quote.notes?.requireTip?.localize()
            // alert if restaurant is require minimum tip
            if (!requireTip.isNullOrEmpty()) {
              DialogFacade.showAlert(input.context, requireTip)
            } else {
              // navigate to select tip
              FeaturePageRouter.navigateTips(
                input.context, tip?.options,
                tip?.notes?.tip?.localize(), restaurant
              )
            }
          }
        )
      )
    }

    // convert fees info list into fees section items
    val feeSectionItems = arrayListOf<CheckoutSectionItem>()
    feesInfoList.forEach {
      feeSectionItems.add(FeesSectionItem(it))
    }
    feesInfoTip.forEach {
      feeSectionItems.add(FeesTipItem(it))
    }

    val discountInfoList = mutableListOf<OrderCellInfo>()

    discounts?.forEach { discount ->
      val leftText = discount.name?.localize() ?: ""
      val amount = discount.amount ?: 0
      val rightText = if (amount > 0) {
        "- ${mapper.formatPrice(amount, country)}"
      } else {
        "${mapper.formatPrice(abs(amount), country)}"
      }
      val type = discount.type

      val alpha = if (discount.apply == true) 1f else 0.4f

      val cellInfo: OrderCellInfo? = when (type) {
        DiscountCondition.TYPE_VIP -> {
          val rightIcon = if (quote.plan == null || discount.apply == true) {
            null
          } else {
            ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_right_gold, input.context)
          }
          OrderCellInfo(
            leftText = leftText,
            rightText = rightText,
            leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_crown, input.context),
            color = ResourcesUtil.getColor(com.ricepo.style.R.color.goldSubText, input.context),
            rightIcon = rightIcon,
            alpha = alpha,
            click = {
              val subscription = quote?.plan
              subscription?.let {
                FeaturePageRouter.navigateSubscription(
                  input.context, it,
                  false, entrance = entrance, restaurant = restaurant
                )
              }
            }
          )
        }
        DiscountCondition.TYPE_UNIONPAY,
        DiscountCondition.TYPE_MENU -> {
          OrderCellInfo(
            leftText = leftText,
            rightText = rightText,
            leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_tag_green, input.context),
            color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, input.context)
          )
        }
        DiscountCondition.TYPE_DELIVERY -> {
          OrderCellInfo(
            leftText = leftText,
            rightText = rightText,
            leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_checkmark_green, input.context),
            color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, input.context)
          )
        }
        DiscountCondition.TYPE_POOL -> {
          OrderCellInfo(
            leftText = leftText,
            leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_clock, input.context),
            rightText = rightText,
            color = ResourcesUtil.getColor(com.ricepo.style.R.color.mr, input.context),
            expiredAt = quote.pool?.expiresAt
          )
        }
        else -> {
          if (leftText.isNotEmpty()) {
            // default adjustment discout
            OrderCellInfo(
              leftText = leftText,
              rightText = rightText,
              leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_tag_green, input.context),
              color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, input.context)
            )
          } else null
        }
      }

      cellInfo?.let {
        discountInfoList.add(it)
      }
    }

    // coupon
    val couponCell = OrderCellInfo(
      leftText = ResourcesUtil.getString(com.ricepo.style.R.string.use_coupon),
      leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_promotion, input.context),
      rightText = "",
      color = ResourcesUtil.getColor(com.ricepo.style.R.color.mr, input.context),
      rightIcon = ResourcesUtil.getDrawable(
        com.ricepo.style.R.drawable.ic_right_arrow_small,
        input.context
      ).apply {
        setTint(ResourcesUtil.getColor(com.ricepo.style.R.color.mr))
      },
      rightIconClick = removeCoupon,
      leftDetail = quote.coupons?.note?.localize(),
      click = {
        FeaturePageRouter.navigateCoupon(
          input.context, subtotal = subtotal,
          options = quote.coupons?.options, restaurant = restaurant
        )
      }
    )
    // coupon selected
    if (coupon != null) {
      val couponValue = quote.coupons?.selected?.adjustments?.customer ?: 0
      couponCell.leftText = "${ResourcesUtil.getString(com.ricepo.style.R.string.coupon)}: ${coupon.code}"
      couponCell.coupon = coupon.code
      couponCell.rightText = "- ${mapper.formatPrice(couponValue, country)}"
    }
    discountInfoList.add(couponCell)

    // ricepo points
    val customerPoints = quote.customer?.point?.balance?.price ?: 0
    val points = quote.points?.adjustments?.customer ?: 0
    var nextSelectPoints = 0
    val pointsCell = OrderCellInfo(
      leftText = ResourcesUtil.getString(com.ricepo.style.R.string.use_ricepo_point),
      leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_rice_icon_selected, input.context),
      rightText = "",
      color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, input.context),
      rightIcon = ResourcesUtil.getDrawable(
        com.ricepo.style.R.drawable.ic_radio_selected,
        input.context
      ).apply {
      },
      rightIconSize = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_24dp),
      rightIconEndMargin = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_40dp),
      leftDetail = quote.coupons?.note?.localize(),
//      click = {
//        FeaturePageRouter.navigatePoints(input.context, quote, restaurant, points)
//      }
      click = {
        if (customerPoints <= 0) {
            // disable
        } else {
          selectPoints(nextSelectPoints)
        }
      },
    ).apply {
      if (customerPoints > 0) {
        // has points
        if (points > 0) {
          color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, input.context)
          leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_rice_icon_selected, input.context)
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.use_ricepo_point)
          rightText = "- ${mapper.formatPrice(points, country)}"
          rightIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_radio_selected, input.context)
          nextSelectPoints = 0
        } else {
          color = ResourcesUtil.getColor(com.ricepo.style.R.color.mr, input.context)
          leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_rice_icon, input.context)
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.use_ricepo_point_unselected,
            mapper.formatPrice(customerPoints, country)
          )
          rightText = ""
          rightIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_radio_unselect, input.context)
          nextSelectPoints = quote.customer?.point?.balance?.available ?: 0
        }
        enable = true
      } else {
        // no points
        color = ResourcesUtil.getColor(com.ricepo.style.R.color.mr, input.context)
        leftIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_rice_icon, input.context)
        leftText = ResourcesUtil.getString(com.ricepo.style.R.string.use_ricepo_point)
        rightText = ""
        rightIcon = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_radio_disable, input.context)
        enable = false
      }
    }
    discountInfoList.add(pointsCell)

    // discount section
    discountInfoList.forEach {
      feeSectionItems.add(FeesDiscountItem(it))
    }

    // return fees section with items
    return CheckoutSection(feeSectionItems, type = CheckoutSection.TYPE_FEES)
  }

  fun calculate(
    deliveryMode: CheckoutViewModel.DeliveryMethod,
    selectedCoupon: Coupon?,
    quote: Quote?,
    restaurantCart: RestaurantCart?,
    customTips: Discount?,
    paymentOwnMethod: PaymentOwnMethod?
  ):
    CalculatedResult {
    var fees = Fees()

    val restCart = restaurantCart

    // group and rest carts
    val cartList = getAllCartsGroup(restCart?.cartList, restaurantCart?.restaurant, false)

    // don't modify the params of restaurantCart
    val triple = mapper.mapCarts(cartList, RestaurantCart())

    // set credit to 0
    fees.credit = 0

    // cart list total
    fees.subtotal = triple.second

    // extra fees
    val extra = calcExtraFees(quote)

    // including extra
    fees.subtotal += extra

    if (deliveryMode == CheckoutViewModel.DeliveryMethod.delivery) {

      // decide the minimum, use min from freeDelivery if exists
      val minimum = quote?.minimum ?: restCart?.restaurant?.delivery?.minimum ?: 0

      // check if reach the minimum
      fees.diff = minimum - fees.subtotal

      // check if set 0 if negative
      if (fees.diff < 0) fees.diff = 0

      // add diff in subtotal
      fees.subtotal += fees.diff

      fees.delivery = quote?.expenses?.delivery?.amount ?: 0

      /**
       * decide tips:
       * if requireTip, use the required tip Percentage
       * if no amount and no percent specified,
       *  use $2 for subtotal < 10
       *  use $3 for subtotal < 20
       *  use 15% for subtotal >= 20
       */
      var tip = FeesTip(percent = 0.15, amount = 0, total = 0)
      val requireTip = quote?.requireTip ?: restCart?.restaurant?.requireTip
      if (requireTip is RequireTip.double && requireTip.v1 != null) {
        tip.percent = requireTip.v1
      } else if (customTips != null) {
        // custom tip
        tip = FeesTip(percent = customTips.factor ?: 0.0, amount = customTips.flat ?: 0, total = 0)
      } else {
        if (fees.subtotal <= 1000) {
          tip = FeesTip(percent = 0.0, amount = 200, total = 0)
        } else if (fees.subtotal < 2000) {
          tip = FeesTip(percent = 0.0, amount = 300, total = 0)
        }
      }

      // get tip total
      tip.total = if (tip.amount > 0) tip.amount else fees.subtotal.times(tip.percent).roundToInt()
      fees.tip = tip
    }

    // get all fees
    fees.tax = (quote?.tax ?: restCart?.restaurant?.tax ?: 0.0)
      .times(fees.subtotal.toDouble()).roundToInt()

    // get service fees
    fees.service = Fees.calculateService(fees.subtotal, quote?.serviceFee ?: restCart?.restaurant?.serviceFee)

    // get total
    var total = fees.subtotal.plus(fees.tax).plus(fees.service).plus(fees.delivery)
      .plus(fees.tip.total).plus(fees.credit)

    // calculate discount and coupon.amount
    val (discount, coupon) = applyDiscountAndCoupon(
      subtotal = fees.subtotal,
      selectedCoupon = selectedCoupon, quote = quote
    )

    // apply discount when pool
    val poolDiscount = quote?.pool?.discount ?: 0
    if (poolDiscount > 0 && SimpleDateUtils.toDateDiff(quote?.pool?.expiresAt) > 0 &&
      deliveryMode == CheckoutViewModel.DeliveryMethod.delivery
    ) {
      total -= poolDiscount
    }

    // apply discount when VIP
    if (discount != null && quote?.plan == null) {
      total -= discount
    }

    // apply coupon
    if (coupon != null) {
      total -= coupon.amount ?: 0
    }

    // apply union discount promo
    var unionPromoDiscount: Int? = null
    paymentOwnMethod?.takeIf {
      it.brand?.toLowerCase() == "unionpay" &&
        AddressCache.isCountryUS()
    }?.let {
      quote?.unionPayDiscount?.let {
        val maxDiscount = min(total, 1000)
        unionPromoDiscount = getDiscount(listOf(it), total, maxDiscount)
      }
    }
    unionPromoDiscount?.let {
      total -= it
    }

    return CalculatedResult(
      total = total, fees = fees, discount = discount,
      coupon = coupon, promoDiscount = unionPromoDiscount
    )
  }

  private fun calcExtraFees(quote: Quote?): Int {
    return quote?.extraFees?.fold(0) { result, extraFee ->
      var r = result
      r += (extraFee?.price ?: 0)
      r
    } ?: 0
  }

  /**
   * return pair apply discount and coupon
   */
  private fun applyDiscountAndCoupon(subtotal: Int, selectedCoupon: Coupon?, quote: Quote?): Pair<Int?, Coupon?> {
    // save subtotal to calculate discount and coupon
    var discountSubtotal = subtotal

    // get vip discount
    // to calculate before coupon to avoid total became a negative number
    val discount = getDiscount(quote?.discount, subtotal, discountSubtotal)

    // make sure a user is VIP
    if (discount != null && quote?.plan == null) {
      // apply to discount subtotal
      discountSubtotal -= discount
    }

    // need to process coupon with  deep copy coupon amount
    var coupon: Coupon? = selectedCoupon?.deepCopy()

    if (coupon != null) {
      // recalculate coupon amount
      coupon = calculateCouponAmount(coupon, subtotal, discountSubtotal)

      discountSubtotal -= coupon?.amount ?: 0
    }

    return Pair(discount, coupon)
  }

  /**
   * Calculate coupon amount
   * if percent is not empty, use percent, else use amount
   */
  private fun calculateCouponAmount(coupon: Coupon?, subtotal: Int, maxCoupon: Int): Coupon? {
    if (coupon == null) return null
    val percent = coupon?.percentage ?: 0.0
    if (percent > 0.0) {
      val offAmount = subtotal.times(percent).roundToInt()
      // final amount need to be lower than cap
      coupon.amount = min(offAmount, coupon.amount ?: 0)
    }
    coupon.amount = min(coupon.amount ?: 0, maxCoupon)

    return coupon
  }

  /**
   * calculate discount for vip
   */
  private fun getDiscount(discountConditions: List<DiscountCondition>?, subtotal: Int, maxDiscount: Int): Int? {
    if (discountConditions == null) return null

    val condition = discountConditions.filter {
      val minimum = it.minimum
      if (minimum == null) {
        false
      } else {
        subtotal >= minimum
      }
    }.maxByOrNull { it.minimum!! }

    val discount = condition?.discount ?: return null

    val flat = discount.flat ?: 0
    val factor = discount.factor ?: 0.0
    val calcResult = flat.plus(subtotal.toDouble().times(factor)).roundToInt()

    // discount must be less than or equal to subtotal
    return min(calcResult, maxDiscount)
  }

  fun updateAddress(): Observable<FormatUserAddress> {
    return Observable.create<FormatUserAddress> {
      val address = addressDao.getAddressLatest()
      if (address != null) {
        it.onNext(address)
      }
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  fun addItemCart(item: Cart, itemIndex: Int, restaurantCart: RestaurantCart?): RestaurantCart? {
    if (restaurantCart == null) return null

    // update food and total in cart
    if (restaurantCart?.cartList != null) {
      var cartList = restaurantCart?.cartList?.toMutableList()

      // add the cart
      cartList?.add(item)

      restaurantCart.cartList = cartList

      // update local
      updateRestaurantCart(cartList, restaurantCart?.restaurant, restaurantCart.comments)
    }

    // firebase event add menu item
    AnalyticsFacade.logEvent(item, itemIndex, FirebaseEventName.rAddItem)
    // log add food
    LifecycleNetworkListener.logAddFood(restaurantCart.restaurant?.id, listOf(item.id))

    return restaurantCart
  }

  fun removeItemCart(item: Cart, itemIndex: Int, restaurantCart: RestaurantCart?): RestaurantCart? {
    if (restaurantCart == null) return null
    val cartList = restaurantCart.cartList ?: return restaurantCart

    // remove one cart
    val lastIndex = cartList.indexOfLast {
      val itList = it.opt?.map {
        // compare list without tag, because of group order can modified
        listOf(it.id, it.count)
      }
      val itemList = item.opt?.map {
        listOf(it.id, it.count)
      }
      it.id == item.id && (itList?.toTypedArray() contentEquals itemList?.toTypedArray())
    }
    if (lastIndex > -1) {
      val cl = cartList.removeElementsAtIndexes(lastIndex)

      restaurantCart.cartList = cl

      // update local
      updateRestaurantCart(cl, restaurantCart?.restaurant, restaurantCart.comments)
    }

    // firebase event remove menu item
    AnalyticsFacade.logEvent(item, itemIndex, FirebaseEventName.rRemoveItem)

    return restaurantCart
  }

  fun updateItemCart(
    originalItem: Cart,
    item: Cart,
    itemIndex: Int,
    restaurantCart: RestaurantCart?
  ): RestaurantCart? {
    if (restaurantCart == null) return null
    val cartList = restaurantCart.cartList ?: return restaurantCart

    // remove one cart
    val lastIndex = cartList.indexOfLast {
      val itList = it.opt?.map {
        listOf(it.id, it.count)
      }
      val itemList = originalItem.opt?.map {
        listOf(it.id, it.count)
      }
      it.id == item.id && (itList?.toTypedArray() contentEquals itemList?.toTypedArray())
    }
    if (lastIndex > -1) {
      val cl = cartList.removeElementsAtIndexes(lastIndex)?.toMutableList()

      // add the cart
      cl?.add(item)

      restaurantCart.cartList = cl

      // update local
      updateRestaurantCart(cl, restaurantCart?.restaurant, restaurantCart.comments)
    }

    // firebase event remove menu item
    AnalyticsFacade.logEvent(item, itemIndex, FirebaseEventName.rRemoveItem)

    return restaurantCart
  }

  private fun updateRestaurantCart(carts: List<Cart>?, restaurant: Restaurant?, comments: String?) {
    GlobalScope.launch {
      if (carts.isNullOrEmpty()) {
        // delete the local restaurant
        RestaurantCartCache.deleteRestaurantCart(restaurant)
      } else {
        val restaurantCart = RestaurantCart(carts, restaurant, comments)
        RestaurantCartCache.saveRestaurantCart(restaurantCart)
      }
    }
  }

  fun getRecommendCoupon(restaurant: Restaurant?, fees: Fees?): Observable<List<Coupon>> {
    val restaurantId = restaurant?.id ?: ""
    val subtotal = fees?.subtotal ?: 0

    return Observable.create { emitter ->
      val body = ValidateCouponReq(restaurantId, subtotal)
      val single = repository.getRecommendCoupons(body)

      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<List<Coupon>>() {
          override fun onSuccess(t: List<Coupon>) {
            if (t != null) {
              emitter.onNext(t)
            }
          }

          override fun onError(e: Throwable) {
            e?.printStackTrace()
          }
        })
      )
    }
  }

  fun validateCoupon(
    restaurant: Restaurant?,
    coupon: Coupon?,
    fees: Fees?,
    quote: Quote?,
    customer: Customer? = null
  ): Boolean {
    val c = coupon ?: return true
    // member of group menu needn't validate coupon
    if (restaurant != null && groupUseCase.memberInGroup(restaurant)) return true

    val subtotal = fees?.subtotal ?: 0

    // get customer need in the thread
    val customer = quote?.customer ?: customer ?: CustomerCache.getCustomer()

    return CouponValidator(c, subtotal, customer).run()
  }

  fun preCheckInPickup(quote: QuoteResponse?): Boolean {
    // not request check
    return false
  }

  fun preCheckInDelivery(quote: QuoteResponse?): String? {
    if (quote is QuoteResponseError) {
      return quote.v1.message ?: ResourcesUtil.getString(com.ricepo.style.R.string.error_address_precheck)
    }
    return null
  }

  fun placeOrder(option: CheckoutViewModel.CheckoutOption): Observable<OrderResponse> {
    return Observable.create<OrderResponse> { emitter ->

      // double check & get quote before create order
//            getQuote().subscribe {
//                if (it.second is QuoteResponseData) {
//                    val orderReq = constructOrder(option, (it.second as QuoteResponseData).v1)

      val orderReq = constructOrder(option)

      val single = repository.createOrder(option.restaurant?.id ?: "", orderReq)

      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<OrderResponse>() {
          override fun onSuccess(t: OrderResponse) {
            if (t != null) {
              emitter.onNext(t)
            }
          }

          override fun onError(e: Throwable) {
            emitter.onNext(OrderResponse.error(ErrorData(e)))
          }
        })
      )
//                }
//
//            }
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  private fun constructOrder(option: CheckoutViewModel.CheckoutOption): OrderReq {
    var quote: Quote? = null
    // bag fees with pickup and delivery to add quote param
    if (option.quote is QuoteResponseData) {
      val optionQuote = option.quote as QuoteResponseData
      quote = optionQuote.v1
    }

    return constructOrder(option, quote)
  }

  fun getCartItems(cartList: List<Cart>?, restaurant: Restaurant?): List<OrderReqItem> {
    val carts = groupUseCase.getAllCartsGroup(cartList, restaurant, false)
    return carts.map { cart ->
      val opt = if (cart.opt.count() > 0) cart.opt.map { it.id } else null
      OrderReqItem(
        id = cart.id,
        options = opt
      )
    }
  }

  private fun constructOrder(option: CheckoutViewModel.CheckoutOption, quote: Quote?): OrderReq {
    // construct order items with id or all carts of group
    val items = getCartItems(option.cartList, option.restaurant)

    // construct language
    val lang = LocaleUtil.getLanguageMapping()

    // construct group info for create order
    val groupId = groupUseCase.getGroupId(option.restaurant)
    val groupInfo = if (groupId != null) {
      OrderReqGroup(deviceId = BaseApplication.mDeviceId, groupId = groupId)
    } else {
      null
    }

    return OrderReq(
      tip = OrderReqTip(cash = 0, amount = quote?.expenses?.tip?.amount ?: 0),
      stripe = OrderReqStripe(null),
      quote = quote,
      items = items,
      phone = option.customer?.phone,
      coupon = quote?.coupons?.selected?.coupon?.id,
      comments = option.comments,
      language = lang,
      group = groupInfo,
      window = option.quoteTimeWindows
    )
  }

  fun createPayment(
    paymentOwnMethod: PaymentOwnMethod?,
    order: Order
  ): Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>?>> {
    return Observable.create { emitter ->
      val single: Single<PaymentObj>? = when (paymentOwnMethod?.method) {
        PaymentOwnMethod.CREDIT_PREVIOUSLY,
        PaymentOwnMethod.CREDIT -> {
          repository.createIntentPayment(
            order.id, paymentOwnMethod.stripeId ?: ""
          )
        }
        PaymentOwnMethod.WECHAT_PAY_PREVIOUSLY,
        PaymentOwnMethod.WECHAT_PAY -> {
          repository.createWechatPayment(order.id)
        }
        PaymentOwnMethod.ALIPAY -> {
          repository.createAlipayPayment(order.id)
        }
        PaymentOwnMethod.GOOGLE_PAY -> {
          Single.just(PaymentObj(order = order))
        }
        PaymentOwnMethod.UNION_PAY -> {
          // union pay
          repository.createUnionPayment(order.id)
        }
        PaymentOwnMethod.PAYPAL_PAY -> {
          // union pay
          repository.createPaypalPayment(order.id)
        }
        PaymentOwnMethod.BBVA_PAY -> {
          // bbva pay
          repository.createBBVAPayment(order.id, paymentOwnMethod.stripeId)
        }
        else -> null
      }
      if (single != null) {
        addDisposable(
          single.subscribeWith(object : DisposableSingleObserver<PaymentObj>() {
            override fun onSuccess(t: PaymentObj) {
              if (t != null) {
                with(emitter) { onNext(Result.success(Pair(paymentOwnMethod, t.copy(orderID = order.id)))) }
              }
            }

            override fun onError(e: Throwable) {
              if (e != null) {
                emitter.onNext(Result.failure(e))
              }
            }
          })
        )
      } else {
        emitter.onNext(Result.failure(Exception("create payment error")))
      }
    }
  }

  fun logPoolEvent(pool: RestaurantPool?, eventName: String) {
    if (pool != null) {
      AnalyticsFacade.logEvent(
        FirebasePoolEvent(
          SimpleDateUtils.toDateDiffSeconds(
            pool.expiresAt
          )
        ),
        eventName
      )
    }
  }
}
