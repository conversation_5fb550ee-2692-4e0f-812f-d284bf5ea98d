package com.ricepo.app.features.recommend

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityRecommendBinding
import com.ricepo.app.features.BaseWebActivity
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.recommend.adapter.RecommendAdapter
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_RECOMMEND)
class RecommendActivity : BaseWebActivity() {

  val recommendViewModel: RecommendViewModel by viewModels()

  lateinit var binding: ActivityRecommendBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityRecommendBinding.inflate(layoutInflater)
    setContentView(binding.root)

    intent.getStringExtra(FeaturePageConst.PARAM_RECOMMEND_TYPE)?.let {
      bindViewModel(it)
    }
  }

  private fun bindViewModel(type: String) {
    lifecycleScope.launch {
      recommendViewModel.observeRecommend(this@RecommendActivity, type)
        .collectLatest { foods ->
          val adapter = RecommendAdapter(foods, {})
          binding.recyclerRecommend.adapter = adapter
        }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      // image feedback success
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_FEEDBACK) {
        DialogFacade.showAlert(this, ResourcesUtil.getString(com.ricepo.style.R.string.thank_rating))
      }
    }
  }
}
