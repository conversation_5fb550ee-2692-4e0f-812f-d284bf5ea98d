package com.ricepo.app.features.checkout

import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.checkout.data.CalculatedResult
import com.ricepo.app.features.checkout.data.CheckoutSection
import com.ricepo.app.features.menu.MenuGroupUseCase
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.DeliveryEstimate
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.ExpressDelivery
import com.ricepo.app.model.Fees
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.Quote
import com.ricepo.app.model.QuoteRequest
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.QuoteResponseData
import com.ricepo.app.model.QuoteResponseError
import com.ricepo.app.model.QuoteWindows
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseApplication
import com.ricepo.base.animation.CheckoutLoading
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Discount
import com.ricepo.base.model.Estimate
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.map.extension.distanceMile
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.utils.GpsUtils
import com.ricepo.monitor.EventLevel
import com.ricepo.monitor.MonitorEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.resource.ErrorCode
import com.ricepo.style.LocaleConst
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.Observables
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class CheckoutViewModel @Inject constructor(
  private val useCase: CheckoutUseCase,
  private val groupUseCase: MenuGroupUseCase,
  private val mapper: CheckoutMapper,
  private val postExecutionThread: PostExecutionThread,
  private val combineApi: CombineRestApi
) : BaseViewModel() {

  data class CheckoutOption(
    val sections: List<CheckoutSection>? = null,
    val address: FormatUserAddress? = null,
    var quote: QuoteResponse? = null,
    val restaurant: Restaurant? = null,
    val cartList: List<Cart>? = null,
    val deliveryMethod: DeliveryMethod = DeliveryMethod.delivery,
    val paymentOwnMethod: PaymentOwnMethod? = null,
    var customer: Customer? = null,
    var comments: String? = null,
    var quoteTimeWindows: QuoteWindows? = null,
    var addressConfirm: Boolean = false,
    var addressFarConfirm: Boolean = false,
    var windowConfirm: Boolean = false,
    var message: String? = null,
    val errorData: ErrorData? = null
  )

  enum class DeliveryMethod {
    delivery,
    pickup
  }

  data class Input(
    val context: Activity,
    val willAppear: Observable<Boolean>,
    val didAppear: Observable<Boolean>,
    val changeAddress: Observable<Boolean>,
    val placeOrder: Observable<CheckoutOption>
  )

  data class Output(
    val buttonEnabled: Observable<Boolean>,
    val paymentUpdated: Observable<Any>,
    val orderPaymentCreated: Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>?>>,
    val qtyUpdated: Observable<RestaurantCart>,
    val observeTableUpdated: Observable<CheckoutOption>,
    val observeTableError: Observable<CheckoutOption>
  )

  private var order: Order? = null

  private var paymentOwnMethod: PaymentOwnMethod? = null
  private var paymentTempOwnMethod: PaymentOwnMethod? = null

  private val observeDeliverMethod: PublishSubject<DeliveryMethod> = PublishSubject.create()

  private val observeCoupon: PublishSubject<Boolean> = PublishSubject.create()

  private var fees: Fees? = null
  var quoteTimeWindow: QuoteWindows? = null
  private var customTips: Discount? = null
  private var restaurantCart: RestaurantCart? = null

  var restaurant: Restaurant? = null
  private var selectedAddress: FormatUserAddress? = null
  private var selectedCoupon: Coupon? = null
  private var comments: String = ""

  private var isCouponRemoved = false

  private var quote: Quote? = null

  var deliveryMode: DeliveryMethod = DeliveryMethod.delivery

  private var customer: Customer? = null

  private var reloadQuote = PublishSubject.create<Boolean>()

  private val observeRemoveCart = PublishSubject.create<RestaurantCart>()
  private val observeAddCart = PublishSubject.create<RestaurantCart>()

  private val observeBack = PublishSubject.create<Any>()

  private val observeComments = BehaviorSubject.createDefault<String>("")

  private val observeError = PublishSubject.create<CheckoutOption>()

  private val observeTips = PublishSubject.create<Discount>()

  private val observerPlaceOrderLoginSuccess: BehaviorSubject<Any> = BehaviorSubject.create()

  val observeGroupOrderUpdate = PublishSubject.create<Boolean>()

  val observePaymentUpdated: PublishSubject<PaymentOwnMethod> = PublishSubject.create()

  /**
   * back from payment don't set default payment
   */
  var isFromPayment: Boolean = false

  /**
   * mark true is ordering to prevent group order alert error when ordering
   */
  var isOrdering: Boolean = false

  var entrance: String? = null

  /**
   * fast delivery amount and estimate
   */
  var deliveryEstimate: DeliveryEstimate? = null

  /**
   * already loaded (quote or member)
   */
  var isAlreadyLoaded = false

  /**
   * ricepo points
   */
  var ricepoPoints: Int? = null
  var lastRicepoPoints: Int? = null

  fun transform(input: Input): Output {
    // observe login customer
    observeLoginSuccess(false)

    // Appear
    val initialize = initiate(with = input).share()

    // Observe payment update
    val paymentUpdated = input.willAppear.flatMap { _ ->
      this.getLastPayment()
    }.distinctUntilChanged()

    // check remove promotion
    val checkRemovePromotion = observeRemovePromotion(
      input, observeRemoveCart,
      observeGroupOrderUpdate
    )

    // Cart Selected
    val qtyUpdated = Observable.merge(checkRemovePromotion, observeAddCart)

    // Observe delivery method clicked
    val deliveryMethod = observeDeliverMethod

    /**
     * Observe subtotal update
     * 1. manually delete cart
     * 2. Group refresh
     */
    val observeSubtotalUpdate = Observable.merge(
      qtyUpdated.map { it as Any },
      observeGroupOrderUpdate.map { it as Any }
    )

    // Observe address update
    val addressUpdated = observeAddressUpdated(with = input)

    /**
     * We need to send request to get quote on following event
     * 1 whenever user enters the checkout page
     * 2 whenever user changed address on checkout page
     * 3 user click delivery view when get quote failed last time
     with error message(maybe timeout/network error) instead of `address-undeliverable`
     */
    val quoteUpdated = observeQuoteUpdate(
      input = input, initializeSuccess = initialize,
      addressUpdated = addressUpdated, reloadQuote = reloadQuote,
      deliveryModeUpdated = deliveryMethod,
      paymentUpdated = paymentUpdated
    ).share()

    // Observe coupon update
//        val couponUpdated = observeCouponValidate(input, observeSubtotalUpdate, quoteUpdated)

    /**
     * Observe fees calculate and drive fees table update when
     * 1. subtotal update(cart update or group refreshed)
     * 2. quote update
     * 3. tips update
     * 4. coupon update
     */

    // Observe place order button enabled status
    val buttonEnabled = observeButtonEnable(input, addressUpdated = addressUpdated, quoteUpdated = quoteUpdated)

    // Observe create order
    val orderPaymentCreated = startPlaceOrder(input = input, quoteDriver = quoteUpdated)

    // Observe comments update
    val updateComments = observeComments

    val observeTableUpdated = observeTableUpdated(input, initialize, quoteUpdated, updateComments)

    return Output(
      buttonEnabled,
      paymentUpdated,
      orderPaymentCreated,
      qtyUpdated,
      observeTableUpdated,
      // error chain to handle alert
      observeError
    )
  }

  private fun observeTableUpdated(
    input: Input,
    initialize: Observable<RestaurantCart>,
    quoteUpdated: Observable<QuoteResponse>,
    updateComments: Observable<String>
  ): Observable<CheckoutOption> {

    return Observables.combineLatest(
      Observable.merge(
        // add distinct until changed only init
        input.didAppear.distinctUntilChanged().map {
          val quote = quote
          if (quote != null) QuoteResponseData(quote) else
            QuoteResponseError(ErrorData())
        },
        observeBack.map {
          val quote = quote
          if (quote != null) QuoteResponseData(quote) else
            QuoteResponseError(ErrorData())
        },
        quoteUpdated.map { it as Any }
      ),
      initialize.distinctUntilChanged().map { it as Any },
      updateComments.map { it as Any }
    ) { quoteRes, _, comments ->
      val comment = if (comments.toString().isNullOrEmpty()) {
        restaurantCart?.comments
      } else {
        comments
      }
      Pair(quoteRes, comment)
    }
      .doOnSubscribe {
        disposables.add(it)
      }
      .observeOn(postExecutionThread.ioScheduler)
      .flatMap { triple ->
        val quoteRes = triple.first
        var comments: String? = null
        if (triple.second is String) {
          comments = triple.second as String
        }

        var sections = mutableListOf<CheckoutSection>()

        // 1. section cart items
        // and get all group carts
        var cartSection = useCase.constructCartSection(restaurantCart)
        if (cartSection != null) {
          sections.add(cartSection)
        }

        // if in group order & the customer is the owner
        if (!groupUseCase.memberInGroup(restaurant)) {

          // 2. section extra fees
          val quoteSection = useCase.constructExtraSection(quote)
          if (quoteSection != null) {
            sections.add(quoteSection)
          }

          // 3. section comment
          val commentSection = useCase.constructCommentSection(input, comments, restaurant)
          if (commentSection != null) {
            sections.add(commentSection)
          }

          // 4. section fees
          val feesSection = useCase.constructQuoteSection(
            input,
            quote, restaurantCart, deliveryMode, entrance,
            removeCoupon = {
              // remove coupon
              removeCoupon()
            }, selectPoints = {
              // select points
              ricepoPoints = it
              reloadQuote()
            })
          if (feesSection != null) {
            sections.add(feesSection)
          }
        }

        // get all carts
        val cartList = useCase.getAllCartsGroup(
          restaurantCart?.cartList,
          restaurantCart?.restaurant, true
        )

        var option = CheckoutOption(
          sections = sections, address = selectedAddress,
          quote = quoteRes as QuoteResponse, restaurant = restaurant,
          cartList = cartList,
          comments = comments, quoteTimeWindows = quoteTimeWindow,
          paymentOwnMethod = paymentOwnMethod, deliveryMethod = deliveryMode,
          customer = customer
        )

        Observable.just(option)
      }
  }

    /*
     * Calculate condition:
     * 1. quote created / updated (both minimum and delivery fee can change)
     * 2.subtotal update(cart update or group refreshed)
     * 3.tip updated
     * 4. coupon added / updated
     */
  private fun observeCalculated(
    subtotalUpdated: Observable<Any>,
    quoteUpdated: Observable<QuoteResponse>,
    tipUpdated: Observable<Discount>,
    couponUpdated: Observable<Boolean>,
    paymentUpdated: Observable<Any>
  ):
    Observable<CalculatedResult> {

    // add & minus food trigger subtotal and coupon
    // subtotalUpdated can trigger couponUpdated
    return Observable.merge(
      tipUpdated.map { it as Any },
      couponUpdated.map { it as Any },
      paymentUpdated.map { it as Any }
    ).observeOn(postExecutionThread.ioScheduler)
      .doOnSubscribe {
        disposables.add(it)
      }
      .flatMap { _ ->
        // member in group don't calculate
        if (groupUseCase.memberInGroup(restaurant)) {
          Observable.just(CalculatedResult())
        } else {
          reloadQuote()

          Observable.just(CalculatedResult())
        }
      }
  }

  /**
   * We need to send request to get quote on following event
   * 1 whenever user enters the checkout page
   * 2 whenever user changed address on checkout page
   * 3 user click delivery view when get quote failed last time
   * with error message(maybe timeout/network error) instead of `address-undeliverable`
   */
  private fun observeQuoteUpdate(
    input: Input,
    initializeSuccess: Observable<RestaurantCart>,
    addressUpdated: Observable<FormatUserAddress>,
    reloadQuote: Observable<Boolean>,
    deliveryModeUpdated: Observable<DeliveryMethod>,
    paymentUpdated: Observable<Any>
  ): Observable<QuoteResponse> {

    // refresh after payment and coupon login
    // not refresh after place order login
    val observeLogin = input.didAppear.filter { it }
      .flatMap {
        useCase.observeLoginSuccess()
          .filter { it is Customer }
          .flatMap { observerPlaceOrderLoginSuccess.filter { it !is Customer }.map { it } }
          .distinctUntilChanged()
      }.distinctUntilChanged()

    return Observables.combineLatest(
      input.didAppear.filter { it }.distinctUntilChanged(),
      paymentUpdated,
      Observable.mergeArray(
        initializeSuccess.map { it as Any },
        observeLogin,
        addressUpdated.map { it as Any },
        reloadQuote.map { it as Any },
        deliveryModeUpdated.map { it as Any },
      )
    )
      .flatMap {
        // get quote need by restaurant id (menu or group)
        if (restaurant != null) {
          val quoteRequest = constructQuoteRequest()
          useCase.getQuote(restaurantCart?.cartList, restaurant, deliveryMode, quoteRequest)
            .doOnSubscribe { Loading.showLoading(input.context) }
            .subscribeOn(postExecutionThread.mainScheduler)
            .observeOn(postExecutionThread.mainScheduler)
            .doOnSubscribe {
              disposables.add(it)
            }
            .doOnNext {
              val error = it.second
              if (error is QuoteResponseError && !getQuoteErrorMessage(error.v1).isNullOrEmpty()) {
                val msg = getQuoteErrorMessage(error.v1)
                val option = CheckoutOption(message = msg, errorData = error.v1)
                observeError.onNext(option)
              }
              Loading.hideLoading()
            }
            .observeOn(postExecutionThread.ioScheduler)
            .flatMap {
              // init with address
              selectedAddress = it.first
              // init quote
              val quoteRes = it.second
              if (quoteRes is QuoteResponseData) {
                quote = quoteRes.v1
                // update selected coupon
                quote?.coupons?.selected?.coupon?.let {
                  selectedCoupon = it
                }
                // update last ricepo points
                lastRicepoPoints = ricepoPoints

                // monitor the pool firebase event
                useCase.logPoolEvent(quote?.pool, FirebaseEventName.rPoolCheckout)
                // save default payment for bbva
                val defaultPayment = quote?.defaultPayment
                if (defaultPayment != null && !isFromPayment) {
                  CustomerCache.savePaymentBbva(defaultPayment) { pay ->
                    paymentOwnMethod = pay
                    observePaymentUpdated.onNext(pay)
                  }
                }
                isAlreadyLoaded = true
              } else if (quoteRes is QuoteResponseError) {
                quote = null
              }
              Observable.just(it.second)
            }
        } else {
          quote = null
          Observable.just(QuoteResponseError(ErrorData()))
        }
      }
  }

  fun reloadQuote() {
    // reload quote when cart items > 0
    viewModelScope.launch {
      val carts = withContext(Dispatchers.IO) {
        useCase.getAllCartsGroup(
          restaurantCart?.cartList,
          restaurant, true
        )
      }

      if (carts.isNotEmpty()) {
        reloadQuote.onNext(true)
      } else {
        observeBack.onNext(true)
      }
    }
  }

  fun observeLoginSuccess(reloadQuote: Boolean) {
    if (reloadQuote) {
      // refresh vip discount after login success
      reloadQuote()
    }
    useCase.observeLoginSuccess()
      .subscribe {
        if (it is Customer) {
          customer = it
        }
        observerPlaceOrderLoginSuccess.onNext(it)
      }
  }

  private fun startPlaceOrder(
    input: CheckoutViewModel.Input,
    quoteDriver: Observable<QuoteResponse>
  ): Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>?>> {

    val preCheck = Observables.combineLatest(
      observerPlaceOrderLoginSuccess.map { it as Any },
      input.placeOrder.map {
        it as CheckoutOption
        // init confirm value
        it.addressConfirm = false
        it.addressFarConfirm = false
        it.windowConfirm = false
        it
      }
    ) { c, option ->
      // check the place order data
      checkBeforeCreateOrder(input, option)
        .flatMap {
          checkBeforeCreateOrderTree(input, option, it)
        }.flatMap {
          // backup to check address far
          checkBeforeCreateOrderTree(input, option, it)
        }.flatMap {
          // backup to check window
          checkBeforeCreateOrderTree(input, option, it)
        }
    }.flatMap { observe ->
      observe.filter { it is CheckoutOption }
        .flatMap { option ->
          Observable.just(option)
        }
    }

    return preCheck
      .doOnSubscribe {
        disposables.add(it)
      }
      .observeOn(postExecutionThread.mainScheduler)
      .map {
        isOrdering = true
        CheckoutLoading.showLoading(
          input.context, com.ricepo.style.R.string.placing_order,
          com.ricepo.style.R.string.placing_order_success
        ) {
          Log.i("thom", "place order end success")
          // need intent success to call
          placeOrderEndSuccess()
        }
        it
      }
      .observeOn(postExecutionThread.ioScheduler)
      .flatMap { checkoutOption ->
        // double check quote before create order
        // because of the restaurant or delivery info maybe change in server
        checkQuoteBeforeCreateOrder(input, checkoutOption as CheckoutOption)
      }
      .flatMap {
        if (it is CheckoutOption) {
          // monitor the pool firebase event
          useCase.logPoolEvent(quote?.pool, FirebaseEventName.rPoolOrder)

          val option = it as CheckoutOption
          // update time window
          option.quoteTimeWindows = quoteTimeWindow
          useCase.placeOrder(option)
            .observeOn(postExecutionThread.mainScheduler)
            .flatMap {
              when (it) {
                is OrderResponse.data -> {
                  order = it.v1
                  Observable.just(it.v1)
                }
                is OrderResponse.error -> orderFailed(it.v1)
              }
            }
            .filter { it is Order }
            .doOnSubscribe {
              disposables.add(it)
            }
            .map {
              it as Order
            }
//            .observeOn(postExecutionThread.ioScheduler)
        } else {
          // request error observable
          Observable.just(false)
        }
      }.flatMap {
        if (it is Order) {
          viewModelScope.launch {

            // clear group order info after create order success
            if (groupUseCase.isInGroup(restaurant)) {
              GroupOrderCache.deleteOrder()
            }

            // clear cart after place success
            RestaurantCartCache.deleteRestaurantCart(restaurant)
            // cache order for wexin pay entry
            OrderCache.saveOrder(order)
          }

//                    useCase.createPayment(paymentOwnMethod, it)
//                        .doOnSubscribe {
//                            disposables.add(it)
//                        }
          createPayment(paymentOwnMethod, it)
        } else {
          // not handle the result
          Observable.just(Result.success(null))
        }
      }
  }

  fun createPayment(paymentOwnMethod: PaymentOwnMethod?, itOrder: Order?):
    Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>?>> {
    val o = itOrder ?: order ?: return Observable.just(Result.success(null))
    return useCase.createPayment(paymentOwnMethod, o)
      .doOnSubscribe {
        disposables.add(it)
      }
  }

  private fun checkBeforeCreateOrderTree(input: Input, option: CheckoutOption, it: Any): Observable<Any> {
    return if (it is CheckoutOption && (
      it.addressConfirm || it.addressFarConfirm ||
        it.windowConfirm
      )
    ) {
      checkBeforeCreateOrder(input, option)
    } else {
      Observable.just(it)
    }
  }

  private fun checkBeforeCreateOrder(input: Input, option: CheckoutOption): Observable<Any> {
    val optionQuote = option.quote

    var isCheckSuccess = true

    // check the quote with delivery method
    if (isCheckSuccess && deliveryMode == DeliveryMethod.delivery) {
      val msg = useCase.preCheckInDelivery(optionQuote) ?: ""
      // msg empty is check success
      if (!msg.isNullOrEmpty()) {
        isCheckSuccess = false
        DialogFacade.showAlert(input.context, msg)
      }
    }

    // checkout payment
    if (isCheckSuccess && !isValidPaymentMethod(paymentOwnMethod)) {
      FeaturePageRouter.navigatePayment(input.context)
      isCheckSuccess = false
    }

    if (isCheckSuccess && deliveryMode == DeliveryMethod.delivery) {
      // check address validation before create order
      val address = option.address
      if (isCheckSuccess && address != null) {
        if (address.country == "US" && address.state != "HI" && address.zipcode.isNullOrEmpty()) {
          isCheckSuccess = false
          // ask change address if address without zip code, except honolulu order
          DialogFacade.showAlert(input.context, com.ricepo.style.R.string.error_incomplete_address) {
            FeaturePageRouter.navigateAddress(input.context)
          }
        } else if ((
          address.street.isNullOrEmpty() || address.source
            == FormatUserAddress.SOURCE_GPS
          ) && !option.addressConfirm
        ) {
          // if address.street does not exist, edit or confirm the address
          return Observable.create<Any> { emitter ->
            DialogFacade.showPrompt(
              input.context, title = ResourcesUtil.getString(com.ricepo.style.R.string.confirm_address),
              message = ResourcesUtil.getString(com.ricepo.style.R.string.use_as_address, address.formatted ?: ""),
              positive = {
                // continued follow-up verification
                option.addressConfirm = true
                emitter.onNext(option)
                if (address.source == FormatUserAddress.SOURCE_GPS) {
                  // modify the source null
                  address.source = null
                  AddressCache.saveAddress(address)
                }
              },
              negative = {
                emitter.onNext(false)
                FeaturePageRouter.navigateAddress(input.context)
              },
              negativeId = com.ricepo.style.R.string.edit
            )
          }
        } else {
          val curLoc = GpsUtils.getCurrentLocation(input.context)
          val miles = if (curLoc != null) {
            val homeLoc = address.location?.latLng()
            val curLatLng = LatLng(curLoc.latitude, curLoc.longitude)
            curLatLng.distanceMile(homeLoc)
          } else {
            -1.0
          }
          // the current address distance over 5 miles -> 1 miles
          if (miles >= 1 && !option.addressConfirm) {
            return Observable.create { emitter ->
              DialogFacade.showPrompt(
                input.context,
                messageId = com.ricepo.style.R.string.address_confirm,
                positiveId = com.ricepo.style.R.string.ok,
                negativeId = com.ricepo.style.R.string.support_change_address_title,
                positive = {
                  option.addressConfirm = true
                  emitter.onNext(option)
                },
                negative = {
                  FeaturePageRouter.navigateAddress(input.context)
                  emitter.onNext(false)
                }
              )
            }
          }
        }
      }
    }

    // show tips with delivery and pickup window
    if (isCheckSuccess && quoteTimeWindow?.formatted != null &&
      !option.windowConfirm
    ) {
      val message = if (deliveryMode == DeliveryMethod.pickup) {
        ResourcesUtil.getString(
          com.ricepo.style.R.string.checkout_pickup_window_alert,
          quoteTimeWindow?.formatted?.localize() ?: ""
        )
      } else {
        ResourcesUtil.getString(
          com.ricepo.style.R.string.checkout_window_alert,
          quoteTimeWindow?.formatted?.localize() ?: ""
        )
      }
      return Observable.create { emitter ->
        DialogFacade.showPrompt(
          input.context,
          message = message,
          positiveId = com.ricepo.style.R.string.checkout_continue,
          positive = {
            // double confirm window time
            option.windowConfirm = true
            emitter.onNext(option)
          },
          negative = {
            emitter.onNext(false)
          }
        )
      }
    }

    return Observable.create<Any> { emitter ->
      // recapture customer because of coupon can login
      CustomerCache.liveCustomer() {
        val customer = it
        if (isCheckSuccess && customer !is Customer) {
          // check the login
          isCheckSuccess = false
          DialogFacade.showAlert(input.context, com.ricepo.style.R.string.require_login) {
            FeaturePageRouter.navigateLogin(input.context)
          }
        }
        if (customer is Customer) {
          option.customer = customer as Customer
        }

        emitter.onNext(if (isCheckSuccess) option else false)
      }
    }
  }

  private fun checkQuoteBeforeCreateOrder(input: Input, option: CheckoutOption): Observable<Any> {
    val quoteRequest = constructQuoteRequest()
    return useCase.getQuote(option.cartList, option.restaurant, option.deliveryMethod, quoteRequest)
//      .observeOn(postExecutionThread.mainScheduler)
      .doOnSubscribe {
        disposables.add(it)
      }
      .flatMap {
        val quoteRes = it.second
        if (quoteRes is QuoteResponseData) {
          // check the quote window
          var previousHasValue = false
          if (option.quote is QuoteResponseData) {
            val quote = (option.quote as QuoteResponseData).v1
            quote.windows?.takeIf {
              it.isNotEmpty()
            }?.let {
              previousHasValue = true
            }
          }

          var hasValue = false
          if (quoteRes.v1.windows?.isNotEmpty() == true) {
            hasValue = true
          }

          if (previousHasValue != hasValue) {
            val message = ResourcesUtil.getString(com.ricepo.style.R.string.checkout_precheck_quote_windows)
            observeError.onNext(CheckoutOption(message = message))
            // reload view with new quote
            reloadQuote()
            Observable.just(false)
          } else {
            option.quote = quoteRes
            // check success
            Observable.just(option)
          }
        } else if (quoteRes is QuoteResponseError) {
          CheckoutLoading.hideLoading()
          isOrdering = false
          val error = quoteRes.v1
          val message = getQuoteErrorMessage(error)
          observeError.onNext(CheckoutOption(message = message, errorData = error))
          Observable.just(false)
        } else {
          CheckoutLoading.hideLoading()
          isOrdering = false
          Observable.just(false)
        }
      }
//      .observeOn(postExecutionThread.ioScheduler)
  }

  private fun constructQuoteRequest(): QuoteRequest {

    val payMethod = paymentOwnMethod?.method
    val payment = when {
      payMethod == PaymentOwnMethod.WECHAT_PAY -> {
        PaymentOwnMethod.WECHAT
      }
      paymentOwnMethod?.brand != null -> {
        paymentOwnMethod?.brand?.toLowerCase()
      }
      else -> {
        // alipay unionPay
        // union discount with unionPay otherwise brand unionpay
        payMethod
      }
    }

    val tipOption = if (customTips != null) {
      customTips
    } else {
      null
    }
    val couponCode = if (isCouponRemoved) {
      ""
    } else {
      selectedCoupon?.code
    }
    return QuoteRequest(
      couponCode = couponCode, payment = payment,
      tipOption = tipOption, expressDelivery = deliveryEstimate,
      points = ricepoPoints
    )
  }

  private fun placeOrderEndSuccess() {
    // hideLoading order failed return
    if (!isOrdering) return
    placeOrderSuccess()
    CheckoutLoading.hideLoading()
  }

  fun placeOrderSuccess() {
    if (order != null) {
      isOrdering = false
      FeaturePageRouter.navigateOrderAfterCheckout(
        order,
        restaurant = restaurant
      )
    }
  }

  /**
   * process create order failed
   */
  private fun orderFailed(v1: ErrorData): Observable<Boolean> {
    CheckoutLoading.hideLoading()
    isOrdering = false

    val code = v1.code
    var message = v1.message
    val detailsObj = v1.detailsObj

    if (message == "ignore") return Observable.just(true)

    // sentry send
    var event = MonitorEvent("Place Order Failed", level = EventLevel.ERROR)
    val err = ParserModelFacade.toJson(v1)
    var ord = ""
    if (order != null) {
      ord = ParserModelFacade.toJson(order)
    }
    event.extras = mapOf(
      "err" to err,
      "order" to ord
    )
//    MonitorFacade.captureEvent(event)

    // if coupon issue, remove coupon
    if (code?.contains("coupon-") == true) {
      removeCoupon()
    }

    // if fetch detail coupon info
    if (code == "coupon-restriction-minimum") {
      val minimum = mapper.formatPriceByRestaurant(detailsObj?.minimum ?: 0, restaurant)
      message = "Coupon requires $minimum minimum purchase"
    }

    // if food unavailable
    if (code == "unavailable") {
      var name = detailsObj?.food?.name?.localize()

      // if food is available but option not available
      if (detailsObj?.food?.available == true) {
        val option = detailsObj?.option?.name?.localize()
        name = "${name ?: ""} => ${option ?: ""}"
      }
      message = ResourcesUtil.getString(com.ricepo.style.R.string.error_unavailable, name ?: "")
    }

    // if food deleted
    if (code == "not-found") {
      val id = detailsObj?.id
      var cartList = restaurantCart?.cartList

      // food deleted
      if (detailsObj?.entity == "food") {
        var name = cartList?.first { it.id == id }?.name?.localize()
        message = ResourcesUtil.getString(com.ricepo.style.R.string.error_unavailable, name ?: "")
      }

      // option deleted
      if (detailsObj?.entity == "option") {
        var name = ""
        var optName = ""
        cartList?.forEach c@{ c ->
          c.opt.forEach { o ->
            if (o.id == id) {
              name = c.name.localize()
              optName = o.name.localize()
              return@c
            }
          }
        }
        message = ResourcesUtil.getString(com.ricepo.style.R.string.error_unavailable, "$name => $optName")
      }
    }

    val option = CheckoutOption(message = message, errorData = v1)
    observeError.onNext(option)

    return Observable.just(false)
  }

  private fun getQuoteErrorMessage(error: ErrorData): String? {
    val errorCode = error.code
    val cartList = restaurantCart?.cartList ?: return error.message()
    return if (errorCode == ErrorCode.FOOD_NOT_FOUND ||
      errorCode == ErrorCode.FOOD_OPTION_NOT_FOUND
    ) {
      val foodId = error.details?.food ?: error.detailsObj?.food?.id
      val itemId = error.details?.option ?: error.detailsObj?.option?.id
      val restBundleCount = cartList.distinctBy { it.bundleRestId }?.size ?: 0
      val cart = cartList.find { it.id == foodId }
      val option = cart?.opt?.find { it.id == itemId }
      val restaurantName = cart?.bundleRestName?.localize() ?: restaurant?.name?.localize() ?: ""
      val foodName = cart?.name?.localize() ?: ""
      val optionName = option?.name?.localize() ?: ""
      val message = if (restBundleCount > 1) {
        if (option != null) {
          ResourcesUtil.getString(
            com.ricepo.style.R.string.quote_food_not_found_restaurant,
            restaurantName, optionName
          )
        } else {
          ResourcesUtil.getString(
            com.ricepo.style.R.string.quote_food_not_found_restaurant,
            restaurantName, foodName
          )
        }
      } else {
        if (option != null) {
          ResourcesUtil.getString(com.ricepo.style.R.string.quote_food_option_not_found, foodName, optionName)
        } else {
          ResourcesUtil.getString(com.ricepo.style.R.string.quote_food_not_found, foodName)
        }
      }
      message
    } else {
      error.message()
    }
  }

  fun handleQuoteFailed(
    activity: ComponentActivity,
    errorData: ErrorData?,
    onFailed: () -> Unit
  ) {
    val errorCode = errorData?.code
    // if coupon issue, remove coupon
    if (errorCode?.contains("coupon-") == true) {
      removeCoupon()
    }

    // if food not found or unavailable
    if (ErrorCode.FOOD_NOT_FOUND == errorCode ||
      ErrorCode.FOOD_OPTION_NOT_FOUND == errorCode ||
      ErrorCode.FOOD_UNAVAILABLE == errorCode ||
      ErrorCode.FOOD_OPTION_UNAVAILABLE == errorCode
    ) {
      val food = errorData?.details?.food ?: errorData?.detailsObj?.food?.id
      val item = errorData?.details?.option ?: errorData?.detailsObj?.option?.id
      viewModelScope.launch {
        onFailed()
        Loading.showLoading(activity)
        restaurantCart = withContext(Dispatchers.IO) {
          groupUseCase.updateGroupCartQuality(GroupOrderCache.removeCart(restaurant, food, item))
          RestaurantCartCache.removeCart(restaurant, food, item)
        }
        if (restaurantCart != null) {
          observeRemoveCart.onNext(restaurantCart!!)
        }
      }
    }

    // ricepo point over
    if (errorCode?.contains("ricepo-point") == true) {
      // update customer
      viewModelScope.launch {
        LifecycleNetworkListener.updateCustomer()
      }
      ricepoPoints = lastRicepoPoints
      reloadQuote()
    }
  }

  private fun observeButtonEnable(
    input: Input,
    addressUpdated: Observable<FormatUserAddress>,
    quoteUpdated: Observable<QuoteResponse>
  ): Observable<Boolean> {

    return Observables.combineLatest(
      Observable.merge(
        input.didAppear.map { it as Any },
        addressUpdated.map { it as Any }
      ),
      quoteUpdated.map { it as Any }
    ) { address, quoteResponse ->
      var result = true
      val deliveryMethod = deliveryMode
      if (deliveryMethod == null) {
        result = false
      }
      if (deliveryMethod == DeliveryMethod.delivery) {
        if (address == null) {
          result = false
        }
        if (quoteResponse == null || quoteResponse is QuoteResponseError) {
          result = false
        }
      }
      result
    }.flatMap {
      Observable.just(it)
    }
  }

  fun updateComments(comments: String) {
    this.comments = comments
    observeComments.onNext(comments)
    // save comments
    restaurantCart?.let {
      viewModelScope.launch {
        it.comments = comments
        RestaurantCartCache.saveRestaurantCart(it, false)
      }
    }
  }

  fun updateTips(tips: Discount?) {
    customTips = tips
    if (tips != null) {
      observeTips.onNext(tips)
    }
    reloadQuote()
  }

  // validate and update coupon when
  // 1. coupon been cleaned
  // 2. auto select coupon success
  // 3. subtotal update(cart update or group refreshed
  private fun observeCouponValidate(
    input: CheckoutViewModel.Input,
    subtotalUpdated: Observable<Any>,
    quoteUpdated: Observable<QuoteResponse>
  ): Observable<Boolean> {
    return Observable.mergeArray(
      observeCoupon,
      subtotalUpdated,
    )
      .doOnSubscribe {
        disposables.add(it)
      }
      .observeOn(postExecutionThread.ioScheduler)
      .switchMap {
        if (!useCase.validateCoupon(restaurant, selectedCoupon, fees, quote)) {

          val option = CheckoutOption(message = ResourcesUtil.getString(com.ricepo.style.R.string.invalid_coupon_retry))
          observeError.onNext(option)

          // clear saved coupon
          selectedCoupon = null

          Observable.just(false)
        } else {
          Observable.just(true)
        }
      }
  }

  private fun autoSelectedCoupon(
    input: Input,
    quoteUpdated: Observable<QuoteResponse>
  ): Observable<Boolean> {
    return Observables.zip(
      input.willAppear.filter { it }.distinctUntilChanged(),
      quoteUpdated.map { it as Any }
    ).flatMap {
      // calculate fess subtotal
      val feesUpdate = useCase.calculate(
        deliveryMode, selectedCoupon,
        quote, restaurantCart, customTips, paymentOwnMethod
      )
      useCase.getRecommendCoupon(restaurant, feesUpdate.fees)
    }.flatMap { coupons ->
      // filter out invalid coupons
      val validCoupons = coupons.filter { it.invalid != true }

      if (validCoupons.isNotEmpty()) {
        selectedCoupon = validCoupons.first()
      }

      Observable.just(true)
    }
  }

  private fun observeAddressUpdated(with: Input): Observable<FormatUserAddress> {
    return with.changeAddress.filter { it }
      .flatMap {
        useCase.updateAddress()
      }
  }

  private fun initiate(with: Input): Observable<RestaurantCart> {
        /*
        * Only initiate when page first loaded and appeared
        */
    return with.willAppear.filter { it }
      .distinctUntilChanged()
      .switchMap { _ ->
        useCase.checkRestaurantCart(restaurant)
      }
      .switchMap {
        restaurantCart = it
        if (restaurant?.name == null) {
          restaurant = restaurantCart?.restaurant
        }
        if (restaurant != null && restaurantCart?.cartList != null) {
          getLastPayment()
        }
        Observable.just(it)
      }
  }

  private fun getLastPayment(): Observable<Any> {
    return Observable.create { emitter ->
      CustomerCache.getPayment {
        paymentOwnMethod = if (paymentTempOwnMethod?.last4 != null) paymentTempOwnMethod else it
        if (paymentOwnMethod != null) {
          observePaymentUpdated.onNext(paymentOwnMethod!!)
          emitter.onNext(paymentOwnMethod!!)
        } else {
          emitter.onNext(true)
        }
      }
    }
  }

  /**
   * add the item of cart
   */
  fun addItemCart(item: Cart, itemIndex: Int) {
    // if not in member is not operation
    if (item.ownerId != null && item.ownerId != BaseApplication.mDeviceId) {
      return
    }

    // reward check
    if (item?.reward == true) {
      // only allow select one reward food.
      if (useCase.validReward(restaurantCart, restaurant, item.bundleRestId) != null) {
        val restaurantName = item.bundleRestName?.localize() ?: ""
        val checkoutOption = CheckoutOption(
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.reward_limit_alert, restaurantName
          )
        )
        observeError.onNext(checkoutOption)
        return
      }

      // check cart and owner point is not enough if need
      if (useCase.checkCardPoint(restaurantCart, restaurant, item) != null) {
        val checkoutOption = CheckoutOption(message = ResourcesUtil.getString(com.ricepo.style.R.string.balance_alert))
        observeError.onNext(checkoutOption)
        return
      }
    }

    // check the limit of food
    item?.limit?.let {
      if (item.reward != true && (item.qty ?: 0) >= it) {
        val checkoutOption = CheckoutOption(
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.alert_food_limit,
            item.name.localize(), it
          )
        )
        observeError.onNext(checkoutOption)
        return
      }
    }

    // check the total section if over the limit of category
    item.categoryMaxItem?.let {
      val count = useCase.getCategoryCount(
        item.categoryId,
        restaurantCart, restaurant
      )
      if (count >= it) {
        val message = if (it > 1) {
          ResourcesUtil.getString(com.ricepo.style.R.string.alert_category_limits, it)
        } else {
          ResourcesUtil.getString(com.ricepo.style.R.string.alert_category_limit, it)
        }
        val checkoutOption = CheckoutOption(message = message)
        observeError.onNext(checkoutOption)
        return
      }
    }

    val rc = useCase.addItemCart(item, itemIndex, restaurantCart)
    if (rc != null) {
      restaurantCart = rc
      observeAddCart.onNext(rc)
    }
  }

  /**
   * remove the item of cart
   */
  fun removeItemCart(item: Cart, itemIndex: Int) {
    // if not in member is not operation
    if (item.ownerId != null && item.ownerId != BaseApplication.mDeviceId) {
      return
    }

    val rc = useCase.removeItemCart(item, itemIndex, restaurantCart)
    if (rc != null) {
      restaurantCart = rc
      observeRemoveCart.onNext(rc)
    }
  }

  /**
   * update item for edit options
   */
  fun updateItemCart(originalFood: Food?, food: Food?) {
    val originalFood = originalFood ?: return
    val food = food ?: return
    val mapper = MenuMapper()
    val originalCart = mapper.mapCart(originalFood, restaurant?.name)
    val cart = mapper.mapCart(food, restaurant?.name)
    val rc = useCase.updateItemCart(originalCart, cart, 0, restaurantCart)
    if (rc != null) {
      restaurantCart = rc
      // check che minimum
      observeRemoveCart.onNext(rc)
    }
  }

  /**
   * remove the item of promotion
   */
  private fun observeRemovePromotion(
    input: Input,
    observeRemoveCart: PublishSubject<RestaurantCart>,
    observeGroupOrderUpdate: PublishSubject<Boolean>
  ):
    Observable<RestaurantCart> {
    return observeRemoveCart.flatMap { restaurantCart ->
      Observable.create { emitter ->
        emitter.onNext(restaurantCart)
        viewModelScope.launch {
          val isGroupExist = withContext(Dispatchers.IO) {
            GroupOrderCache.isGroupExist()
          }
          if (isGroupExist) {
            // after update group order to check
            observeGroupOrderUpdate.filter { it }
              .subscribe {
                checkOwnerMinimum(input, restaurantCart)
              }
          } else {
            checkOwnerMinimum(input, restaurantCart)
          }
        }
      }
    }
  }

  private fun checkOwnerMinimum(input: Input, restaurantCart: RestaurantCart) {
    viewModelScope.launch {
      val promotionCarts = withContext(Dispatchers.IO) {
        useCase.checkMinimum(restaurantCart)
      }
      val isOwner = restaurantCart.restaurant?.id?.let {
        withContext(Dispatchers.IO) {
          GroupOrderCache.isOwner(it)
        }
      }
      if (isOwner == true && promotionCarts?.isNotEmpty() == true) {
        Loading.hideLoading()
        val cart = promotionCarts.last()
        val minimum = cart.minimum ?: return@launch
        val name = cart.name.localize()
        val price = mapper.toTotalPrice(minimum, restaurantCart?.restaurant)
        val message = if (LocaleUtil.getResourcesLanguage() == LocaleConst.ENGLISH) {
          ResourcesUtil.getString(com.ricepo.style.R.string.menu_promotion_remove, name, price)
        } else {
          ResourcesUtil.getString(com.ricepo.style.R.string.menu_promotion_remove, name, price, price)
        }
        val itemIndex = restaurantCart.cartList?.indexOf(cart) ?: 0
        DialogFacade.showAlert(input.context, message) {
          removeItemCart(cart, itemIndex)
        }
      }
    }
  }

  /**
   * edit the food options
   */
  fun editOption(context: Context, cart: Cart, position: Int) {
    val restaurant = restaurant ?: return

    viewModelScope.launch {
      getFoodFromServer(context, cart)
        ?.collectLatest { food ->
          FeaturePageRouter.navigateOptions(
            context, food, restaurant,
            position, null, null
          )
        }
    }
  }

  /**
   * get food by food id for options edit
   */
  private fun getFoodFromServer(context: Context, cart: Cart?): Flow<Food>? {
    if (context is Activity) else return null
    val restaurantId = cart?.bundleRestId ?: restaurant?.id ?: return null
    val foodId = cart?.id ?: return null

    return flowLoading(
      context,
      { _ ->
        DialogFacade.showAlert(context, ResourcesUtil.getString(com.ricepo.style.R.string.checkout_options_edit_error))
      }
    ) {

      val food = combineApi.getFood(restaurantId, foodId)
      // original food opt for remove item in cart
      food?.opt = cart?.opt?.toMutableList()
      // set food default qty
      useCase.setFoodOptionsSelected(cart, food)
      food?.let { emit(it) }
    }
  }

  /**
   * case1: order coupon failed
   */
  fun updateCoupon(coupon: Coupon?) {
    selectedCoupon = coupon
    isCouponRemoved = false
    if (coupon != null) {
      observeCoupon.onNext(true)
      reloadQuote()
    } else {
      observeCoupon.onNext(false)
    }
  }

  private fun removeCoupon() {
    updateCoupon(null)
    isCouponRemoved = true
    reloadQuote()
  }

  fun changeToPickup() {
    deliveryMode = DeliveryMethod.pickup
    observeDeliverMethod.onNext(deliveryMode)
    // clear the window
    quoteTimeWindow = null
  }

  fun changeToDelivery() {
    deliveryMode = DeliveryMethod.delivery
    observeDeliverMethod.onNext(deliveryMode)
    // clear the window
    quoteTimeWindow = null
  }

  fun updateTempPayment(payment: PaymentOwnMethod?) {
    paymentTempOwnMethod = payment
  }

  fun cleanTempPayment() {
    paymentTempOwnMethod = null
  }

  fun saveTempPaymentCard() {
    val payment = paymentTempOwnMethod
    if (payment != null) {
      CustomerCache.savePayment(payment) {}
    }
  }

  /**
   * check the payment method is valid
   */
  fun isValidPaymentMethod(payment: PaymentOwnMethod?): Boolean {
    return if (payment?.method == PaymentOwnMethod.CREDIT) {
      !payment.last4.isNullOrEmpty() || !payment.stripeId.isNullOrEmpty()
    } else {
      payment?.method != null
    }
  }

  fun getDeliveryTime(estimate: Estimate?): String {
    return "${(estimate?.min ?: 0.0).toInt()} - ${(estimate?.max ?: 0.0).toInt()} min"
  }

  fun setDeliveryTickIndex(express: ExpressDelivery): Int {
    deliveryEstimate = express.selected
    val tickIndex = express?.options?.indexOf(deliveryEstimate) ?: 0
    if (tickIndex == 0) {
      deliveryEstimate = express?.options?.firstOrNull()
    }
    return tickIndex
  }

  private val disposables = CompositeDisposable()

  override fun onCleared() {
    super.onCleared()
    useCase.dispose()
    disposables.dispose()
  }
}
