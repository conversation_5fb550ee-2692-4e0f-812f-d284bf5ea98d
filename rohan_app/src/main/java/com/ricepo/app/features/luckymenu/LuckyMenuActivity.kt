package com.ricepo.app.features.luckymenu

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.databinding.ActivityLuckyMenuBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.luckymenu.view.FrameAnimation
import com.ricepo.app.features.luckymenu.view.FrameAnimationUtils
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger

//
// Created by <PERSON> on 11/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_LUCKY)
class LuckyMenuActivity : BaseActivity() {

  private lateinit var binding: ActivityLuckyMenuBinding
  private lateinit var animation: FrameAnimation
  private var num = 1

  companion object {
    const val MIN = 1
    const val MAX = 8
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityLuckyMenuBinding.inflate(layoutInflater)
    setContentView(binding.root)
    // dice animation
    animation = FrameAnimation(binding.ivDice, FrameAnimationUtils.getRes(), 20, true)
    animation.play(0)
    setupListener()
  }

  private fun setupListener() {
    binding.ivClose.clickWithTrigger {
      finish()
    }

    binding.btMinus.setOnClickListener {
      num--
      binding.tvPeopleNum.apply {
        if (num >= MIN) text = num.toString() else num = MIN
      }
    }

    binding.btPlus.setOnClickListener {
      num++
      binding.tvPeopleNum.apply {
        if (num <= MAX) text = num.toString() else num = MAX
      }
    }

    binding.tvGenerateMenu.clickWithTrigger {
      FeaturePageRouter.navigateLuckyRecommendMenu(binding.tvPeopleNum.text.toString())
    }
  }
}
