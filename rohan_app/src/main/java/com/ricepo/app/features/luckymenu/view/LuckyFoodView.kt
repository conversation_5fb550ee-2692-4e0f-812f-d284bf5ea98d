package com.ricepo.app.features.luckymenu.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import com.ricepo.app.R
import com.ricepo.app.databinding.LayoutLuckyFoodBinding
import com.ricepo.app.features.menu.preview.MenuMarketPreviewActivity
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.style.ResourcesUtil
import kotlin.math.abs

//
// Created by <PERSON> on 20/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LuckyFoodView : ConstraintLayout {

  constructor(context: Context) : super(context) {
    init(context)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs)
    }

  private lateinit var binding: LayoutLuckyFoodBinding

  private fun init(context: Context, attrs: AttributeSet? = null) {
    binding = LayoutLuckyFoodBinding.inflate(
      LayoutInflater.from(context),
      this, true
    )
  }

  var mid: String = ""

  var mFood: Food? = null

  private val mapper = BaseMapper()

  fun setFoodView(restaurant: Restaurant?, food: Food, index: Int) {
    RestViewUtils.setFoodImage(food.image, restaurant, binding.ivFood, binding.ivFoodBg)
    binding.ivFood.setOnTouchListener { v, event ->
      touchLongAndClickListener(binding.root, event, food, restaurant)
    }

    binding.tvFoodName.text = food.name?.localize()
    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(food.price, restaurant)

    var width = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_21dp)
    var height = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp)

    if (index != 2 && index != 3) {
      // the biggest food
      binding.tvFoodName.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_15sp)
      )
      binding.tvFoodPrice.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_15sp)
      )
      width = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_32dp)
      height = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_34dp)
    } else {
      binding.tvFoodName.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_13sp)
      )
      binding.tvFoodPrice.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_13sp)
      )
    }
    val params = binding.btnFoodOption.layoutParams
    if (params is ConstraintLayout.LayoutParams) {
      params.width = width
      params.height = height
      binding.btnFoodOption.layoutParams = params
    }

    binding.btnFoodOption.setImageResource(com.ricepo.style.R.drawable.button_not_check)
    setRandomParams(food, index)
  }

  fun setSelectedView(selected: Boolean) {
    if (selected) {
      binding.btnFoodOption.setImageResource(com.ricepo.style.R.drawable.button_red_check)
      binding.tvFoodName.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.luckyMenuSelected, binding.root))
      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.luckyMenuSelected, binding.root))
    } else {
      binding.btnFoodOption.setImageResource(com.ricepo.style.R.drawable.button_not_check)

      binding.tvFoodName.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))
      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))
    }
  }

  fun setRefreshView(refreshResId: Int) {
    binding.root.setBackgroundResource(refreshResId)
    binding.ivFoodBg.visibility = View.GONE
  }

  private fun setRandomParams(food: Food, index: Int) {
    val plateSize = randomPlateSize(index)
    val plateParams = binding.ivFoodBg.layoutParams
    plateParams.width = plateSize
    plateParams.height = (plateSize * 1.3).toInt()
    binding.ivFoodBg.layoutParams = plateParams

    val foodParams = binding.ivFood.layoutParams
    foodParams.width = plateSize
    foodParams.height = plateSize
    binding.ivFood.layoutParams = foodParams

    var nameSize = if (index != 2 && index != 3) {
      ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_90dp)
    } else {
      ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_77dp)
    }
    if (food.name.localize().length < 3) {
      nameSize = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.sw_50dp)
    }
    val nameParams = binding.tvFoodName.layoutParams as ConstraintLayout.LayoutParams
    nameParams.width = nameSize
    binding.tvFoodName.layoutParams = nameParams

    // middle margin
    val size = plateSize + nameSize + nameParams.leftMargin
    // RandomLayout addViewAtRandomXY2 for view.measure ConstraintLayout
    val params = ConstraintLayout.LayoutParams(
      size,
      ConstraintLayout.LayoutParams.WRAP_CONTENT
    )
    binding.root.layoutParams = params
  }

  private fun randomPlateSize(index: Int): Int {
//        val min = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_56)
//        val max = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_100)
//        return Random.nextInt(min, max)
    return when (index) {
      2 -> {
//                DisplayUtil.dp2PxOffset(77f)
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_77dp)
      }
      3 -> {
//                DisplayUtil.dp2PxOffset(50f)
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_55dp)
      }
      else -> {
//                DisplayUtil.dp2PxOffset(120f)
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_120dp)
      }
    }
  }

  var clickMenu: (food: Food?) -> Unit = {}

  private var startX = 0f
  private var startY = 0f
  private var downLong: Long? = null

  private val mHandler = Handler()

  private fun touchLongAndClickListener(
    rootView: View,
    event: MotionEvent,
    food: Food,
    restaurant: Restaurant?
  ): Boolean {
    if (event.action == MotionEvent.ACTION_DOWN) {
      downLong = System.currentTimeMillis()
      startX = event.x
      startY = event.y
      mHandler.removeCallbacksAndMessages(null)
      mHandler.postDelayed(
        {
          downLong = null
          transitionPreview(rootView, food, restaurant)
        },
        1000
      )
    }
    if (event.action == MotionEvent.ACTION_MOVE) {
      if (abs(event.x - startX) > 5 || abs(event.y - startY) > 5) {
        mHandler.removeCallbacksAndMessages(null)
      } else {
        // no-op
      }
    }
    if (event.action == MotionEvent.ACTION_CANCEL) {
      mHandler.removeCallbacksAndMessages(null)
    }
    if (event.action == MotionEvent.ACTION_UP) {
      mHandler.removeCallbacksAndMessages(null)
      if (downLong != null) {
        clickMenu(mFood)
      }
    }
    return true
  }

  private fun transitionPreview(rootView: View, food: Food, restaurant: Restaurant?): Boolean {
    val context = rootView.context ?: return false
    if (context is Activity) else return false
    food.image?.url ?: return false

    if (food.image?.placeholder == FoodImage.MARKET) {
      transitionToMarket(context, food, restaurant, rootView)
    } else {
      transitionToNormal(context, food, restaurant, rootView)
    }
    return true
  }

  private fun transitionToNormal(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    rootView: View
  ) {

    val ivFood = rootView.findViewById<View?>(R.id.iv_food) ?: return
    val ivFoodBg = rootView.findViewById<View?>(R.id.iv_food_bg)

    val intent = Intent(context, MenuNormalPreviewActivity::class.java)

    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        ivFood,
        MenuNormalPreviewActivity.VIEW_MENU_IMAGE
      ),
      Pair(
        ivFoodBg,
        MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }

  private fun transitionToMarket(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    rootView: View
  ) {
    val ivFood = rootView.findViewById<View?>(R.id.iv_food) ?: return

    val intent = Intent(context, MenuMarketPreviewActivity::class.java)

    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        ivFood,
        MenuMarketPreviewActivity.VIEW_MENU_IMAGE
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }

//    private val mBgPaint: Paint = Paint()
//    var pfd = PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)
//
//    var mid: String = ""
//    var food: Food? = null
//    private var radius: Double
//
//    init {
//        setLayerType(View.LAYER_TYPE_SOFTWARE, null)
//        mBgPaint.color = Color.WHITE
//        mBgPaint.isAntiAlias = true
//        radius = (30 + Math.random() * 31)
//    }
//
//    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        Log.d("LuckyFoodView", "onMeasure")
//        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
//        val measuredWidth = measuredWidth
//        val measuredHeight = measuredHeight
//        val max = measuredWidth.coerceAtLeast(measuredHeight)
//        setMeasuredDimension(max, max)
//    }
//
//    override fun setBackgroundColor(color: Int) {
//        mBgPaint.color = color
//    }
//
//    override fun setBackgroundResource(resId: Int) {
//        super.setBackgroundResource(resId)
//        // solved octagon when set image background on samsung a20
//        radius = 0.0
//    }
//
//    override fun draw(canvas: Canvas) {
//        Log.d("LuckyFoodView", "draw")
//        canvas.drawFilter = pfd
//        canvas.drawCircle(
//            (width / 2).toFloat(),
//            (height / 2).toFloat(),
//            radius.toFloat(),
//            mBgPaint
//        )
//
//        super.draw(canvas)
//    }
}
