package com.ricepo.app.features.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentEditEmailBinding
import com.ricepo.app.utils.flow
import com.ricepo.app.utils.hideLoading
import com.ricepo.app.utils.showLoading
import com.ricepo.base.model.Customer
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment
import com.ricepo.style.view.animateFade
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class EmailEditFragment(
  val onSuccess: (() -> Unit)? = null,
  val onError: (() -> Unit)? = null,
  val customer: Customer? = null
) : RoundedBottomSheetDialogFragment() {

  @Inject
  lateinit var emailViewModelFactory: EditEmailViewModel.AssistedFactory

  private val editEmailViewModel: EditEmailViewModel by viewModels {
    EditEmailViewModel.provideFactory(
      emailViewModelFactory,
      customer
    )
  }

  lateinit var binding: FragmentEditEmailBinding

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    if (customer == null) {
      dismiss()
    }
    binding = FragmentEditEmailBinding.inflate(inflater)
    return binding.root.apply {
      post {
        expendDialog("") {}
      }
    }
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    initView()
    observeState()
    editEmailViewModel.combineState(
      binding.etEmail.flow(),
      binding.etCode.flow()
    )
  }

  private fun initView() {
    binding.ivClose.setOnClickListener {
      dismiss()
    }
    binding.submit.setOnClickListener {
      editEmailViewModel.submit(
        onStart = {
          showLoading(binding.root)
        },
        onSuccess = {
          hideLoading(binding.root)
        },
        onError = {
          hideLoading(binding.root)
        }
      )
    }
    binding.tvState.setOnClickListener {
      editEmailViewModel.sendCode(
        onStart = {
          showLoading(binding.root)
        },
        onSuccess = {
          hideLoading(binding.root)
        },
        onError = {
          hideLoading(binding.root)
        }
      )
    }
  }

  private fun observeState() {

    lifecycleScope.launchWhenStarted {

      editEmailViewModel.uiState.collect {
        render(it)
      }
    }
  }

  private fun render(
    uiState: EmailUiState
  ) {
    with(uiState) {
      binding.editEmailLayout.animateFade(isEdit)
      binding.tvEmailDisplay.animateFade(!isEdit)

      binding.tvState.text = when {
        tickTime > 0 -> "${tickTime}s"
        else -> resources.getString(com.ricepo.style.R.string.email_verify_receive)
      }

      if (sendEnable) {
        binding.tvState.setTextColor(resources.getColor(com.ricepo.style.R.color.mr))
      } else {
        binding.tvState.setTextColor(resources.getColor(com.ricepo.style.R.color.subText))
      }

      binding.tvState.isEnabled = sendEnable

      if (isEdit) {
        binding.submit.text = resources.getString(com.ricepo.style.R.string.confirm)
      } else {
        binding.submit.text = resources.getString(com.ricepo.style.R.string.modify)
      }
      binding.submit.isEnabled = submitEnable || !isEdit
      binding.submit.alpha = if (submitEnable || !isEdit) 1f else 0.2f
      binding.tvEmailDisplay.text = succeedEmail ?: ""
    }
  }
}
