package com.ricepo.app.features.subscription

import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.login.repository.AuthUseCase
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Customer
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.annotations.NonNull
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableEmitter
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 16/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class SubscriptionUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val authUseCase: AuthUseCase,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  fun createSubscription(
    subscriptionOption: SubscriptionViewModel.SubscriptionOption,
    planId: String?,
    stripeId: String?
  ):
    Observable<SubscriptionViewModel.SubscriptionOption> {
    return Observable.create<SubscriptionViewModel.SubscriptionOption> { emitter ->
      if (stripeId != null && planId != null) {
        val single = repository.createSubscription(planId, stripeId)
        addDisposable(
          single.subscribeWith(object : DisposableSingleObserver<PaymentObj>() {
            override fun onSuccess(t: PaymentObj) {
              if (t != null) {
                subscriptionOption.paymentObj = t
                emitter.onNext(subscriptionOption)
              }
            }

            override fun onError(e: Throwable) {
              if (e is NetworkError && e.code == ErrorCode.SUBSCRIPTION_EXIST) {
                // subscribe success
                subscriptionOption.paymentObj = PaymentObj(signed = null)
              } else {
                subscriptionOption.message = e?.message
              }
              emitter.onNext(subscriptionOption)
            }
          })
        )
//                Thread.sleep(3000)
//                subscriptionOption.paymentObj = PaymentObj(signed = null)
//                emitter.onNext(subscriptionOption)
      } else {
        emitter.onNext(subscriptionOption)
      }
    }
  }

  fun cancelSubscription(subscriptionId: String?):
    Observable<SubscriptionViewModel.SubscriptionOption> {
    return Observable.create { emitter ->
      if (subscriptionId != null) {
        val single = repository.cancelSubscription(subscriptionId)
        addDisposable(
          single.subscribeWith(object : DisposableSingleObserver<Any>() {
            override fun onSuccess(t: Any) {
              if (t != null) {
                emitter.onNext(SubscriptionViewModel.SubscriptionOption())
              }
            }

            override fun onError(e: Throwable) {
              val option = SubscriptionViewModel.SubscriptionOption(message = e?.message)
              emitter.onNext(option)
            }
          })
        )
      }
    }
  }

  fun updateSubscription(stripeId: String?): Observable<SubscriptionViewModel.SubscriptionOption> {
    return Observable.create<SubscriptionViewModel.SubscriptionOption> { emitter ->
      if (stripeId != null) {
        val customer = CustomerCache.getCustomer()
        val subscriptionId = customer?.subscription?.id
        if (subscriptionId != null) {
          val single = repository.updateSubscriptionPayment(subscriptionId, stripeId)
          addDisposable(
            single.subscribeWith(object : DisposableSingleObserver<Void>() {
              override fun onSuccess(t: Void) {
                updateCustomer(emitter, customer)
              }

              override fun onError(e: Throwable) {
                if (e?.message == null) {
                  updateCustomer(emitter, customer)
                } else {
                  val option = SubscriptionViewModel.SubscriptionOption(message = e?.message)
                  emitter.onNext(option)
                }
              }
            })
          )
        }
      }
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  private fun updateCustomer(
    emitter: @NonNull ObservableEmitter<SubscriptionViewModel.SubscriptionOption>,
    customer: Customer
  ) {
    authUseCase.getCustomer(
      object : DisposableSingleObserver<Customer>() {
        override fun onSuccess(t: Customer) {
          if (t != null) {
            emitter.onNext(SubscriptionViewModel.SubscriptionOption())
          }
        }

        override fun onError(e: Throwable) {
          val option = SubscriptionViewModel.SubscriptionOption(message = e?.message)
          emitter.onNext(option)
        }
      },
      customer.id
    )
  }
}
