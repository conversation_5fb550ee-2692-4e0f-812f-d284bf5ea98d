package com.ricepo.app.features.bannerweb

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.webkit.WebViewClientCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.ActivityBannerWebBinding
import com.ricepo.app.features.BaseWebActivity
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.login.LoginActivity
import com.ricepo.base.model.Banner
import com.ricepo.base.model.Restaurant
import com.ricepo.base.tools.IntentUtils
import com.ricepo.tripartite.wechat.WeChatShare

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_BANNER_WEB)
class BannerWebActivity : BaseWebActivity() {

  lateinit var binding: ActivityBannerWebBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityBannerWebBinding.inflate(layoutInflater)
    setContentView(binding.root)

    val bannerData = intent.getParcelableExtra<Banner?>(FeaturePageConst.PARAM_BANNER_DATA)

    binding.wvBanner.webViewClient = BannerWebViewClient(binding, bannerData, loginLauncher)
    binding.wvBanner.webChromeClient = BannerWebChromeClient(binding)

    binding.wvBanner.settings.cacheMode = WebSettings.LOAD_NO_CACHE

    binding.wvBanner.setBackgroundColor(Color.TRANSPARENT)

    intent.getStringExtra(FeaturePageConst.PARAM_BANNER_URL)?.let {
      binding.wvBanner.loadUrl(it)
    }
  }

  // activity:1.2.0-alpha04 prepareCall renamed to registerForActivityResult
  private val loginLauncher = registerForActivityResult(
    ActivityResultContracts.StartActivityForResult()
  ) { activityResult ->
    if (activityResult.resultCode == Activity.RESULT_OK) {
      finish()
    }
  }

  class BannerWebViewClient internal constructor(
    private val binding: ActivityBannerWebBinding,
    private val banner: Banner?,
    private val loginLauncher: ActivityResultLauncher<Intent>
  ) :
    WebViewClientCompat() {

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
      super.onPageStarted(view, url, favicon)
//            binding.wpBanner.setColor(com.ricepo.style.R.color.colorAccent)
//            binding.wpBanner.show()
    }

    override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
//            view.loadUrl(request.url.toString())
      dispatchUrlLoading(view, request.url)
      return true
    }

    override fun onPageFinished(view: WebView?, url: String?) {
      super.onPageFinished(view, url)
      binding.wpBanner.hide()
    }

    private fun dispatchUrlLoading(view: WebView, uri: Uri) {
      val title = uri.getQueryParameter("title")
      if (!title.isNullOrEmpty()) {
        binding.tvTitle.text = title
      }

      val type = uri.getQueryParameter("type")
      if (type != null) {
        handleUrlWithType(type, uri, view)
        return
      }
      val restaurantId = uri.getQueryParameter("restaurantId")
      if (restaurantId != null) {
        val searchText = uri.getQueryParameter("searchText")
        val restaurant = Restaurant(id = restaurantId)
        val searches = searchText?.let {
          arrayListOf(it)
        }
        FeaturePageRouter.navigateMenu(restaurant, searches)
        return
      }

      val url = uri.toString()
      if (url.startsWith("https://play.google.com") ||
        url.startsWith("https://apps.apple.com")
      ) {
        IntentUtils.intentBrowser(binding.root.context, url)
      } else {
        view.loadUrl(uri.toString())
      }
    }

    private fun handleUrlWithType(type: String, uri: Uri, view: WebView) {
      if (type == "vip") {
        // vip subscription
        handleSubscription(view)
      }
      if (type == "wechat") {
        val userName = uri.getQueryParameter("userName") ?: return
        val path = uri.getQueryParameter("path")
        // ricepo mini program
//                val userName = "gh_f5ee833c8d48"
//                val path = ""
//                val shareImage = BitmapFactory.decodeResource(view.resources, com.ricepo.style.R.drawable.ic_logo_wechat_mini)
        WeChatShare.enterMicroProgram(view.context, userName = userName, path = path)
      }
    }

    private fun handleSubscription(view: WebView) {
      CustomerCache.liveCustomer { customer ->
        if (!customer?.id.isNullOrEmpty()) {
          val subscription = banner?.data?.plan ?: return@liveCustomer
          FeaturePageRouter.navigateSubscription(view.context, subscription, true)
        } else {
          loginLauncher.launch(Intent(view.context, LoginActivity::class.java))
        }
      }
    }
  }

  class BannerWebChromeClient internal constructor(private val binding: ActivityBannerWebBinding) :
    WebChromeClient() {
    override fun onProgressChanged(view: WebView?, newProgress: Int) {
      super.onProgressChanged(view, newProgress)
      binding.wpBanner.setWebProgress(newProgress)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
  }
}
