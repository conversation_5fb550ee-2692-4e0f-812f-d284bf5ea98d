package com.ricepo.app.features.settings

import androidx.activity.ComponentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.FeedbackReq
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 26/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class SettingsViewModel @Inject constructor(
  private val repository: CombineRestApi
) : BaseViewModel() {

  val liveData = MutableLiveData<SettingsOption>()

  var isLogoutSuccess = true

  data class SettingsOption(
    val loginLabel: String? = null,
    val lang: String? = null
  )

  fun initSettings() {
    viewModelScope.launch(Dispatchers.IO) {
      val token = CustomerCache.getToken()
      val lang = LocaleUtil.getLanguage()
      changeSettings(token, lang)
    }
  }

  fun logout() {
    viewModelScope.launch {
      isLogoutSuccess = false
      withContext(Dispatchers.IO) { CustomerCache.clearWithLogoutSuspend() }
      val lang = LocaleUtil.getLanguage()
      changeSettings(null, lang)
      isLogoutSuccess = true
    }
  }

  private fun changeSettings(token: String?, lang: String?) {
    val loginLabel = if (token == null) {
      ResourcesUtil.getString(com.ricepo.style.R.string.login)
    } else {
      ResourcesUtil.getString(com.ricepo.style.R.string.logout)
    }
    liveData.postValue(
      SettingsOption(
        loginLabel,
        lang
      )
    )
  }

  /**
   * submit user feedback issue
   */
  fun submitSubject(
    context: ComponentActivity,
    content: String?,
    phone: String?,
    back: () -> Unit = {}
  ) {
    // check text required and subject is not entered
    if (content.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.feedback_alert_text)
      return
    }
    if (phone.isNullOrEmpty()) {
      DialogFacade.showAlert(context, com.ricepo.style.R.string.feedback_alert_phone)
      return
    }

    viewModelScope.launch {
      createTicket(context, content, phone)
        .collectLatest {
          DialogFacade.showAlert(context, ResourcesUtil.getString(com.ricepo.style.R.string.ticket_received)) {
            back()
          }
        }
    }
  }

  private fun createTicket(
    context: ComponentActivity,
    content: String,
    phone: String
  ): Flow<NetworkError> {
    val req = FeedbackReq(type = "general-feedback", subject = content, phone = phone)
    return flowLoading(
      context,
      error = { e ->
        val message = e.parseByBuzNetwork().message
        DialogFacade.showAlert(context, message)
      }
    ) {
      emit(repository.imageFeedback(req))
    }
  }
}
