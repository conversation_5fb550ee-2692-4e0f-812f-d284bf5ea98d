package com.ricepo.app.features.luckymenu.view

import android.widget.ImageView

//
// Created by <PERSON> on 19/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class FrameAnimation(
  ivDice: ImageView,
  frameRes: IntArray,
  duration: Int,
  isRepeat: Boolean
) {
  private var mIsRepeat = isRepeat

  private var mAnimationListener: AnimationListener? = null

  private var mImageView = ivDice

  private var mFrameRess = frameRes

  /**
   * the playback interval for each frame of animation
   */
  private var mDuration = duration

  /**
   * delay time for the next animation to play
   */
  private var mDelay = 0
  private var isStart = false

  private var mLastFrame = frameRes.size - 1

  private var mNext = false

  private var mPause = false

  private var mCurrentSelect = 0

  private var mCurrentFrame = 0

  private val SELECTED_B = 2

  private val SELECTED_D = 4

  private fun playAndDelay(i: Int) {
    mImageView.postDelayed(
      Runnable {
        if (mPause) {
          if (mPause) {
            mCurrentSelect = SELECTED_B
            mCurrentFrame = i
            return@Runnable
          }
          return@Runnable
        }
        mNext = false
        if (0 == i) {
          if (mAnimationListener != null) {
            mAnimationListener!!.onAnimationStart()
          }
        }
        mImageView.setImageResource(mFrameRess[i])
        if (i == mLastFrame) {
          if (mAnimationListener != null) {
            mAnimationListener!!.onAnimationRepeat()
          }
          mNext = true
          playAndDelay(0)
        } else {
          playAndDelay(i + 1)
        }
      },
      if (mNext && mDelay > 0) mDelay.toLong() else mDuration.toLong()
    )
  }

  fun play(i: Int) {
    isStart = true
    mImageView.postDelayed(
      Runnable {
        if (mPause) {
          if (mPause) {
            mCurrentSelect = SELECTED_D
            mCurrentFrame = i
            return@Runnable
          }
          return@Runnable
        }
        if (0 == i) {
          if (mAnimationListener != null) {
            mAnimationListener!!.onAnimationStart()
          }
        }
        mImageView.setImageResource(mFrameRess[i])
        if (i == mLastFrame) {
          if (mIsRepeat) {
            if (mAnimationListener != null) {
              mAnimationListener!!.onAnimationRepeat()
            }
            play(0)
          } else {
            if (mAnimationListener != null) {
              mAnimationListener!!.onAnimationEnd()
            }
          }
        } else {
          play(i + 1)
        }
      },
      mDuration.toLong()
    )
  }

  interface AnimationListener {
    /**
     *
     * Notifies the start of the animation.
     */
    fun onAnimationStart()

    /**
     *
     * Notifies the end of the animation. This callback is not invoked
     * for animations with repeat count set to INFINITE.
     */
    fun onAnimationEnd()

    /**
     *
     * Notifies the repetition of the animation.
     */
    fun onAnimationRepeat()
  }

  /**
   *
   * Binds an animation listener to this animation. The animation listener
   * is notified of animation events such as the end of the animation or the
   * repetition of the animation.
   *
   * @param listener the animation listener to be notified
   */
  fun setAnimationListener(listener: AnimationListener?) {
    mAnimationListener = listener
  }

  fun release() {
    pauseAnimation()
  }

  fun pauseAnimation() {
    mPause = true
  }

  fun isStarted(): Boolean {
    return isStart
  }

  fun isPause(): Boolean {
    return mPause
  }

  fun restartAnimation() {
    if (mPause) {
      mPause = false
      when (mCurrentSelect) {
        SELECTED_B -> playAndDelay(mCurrentFrame)
        SELECTED_D -> play(mCurrentFrame)
        else -> {
        }
      }
    }
  }
}
