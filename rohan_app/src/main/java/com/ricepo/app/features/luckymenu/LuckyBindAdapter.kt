package com.ricepo.app.features.luckymenu

import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.ricepo.app.features.luckymenu.view.FrameAnimation
import com.ricepo.app.features.luckymenu.view.FrameAnimationUtils

//
// Created by Mark on 11/13/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object LuckyBindAdapter {

  var animation: FrameAnimation? = null
  var isFirstAnimation: Boolean = true

  @JvmStatic
  @BindingAdapter("animation")
  fun bindStartDiceAnimation(view: ImageView, refresh: Boolean) {
    if (animation == null) {
      animation = FrameAnimation(view, FrameAnimationUtils.getRes(), 20, true)
    }
    if (refresh) {
      if (isFirstAnimation) {
        animation?.play(0)
        isFirstAnimation = false
      } else {
        animation?.restartAnimation()
      }
    } else {
      animation?.pauseAnimation()
    }
  }

  fun releaseAnimation() {
    animation = null
    isFirstAnimation = true
  }
}
