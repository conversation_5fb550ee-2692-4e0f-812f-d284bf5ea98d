package com.ricepo.app.features

import com.ricepo.base.BaseEntrance

//
// Created by <PERSON><PERSON> on 1/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object FeaturePageConst {

  /**
   * the entrance of page
   */
  const val PARAM_PAGE_ENTRANCE = BaseEntrance.PARAM_PAGE_ENTRANCE

  /**
   * the current restaurant
   */
  const val PARAM_RESTAURANT = "current_restaurant"

  const val PARAM_CUSTOMER = "current_customer"

  /**
   * App Home page
   */
  const val PAGE_HOME_MAIN = "/app/main"

  const val PARAM_HOME_DYNAMIC_LINK = "home_dynamic_link"

  const val PARAM_HOME_NOTIFICATION = "home_notification"

  const val PARAM_HOME_NOTIFICATION_DATA = "home_notification_data"

  /**
   * Enter from screen page
   */
  const val PARAM_LAUNCH_ENTRANCE = "launch_entrance"

  /**
   * User login page
   */
  const val PAGE_LOGIN = "/app/login"

  const val PARAM_LOGIN_CHAT = "login_chat"

  const val REQUEST_CODE_LOGIN = 0x208

  /**
   * User profile login
   */
  const val PAGE_PROFILE = "/app/profile"

  /**
   * address search page
   */
  const val PAGE_ADDRESS = "/app/address"

  const val REQUEST_CODE_ADDRESS = 0x209

  /**
   * menu page
   * entrance：
   * 1、restaurant list
   * 2、weixin link
   * 3、next order
   */
  const val PAGE_MENU = "/app/menu"

  const val PARAM_MENU_RESTAURANT = "app_menu_restaurant"

  const val PARAM_MENU_RESTAURANT_SEARCH_KEYWORD = "app_menu_restaurant_search_keyword"

  const val PARAM_MENU_GROUP_ID = "app_menu_group_id"

  const val PARAM_DELIVERY_MODE = "app_menu_ship_mode"

  const val PARAM_RESTAURANT_LOCATION = "app_menu_location"

  /**
   * menu options page
   */
  const val PAGE_MENU_OPTIONS = "/app/menu/options"

  const val PARAM_MENU_OPTIONS_FOOD = "menu_options_food"

  const val PARAM_MENU_OPTIONS_POSITION = "menu_options_position"

  const val PARAM_MENU_OPTIONS_FOOD_INDEX = "menu_options_food_index"

  const val PARAM_MENU_OPTIONS_CART = "menu_options_cart"

  const val PARAM_MENU_OPTIONS_FOOD_ORIGINAL = "menu_options_food_original"

  const val REQUEST_CODE_MENU_OPTIONS = 0x300

  /**
   * menu combo page
   */
  const val PAGE_MENU_COMBO = "/app/menu/combo"

  const val PARAM_MENU_COMBO_FOOD = "menu_combo_food"

  const val PARAM_MENU_COMBO_POSITION = "menu_combo_position"

  const val REQUEST_CODE_MENU_COMBO = 0x316

  /**
   * menu feedback page
   */
  const val PAGE_MENU_FEEDBACK = "/app/menu/feedback"

  const val REQUEST_CODE_MENU_FEEDBACK = 0x301

  /**
   * menu submore activity
   */
  const val PAGE_MENU_SUBMORE = "/app/menu/submore"

  const val PARAM_MENU_SUBMORE_CATEGORY = "menu_submore_category"

  const val PARAM_MENU_PROMO_FOODS = "menu_promo_foods"

  const val REQUEST_CODE_MENU_SUBMORE = 0x3001

  /**
   * menu search activity
   */
  const val PAGE_MENU_SEARCH = "/app/menu/search"

  const val PARAM_MENU_SEARCH_BUNDLES = "menu_search_bundles"

  const val PARAM_MENU_CATEGORY_MAXITEM = "menu_category_max_item"

  const val REQUEST_CODE_MENU_SEARCH = 0x3002

  /**
   * settings page
   */
  const val PAGE_SETTINGS = "/app/settings"

  /**
   * checkout page
   */
  const val PAGE_CHECKOUT = "/app/checkout"

  const val REQUEST_CODE_CHECKOUT = 0x302

  /**
   * checkout comment page
   */
  const val PAGE_CHECKOUT_COMMENT = "/app/checkout/comment"

  const val REQUEST_CODE_CHECKOUT_COMMENT = 0x303

  const val PARAM_PAGE_CHECKOUT_COMMENT = "checkout_comments"

  const val PARAM_PAGE_CHECKOUT_RESTAURANT_ID = "checkout_restaurant_id"

  /**
   * checkout tips page
   */
  const val PAGE_CHECKOUT_TIPS = "/app/checkout/tips"

  const val REQUEST_CODE_CHECKOUT_TIPS = 0x304

  const val PARAM_PAGE_CHECKOUT_TIPS_OPTIONS = "checkout_tips_options"

  const val PARAM_PAGE_CHECKOUT_TIPS_PROMPT = "checkout_tips_prompt"

  const val PARAM_PAGE_CHECKOUT_TIPS_RESTAURANT = "checkout_tips_restaurant"

  const val PARAM_PAGE_CHECKOUT_TIPS = "checkout_tips"

  /**
   * checkout tips enter page
   */
  const val PAGE_CHECKOUT_TIPS_ENTER = "/app/checkout/tips/enter"

  const val REQUEST_CODE_CHECKOUT_TIPS_ENTER = 0x305

  const val PARAM_PAGE_CHECKOUT_TIPS_ENTER = "checkout_tips_enter"

  /**
   * coupon page
   */
  const val PAGE_COUPON = "/app/coupon"

  const val REQUEST_CODE_COUPON = 0x306

  const val PARAM_PAGE_COUPON_SUBTOTAL = "coupon_subtotal"

  const val PARAM_PAGE_COUPON_SHOWCASE = "coupon_showcase"

  const val PARAM_PAGE_COUPON_OPTIONS = "coupon_options"

  const val PARAM_PAGE_COUPON_SELECTED = "coupon_selected"

  /**
   * payment page
   */
  const val PAGE_PAYMENT = "/app/payment"

  const val REQUEST_CODE_PAYMENT = 0x307

  const val PARAM_PAGE_PAYMENT_SHOWCASE = "payment_showcase"

  const val PARAM_PAGE_PAYMENT_HANDLE = "payment_handle"

  const val PARAM_PAGE_PAYMENT_ORDER = "payment_order"

  /**
   * payment card page
   */
  const val PAGE_PAYMENT_CARD = "/app/payment/card"

  const val REQUEST_CODE_PAYMENT_CARD = 0x308

  const val PARAM_PAGE_PAYMENT_NEW_CARD = "payment_new_card"

  /**
   * order page
   */
  const val PAGE_ORDER = "/app/order"

  const val PARAM_ORDER = "order"

  const val PARAM_ORDER_ID = "order_id"

  const val REQUEST_CODE_RECENT_ORDER = 0x309

  const val REQUEST_CODE_HISTORY_ORDER = 0x3091

  const val PARAM_PAGE_ORDER_PAYMENT_ERROR = "order_payment_error"

  /**
   * subscription page
   */
  const val PAGE_SUBSCRIPTION = "/app/subscription"

  const val REQUEST_CODE_SUBSCRIPTION = 0x310

  const val PARAM_SUBSCRIPTION_PLAN = "subscription_plan"

  const val PARAM_SUBSCRIPTION_ONLY_SUBSCRIBE = "subscription_only_subscribe"

  const val PARAM_SUBSCRIPTION_PROFILE_SUBSCRIBE = "subscription_profile_subscribe"

  /**
   * subscription update page
   */
  const val PAGE_SUBSCRIPTION_UPDATE = "/app/subscription_update"

  const val PARAM_SUBSCRIPTION_CUSTOMER = "subscription_customer"

  /**
   * driver rating page
   */
  const val PAGE_DRIVER_RATING = "/app/profile/driver_rating"

  const val REQUEST_CODE_DRIVER_RATING = 0x311

  const val REQUEST_CODE_DRIVER_RATING_RESUME = 0x3111

  const val PARAM_PROFILE_ORDER_POSITION = "rating_order_position"

  const val PARAM_PROFILE_HISTORY_ORDER = "rating_order"

  /**
   * order support page
   */
  const val PAGE_ORDER_SUPPORT = "/app/order/support"

  const val REQUEST_CODE_ORDER_SUPPORT = 0x312

  const val PARAM_ORDER_SUPPORT_ORDER = "order_support_order"

  const val PARAM_ORDER_SUPPORT_ITEMS = "order_support_items"

  const val PAGE_ORDER_SUPPORT_ISSUE = "/app/order/support/issue"

  const val PARAM_ORDER_SUPPORT_RULE = "order_support_rule"

  const val PARAM_ORDER_SUPPORT_RULES = "order_support_rules"

  const val PARAM_ORDER_SUPPORT_RULE_ITEM = "order_support_rule_item"

  const val PARAM_ORDER_SUPPORT_RULE_ITEMS = "order_support_rule_items"

  const val REQUEST_CODE_ORDER_SUPPORT_ISSUE = 0x313

  /**
   * order support chat page
   */
  const val PAGE_ORDER_SUPPORT_CHAT = "/app/order/support/chat"

  /**
   * restaurant search page
   */
  const val PAGE_RESTAURANT_SEARCH = "/app/restaurant/search"

  const val PARAM_SEARCH_LOCATION = "search_location"

  const val PARAM_SEARCH_REGION_ID = "search_region_id"

  const val PARAM_SEARCH_MILES = "search_miles"

  /**
   * region search page
   */
  const val PAGE_REGION_EXPLORE = "/app/region/explore"

  /**
   * banner web page
   */
  const val PAGE_BANNER_WEB = "/app/banner/web"

  const val PARAM_BANNER_URL = "banner_url"

  const val PARAM_BANNER_DATA = "banner_data"

  const val PAGE_WEB_REFER = "/app/web/refer"

  const val PARAM_WEB_ORDER = "web_order"

  const val PAGE_HTML_WEB = "/app/html/web"

  const val PARAM_WEB_CONTENT = "web_content"

  /**
   * food recommend ^ rank page
   */
  const val PAGE_RECOMMEND = "/app/recommend"

  const val PARAM_RECOMMEND_TYPE = "recommend_type"

  /**
   * language select page
   */
  const val PAGE_LANGUAGE = "/app/language"

  /**
   * settings my coin page
   */
  const val PAGE_REWARD_SUMMARY = "/app/reward/summary"

  /**
   * restaurant horizontal more and cuisine
   */
  const val PAGE_RESTAURANT_SUBMORE = "/app/restaurant/submore"

  const val PARAM_RESTAURANT_SUBMORE_REQUEST = "restaurant_submore_request"

  const val PARAM_RESTAURANT_SUBMORE_SCROLL_POSITION = "restaurant_submore_scroll_position"

  /**
   * settings user feedback
   */
  const val PAGE_USER_FEEDBACK = "/app/user/feedback"

  /**
   * feeling lucky page
   */
  const val PAGE_LUCKY = "/app/restaurant/lucky"

  /**
   * feeling lucky recommend page
   */
  const val PAGE_LUCKY_RECOMMEND = "/app/restaurant/lucky/recommend"
  const val PAGE_LUCKY_GROUP_SIZE = "group_size"

  /**
   * refer page
   */
  const val PAGE_REFER = "/app/refer"

  const val REQUEST_CODE_REFER = 0x314

  /**
   * refer diary image
   */
  const val PAGE_REFER_DIARY = "/app/refer/diary"

  const val PARAM_DIARY_IMAGE = "diary_image"

  const val PARAM_DIARY_ORDER = "diary_order"

  /**
   * page of ricepo points
   */
  const val PAGE_POINTS_SUMMARY = "/app/points/summary"

  const val PAGE_CHECKOUT_POINTS = "/app/checkout/points"

  const val PARAM_CHECKOUT_QUOTE = "checkout_quote"

  const val PARAM_CHECKOUT_RESTAURANT = "checkout_restaurant"

  const val PARAM_POINTS_VALUE = "checkout_points_value"

  const val REQUEST_CODE_CHECKOUT_POINTS = 0x315
}
