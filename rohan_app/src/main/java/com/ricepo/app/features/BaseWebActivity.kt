package com.ricepo.app.features

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ClipData
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Message
import android.provider.MediaStore
import android.util.Log
import android.webkit.JsPromptResult
import android.webkit.JsResult
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.webkit.SafeBrowsingResponseCompat
import androidx.webkit.WebResourceErrorCompat
import androidx.webkit.WebViewClientCompat
import com.ricepo.base.BaseActivity
import com.ricepo.base.tools.IntentUtils
import java.io.File
import java.io.IOException
import java.lang.Exception
import java.text.SimpleDateFormat

//
// Created by Thomsen on 13/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class BaseWebActivity : BaseActivity() {

  private var webView: WebView? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
  }

  override fun onWindowFocusChanged(hasFocus: Boolean) {
    super.onWindowFocusChanged(hasFocus)
//        if (hasFocus && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            val window = window
//            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
//            window.statusBarColor = Color.TRANSPARENT
//            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//        }
  }

  override fun onResume() {
    super.onResume()
    webView?.onResume()
  }

  override fun onPause() {
    super.onPause()
    webView?.onPause()
  }

  override fun onDestroy() {
    webView?.destroy()
    super.onDestroy()
  }

  open fun setupWebView(wv: WebView) {
    webView = wv

    webView?.alpha = 0f

    webView?.apply {
      settings.apply {
        // enable javascript
        javaScriptEnabled = true
        allowContentAccess = true
        // file chooser
        allowFileAccess = true

        setSupportZoom(true)
        builtInZoomControls = true
        displayZoomControls = false

        // adapter screen wide
        loadWithOverviewMode = true
        useWideViewPort = true

        domStorageEnabled = true
      }
    }

    webView?.webChromeClient = CustomWebChromeClient(this)
    webView?.webViewClient = CustomWebViewClient(this)
  }

  private class CustomWebViewClient internal constructor(private val activity: BaseWebActivity) : WebViewClientCompat() {

    val TAG = "WebViewClient"

    override fun onPageCommitVisible(view: WebView, url: String) {
      super.onPageCommitVisible(view, url)
      Log.d(TAG, "onPageCommitVisible: $url")
      view?.alpha = 1.0f
    }

    override fun onReceivedError(
      view: WebView,
      request: WebResourceRequest,
      error: WebResourceErrorCompat
    ) {
      super.onReceivedError(view, request, error)
      Log.d(TAG, "onReceivedError: ${error.description}")
    }

    override fun onReceivedHttpError(
      view: WebView,
      request: WebResourceRequest,
      errorResponse: WebResourceResponse
    ) {
      super.onReceivedHttpError(view, request, errorResponse)
      Log.d(TAG, "onReceivedHttpError: ${errorResponse.statusCode}")
    }

    override fun onSafeBrowsingHit(
      view: WebView,
      request: WebResourceRequest,
      threatType: Int,
      callback: SafeBrowsingResponseCompat
    ) {
      super.onSafeBrowsingHit(view, request, threatType, callback)
      Log.d(TAG, "onSafeBrowsingHit: $threatType")
    }

    override fun shouldOverrideUrlLoading(view: WebView?, url: String): Boolean {
      Log.d(TAG, "shouldOverrideUrlLoading: $url")
      return false
    }

    override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
      super.doUpdateVisitedHistory(view, url, isReload)
      Log.d(TAG, "doUpdateVisitedHistory: $activity.webView.url")
    }
  }

  var camFileData: String? = null

  var imageFileUri: Uri? = null
  var imageFilePath: String? = null

  private inner class CustomWebChromeClient constructor(private val activity: BaseWebActivity) : WebChromeClient() {

    override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
      return false
    }

    override fun onJsPrompt(
      view: WebView?,
      url: String?,
      message: String?,
      defaultValue: String?,
      result: JsPromptResult?
    ): Boolean {
      return false
    }

    override fun onCreateWindow(
      view: WebView?,
      isDialog: Boolean,
      isUserGesture: Boolean,
      resultMsg: Message?
    ): Boolean {
      view ?: return false

      val href = view.handler.obtainMessage()
      view.requestFocusNodeHref(href)
      val url = href.data.getString("url")

      view.stopLoading()
      val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
      activity.startActivity(browserIntent)
      return true
    }

    override fun onShowFileChooser(
      webView: WebView?,
      filePathCallback: ValueCallback<Array<Uri>>?,
      fileChooserParams: FileChooserParams
    ): Boolean {
      return showFileChooser(filePathCallback, fileChooserParams)
    }

    fun showFileChooser(
      filePathCallback: ValueCallback<Array<Uri>>?,
      fileChooserParams: WebChromeClient.FileChooserParams
    ): Boolean {
      return if (checkPermission() && Build.VERSION.SDK_INT >= 21) {
        filePath = filePathCallback
        var takePictureIntent: Intent? = null
        var takeVideoIntent: Intent? = null
        var includeVideo = false
        var includePhoto = false

        /*-- checking the accept parameter to determine which intent(s) to include --*/
        paramCheck@ for (acceptTypes in fileChooserParams.acceptTypes) {
          val splitTypes = acceptTypes.split(", ?+".toRegex())
            .toTypedArray() // although it's an array, it still seems to be the whole value; split it out into chunks so that we can detect multiple values
          for (acceptType in splitTypes) {
            when (acceptType) {
              "*/*" -> {
                includePhoto = true
                includeVideo = true
                break@paramCheck
              }
              "image/*" -> includePhoto = true
              "video/*" -> includeVideo = true
            }
          }
        }
        // no `accept` parameter was specified, allow both photo and video
        if (fileChooserParams.acceptTypes.size === 0) {
          includePhoto = true
          includeVideo = true
        }
        if (includePhoto) {
          val intents = IntentUtils.getImageIntent(activity)
          takePictureIntent = intents?.first
          imageFileUri = intents?.second
          imageFilePath = intents?.third
        }
//                if (includeVideo) {
//                    takeVideoIntent = Intent(MediaStore.ACTION_VIDEO_CAPTURE)
//                    if (takeVideoIntent.resolveActivity(activity.getPackageManager()) != null) {
//                        var videoFile: File? = null
//                        try {
//                            videoFile = createVideo()
//                        } catch (ex: IOException) {
//                            Log.e("thom", "Video file creation failed", ex)
//                        }
//                        if (videoFile != null) {
//                            camFileData = "file:" + videoFile.getAbsolutePath()
//                            takeVideoIntent.putExtra(
//                                MediaStore.EXTRA_OUTPUT,
//                                Uri.fromFile(videoFile)
//                            )
//                        } else {
//                            camFileData = null
//                            takeVideoIntent = null
//                        }
//                    }
//                }
        triggerChooser(takePictureIntent, takeVideoIntent)
        // Duplicate showFileChooser result if return false
        true
      } else {
        false
      }
    }

    /*-- checking and asking for required file permissions --*/
    open fun checkPermission(): Boolean {
      return if (Build.VERSION.SDK_INT >= 23 && (
        ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
          ) !== PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.CAMERA
          ) !== PackageManager.PERMISSION_GRANTED
        )
      ) {
        ActivityCompat.requestPermissions(
          activity,
          arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.CAMERA),
          1
        )
        false
      } else {
        true
      }
    }

    /*-- creating new video file here --*/
    @Throws(IOException::class)
    private fun createVideo(): File? {
      @SuppressLint("SimpleDateFormat") val fileName: String =
        SimpleDateFormat("yyyy_mm_ss").format(java.util.Date())
      val newName = "file_" + fileName + "_"
      val sdDir: File? = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
      return File.createTempFile(newName, ".3gp", sdDir)
    }
  }

  // data/header received after file selection
  private val fileData: ValueCallback<Uri>? = null
  // received file(s) temp. location
  private var filePath: ValueCallback<Array<Uri>>? = null

  fun triggerChooser(takePictureIntent: Intent?, takeVideoIntent: Intent?) {
    val contentSelectionIntent = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
      val intent = Intent(Intent.ACTION_GET_CONTENT)
      intent.type = "image/*"
      intent.addCategory(Intent.CATEGORY_OPENABLE)
      intent
    } else {
      val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
      intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*")
      intent
    }

    // multiple files flag
//        contentSelectionIntent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)

    val intentArray: Array<Intent?> = if (takePictureIntent != null && takeVideoIntent != null) {
      arrayOf(takePictureIntent, takeVideoIntent)
    } else takePictureIntent?.let { arrayOf(it) }
      ?: (takeVideoIntent?.let { arrayOf(it) } ?: arrayOfNulls(0))

    val chooserIntent = Intent(Intent.ACTION_CHOOSER)
    chooserIntent.putExtra(Intent.EXTRA_INTENT, contentSelectionIntent)
    chooserIntent.putExtra(Intent.EXTRA_TITLE, "File chooser")
    chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, intentArray)
    imageLaunder.launch(chooserIntent)
  }

  private val imageLaunder = registerForActivityResult(
    ActivityResultContracts.StartActivityForResult(),
    ActivityResultCallback { activityResult ->

      val resultCode = activityResult.resultCode
      val intent = activityResult.data

      var results: Array<Uri>? = null

      /*-- continue if response is positive --*/
      if (resultCode == Activity.RESULT_OK) {
        if (null == filePath) {
          return@ActivityResultCallback
        }

        var clipData: ClipData? = null
        var uriData: Uri? = null
        try {
          clipData = intent?.clipData
          uriData = intent?.data
        } catch (e: Exception) {
          e.printStackTrace()
          clipData = null
          uriData = null
        }

        val imageUri = imageFileUri
        if (clipData == null && uriData == null &&
          (imageUri != null || imageFilePath != null)
        ) {
          val mediaPath = IntentUtils.parseImagePath(
            this@BaseWebActivity,
            imageUri, imageFilePath
          )
          results = arrayOf(Uri.fromFile(File(mediaPath)))
        } else {
          if (clipData != null) { // checking if multiple files selected or not
            val numSelectedFiles = clipData.itemCount
            results = Array(numSelectedFiles) { Uri.EMPTY }
            for (i in 0 until numSelectedFiles) {
              results[i] = clipData.getItemAt(i).uri
            }
          } else if (uriData != null) {
            // content://
            results = arrayOf(uriData)
          }
        }
      }
      filePath?.onReceiveValue(results)
      filePath = null
    }
  )
}
