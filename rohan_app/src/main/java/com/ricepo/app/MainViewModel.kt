package com.ricepo.app

import androidx.lifecycle.ViewModel
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

data class HomeTabUi(
  val title: String,
  val images: Pair<Int, Int>,
  val selected: <PERSON>olean
) {
  companion object {
    fun default() = HomeTabUi(
      title = ResourcesUtil.getString(com.ricepo.style.R.string.mode_switch_popup_title),
      images = Pair(com.ricepo.style.R.drawable.ic_on_demand_pressed, com.ricepo.style.R.drawable.ic_on_demand),
      selected = true
    )
  }
  fun getCurrentImage() =
    if (selected) images.first else images.second
}

fun RestaurantTab.toUi(
  isOuterSelected: Boolean = true
): HomeTabUi {
  return HomeTabUi(
    title = name?.localize() ?: "",
    images = when (type) {
      RestaurantTab.TYPE_SCHEDULED -> {
        Pair(com.ricepo.style.R.drawable.ic_schedule_pressed, com.ricepo.style.R.drawable.ic_schedule)
      }
      RestaurantTab.TYPE_PICKUP -> {
        Pair(com.ricepo.style.R.drawable.ic_pickup_pressed, com.ricepo.style.R.drawable.ic_pickup)
      }
      else -> {
        Pair(com.ricepo.style.R.drawable.ic_on_demand_pressed, com.ricepo.style.R.drawable.ic_on_demand)
      }
    },
    selected = isOuterSelected && selected ?: false
  )
}

sealed class HomeTab(
  open val isSelected: Boolean,
) {
  fun toUi(): HomeTabUi = when (this) {
    is Home -> {
      innerTabs?.firstOrNull {
        it.selected == true
      }?.toUi(
        isOuterSelected = isSelected
      ) ?: HomeTabUi.default()
    }
    is Order -> {
      HomeTabUi(
        title = ResourcesUtil.getString(com.ricepo.style.R.string.tab_orders),
        images = Pair(com.ricepo.style.R.drawable.ic_order_select, com.ricepo.style.R.drawable.ic_order),
        selected = isSelected
      )
    }
    is Wallet -> {
      HomeTabUi(
        title = ResourcesUtil.getString(com.ricepo.style.R.string.tab_coupon),
        images = Pair(com.ricepo.style.R.drawable.ic_wallet_select, com.ricepo.style.R.drawable.ic_wallet),
        selected = isSelected
      )
    }
  }

  open val innerSelectedType: String? = null

  data class Home(
    val innerTabs: List<RestaurantTab>? = null,
    override val isSelected: Boolean = true,
  ) : HomeTab(isSelected = isSelected) {
    override val innerSelectedType: String?
      get() = innerTabs?.filter {
        it.selected == true
      }?.firstOrNull()?.type
  }

  data class Order(
    override val isSelected: Boolean = false,
  ) : HomeTab(isSelected = isSelected)

  data class Wallet(
    override val isSelected: Boolean = false,
  ) : HomeTab(isSelected = isSelected)
}

@HiltViewModel
class MainViewModel @Inject constructor() : ViewModel() {

  private val _showTab = MutableStateFlow(true)
  val showTab = _showTab.asStateFlow()

  private val _tabs = MutableStateFlow(
    Triple(HomeTab.Home(), HomeTab.Order(), HomeTab.Wallet())
  )
  val tabs = _tabs.asStateFlow()

  fun changeShowTab(showTab: Boolean) {
    _showTab.value = showTab
  }

  fun refreshTabs(
    tabs: List<RestaurantTab>?
  ) {
    refreshTabs(
      HomeTab.Home(
        innerTabs = tabs,
        isSelected = tabs?.first()?.selected ?: true
      )
    )
  }

  fun refreshTabs(
    tab: HomeTab,
  ) {
    changeShowTab(true)
    when (tab) {
      is HomeTab.Home -> {
        _tabs.value = with(_tabs.value) {
          Triple(
            first = tab.copy(isSelected = true),
            second = second.copy(isSelected = false),
            third = third.copy(isSelected = false)
          )
        }
      }
      is HomeTab.Order -> {
        _tabs.value = with(_tabs.value) {
          Triple(
            first = first.copy(isSelected = false),
            second = tab.copy(isSelected = true),
            third = third.copy(isSelected = false),
          )
        }
      }
      is HomeTab.Wallet -> {
        _tabs.value = with(_tabs.value) {
          Triple(
            first = first.copy(isSelected = false),
            second = second.copy(isSelected = false),
            third = tab.copy(isSelected = true),
          )
        }
      }
    }
  }
}
