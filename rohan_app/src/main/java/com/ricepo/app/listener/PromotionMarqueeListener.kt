package com.ricepo.app.listener

import android.graphics.Rect
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R

//
// Created by <PERSON><PERSON> on 29/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PromotionMarqueeListener(private val isNested: Boolean = false) : RecyclerView.OnScrollListener() {

  override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
    super.onScrollStateChanged(recyclerView, newState)
    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
      if (isNested) {
        setNestedPromotionMarquee(recyclerView)
      } else {
        setPromotionMarquee(recyclerView)
      }
    }
  }

  override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
    super.onScrolled(recyclerView, dx, dy)
    if (isNested) {
      setNestedPromotionMarquee(recyclerView)
    } else {
      setPromotionMarquee(recyclerView)
    }
  }

  companion object {
    /**
     * set the promotion auto scroll marquee
     */
    fun setPromotionMarquee(recyclerView: RecyclerView) {
      val layoutManager = recyclerView.layoutManager
      if (layoutManager is LinearLayoutManager) {
        val firstPosition = layoutManager.findFirstVisibleItemPosition()
        val lastPosition = layoutManager.findLastVisibleItemPosition()
//                setMarquee(layoutManager, firstPosition, false)
//                setMarquee(layoutManager, lastPosition, false)

//                val firstCompletelyPosition = layoutManager.findFirstCompletelyVisibleItemPosition()
//                val lastCompletelyPosition = layoutManager.findLastCompletelyVisibleItemPosition()
        for (i in firstPosition..lastPosition) {
          setMarquee(layoutManager, i, true)
        }
      }
    }

    fun setNestedPromotionMarquee(recyclerView: RecyclerView) {
      val scrollBounds = Rect()
      if (recyclerView?.adapter != null &&
        recyclerView.adapter?.itemCount != null &&
        recyclerView.adapter?.itemCount!! > 0
      ) {
        for (i in 0 until recyclerView.adapter?.itemCount!!) {
          var view = recyclerView?.findViewHolderForAdapterPosition(i)?.itemView
          val promotionView = view?.findViewById<TextView>(R.id.tv_restaurant_sub_info)

          val isSelected = if (promotionView?.getLocalVisibleRect(scrollBounds) == true) {
            // bottom = 0 is leave from top
            // top <= cart bar height is leave from bottom
            // (scrollBounds.bottom < 0) || (scrollBounds.top < 0)
            scrollBounds.bottom >= 0 && scrollBounds.top >= 0
          } else {
            false
          }

          promotionView?.let {
            it.isSelected = isSelected
          }
        }
      }
    }

    private fun setMarquee(
      layoutManager: LinearLayoutManager,
      i: Int,
      run: Boolean
    ) {
      val view = layoutManager.findViewByPosition(i)
      val promotionView = view?.findViewById<TextView>(R.id.tv_restaurant_sub_info)

      // the promotion view to visible in screen
      val scrollBounds = Rect()
      val isSelected = if (promotionView?.getLocalVisibleRect(scrollBounds) == true) {
        // bottom = 0 is leave from top
        // top <= cart bar height is leave from bottom
        // (scrollBounds.bottom < 0) || (scrollBounds.top < 0)
        scrollBounds.bottom >= 0 && scrollBounds.top >= 0
      } else {
        false
      }

//            Log.i("thom", "${promotionView?.text}  ${scrollBounds.bottom} " +
//                    "- ${scrollBounds.top} = ${scrollBounds.height()} ")

      promotionView?.let {
        it.isSelected = (run && isSelected)
      }
    }
  }
}
