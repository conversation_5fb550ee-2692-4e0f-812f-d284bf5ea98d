package com.ricepo.app.listener

import androidx.lifecycle.LifecycleCoroutineScope
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.di.entrypoint.CombineApiPoint
import com.ricepo.app.model.Order
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseApplication
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Food
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 2/3/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object LifecycleNetworkListener {

  private val api: CombineRestApi by lazy {
    EntryPointAccessors.fromApplication(
      BaseApplication.context,
      CombineApiPoint::class.java
    ).injectCombineApi()
  }

  fun checkRatingOrder(
    lifecycleScope: LifecycleCoroutineScope,
    callback: (order: Order?) -> Unit
  ): Job {
    return lifecycleScope.launchWhenResumed {
      val order = withContext(Dispatchers.IO) {
        val customerId = CustomerCache.getCustomerSuspend()?.id
        if (customerId == null) {
          null
        } else {
          try {
            api.getRatingOrder(customerId)
          } catch (e: Exception) {
            null
          }
        }
      }
      if (order is Order) {
        callback(order)
      }
    }
  }

  /**
   * log add food to cart
   */
  fun logAddFood(restId: String?, foodIds: List<String?>?) {
    val restId = restId ?: return
    val foodIds = foodIds?.filterNotNull() ?: return
    GlobalScope.launch {
      try {
        val params = mapOf("food" to foodIds)
        api.logAddFood(restId, params)
      } catch (e: Exception) {
        null
      }
    }
  }

  /**
   * log add food to cart
   */
  fun logAddFood(food: Food?) {
    val foodId = food?.id ?: return
    val restaurantId = food?.restaurant?.id ?: return
    val foodIds = listOf(foodId)
    logAddFood(restaurantId, foodIds)
  }

  suspend fun updateCustomer() {
    val customer = CustomerCache.getCustomerSuspend() ?: return
    try {
      val cust = api.getCustomer(customerId = customer.id)
      CustomerCache.saveCustomer(cust)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  suspend fun updateCustomer(customer: Customer?): Customer? {
    val customer = customer ?: return null
    try {
      val cust = api.getCustomer(customerId = customer.id)
      CustomerCache.saveCustomer(cust)
      return cust
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return null
  }
}
