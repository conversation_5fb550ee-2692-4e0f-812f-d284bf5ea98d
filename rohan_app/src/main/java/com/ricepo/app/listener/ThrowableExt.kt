package com.ricepo.app.listener

import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.parseByNetwork
import java.lang.Exception

//
// Created by <PERSON><PERSON> on 8/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun Throwable.parseByBuzNetwork(): Exception {
  return this.parseByNetwork {
    if (ErrorCode.AUTH_FAILED == it) {
      // auth failed to logout
      CustomerCache.clearWithLogout()
    }
  }
}
