package com.ricepo.app

import android.content.res.Configuration
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.ricepo.app.pattern.payment.PaypalInitializer
import com.ricepo.app.startup.FirebaseInitializer
import com.ricepo.app.startup.LeakInitializer
import com.ricepo.app.startup.LifecycleInitializer
import com.ricepo.app.startup.MapInitializer
import com.ricepo.app.startup.MonitorInitializer
import com.ricepo.app.startup.RouterInitializer
import com.ricepo.app.startup.StripeInitializer
import com.ricepo.app.startup.TimeZoneInitializer
import com.ricepo.app.workaround.GoogleMapWorkaround
import com.ricepo.base.BaseApplication
import com.ricepo.base.view.PullRefreshHeader
import com.ricepo.network.NetworkDebugTools
import com.ricepo.style.ThemeUtil
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import dagger.hilt.android.HiltAndroidApp
import io.branch.referral.Branch

/**
 * the app global configuration
 */
@HiltAndroidApp
class RicepoApplication : BaseApplication() {

  override fun onCreate() {
    super.onCreate()
    GoogleMapWorkaround.workaround(this)
    configCrashlytics()
    NetworkDebugTools.init(this)
    // library initializer
    RouterInitializer.init(this)
    MonitorInitializer.init(this)
    FirebaseInitializer.init(this)
    LifecycleInitializer.init(this)
    MapInitializer.init(this)
    StripeInitializer.init(this)
    TimeZoneInitializer.init(this)
    LeakInitializer.init(this)
    PaypalInitializer.init(this)
  }

  private fun configCrashlytics() {
    FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(!BuildConfig.DEBUG)
  }

  override fun afterInitDeviceId(id: String?) {
    super.afterInitDeviceId(id)
    // test mode
    if (BuildConfig.DEBUG) {
      // Branch.enableTestMode()
      Branch.enableLogging()
    }
    // Initialize the Branch SDK
    Branch.getAutoInstance(this)
  }

  init {
    SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, _ -> PullRefreshHeader(context) }
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    ThemeUtil.changeThemeDark(newConfig)
  }
}
