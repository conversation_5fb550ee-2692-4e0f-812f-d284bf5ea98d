package com.ricepo.app

import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.os.PersistableBundle
import android.text.SpannableStringBuilder
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.constraintlayout.motion.widget.MotionLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.ktx.Firebase
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.ActivityMainBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.coupon.CouponFragment
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.features.profile.ProfileFragment
import com.ricepo.app.restaurant.home.ComposeTabActivity
import com.ricepo.app.restaurant.home.RestaurantFloorFragment
import com.ricepo.app.restaurant.home.RestaurantPickupFragment
import com.ricepo.app.restaurant.home.TabFragment
import com.ricepo.base.BaseApplication
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.simulateClickListener
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantTab
import com.ricepo.base.model.localize
import com.ricepo.base.tools.IntentUtils
import com.ricepo.monitor.log.Logger
import com.ricepo.style.DisplayUtil
import com.ricepo.style.LocaleUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.ricepo.style.view.gone
import com.ricepo.style.view.show
import dagger.hilt.android.AndroidEntryPoint
import io.branch.referral.Branch
import io.branch.referral.validators.IntegrationValidator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_HOME_MAIN)
class MainActivity : ComposeTabActivity() {

  lateinit var binding: ActivityMainBinding

  private var SPLASH_FLAG = "splash_flag"
  private var isSplash: Boolean = true

  private val restaurantFragment: RestaurantFloorFragment by lazy {
    RestaurantFloorFragment.newInstance()
  }
  private val restaurantPickupFragment: RestaurantPickupFragment by lazy {
    RestaurantPickupFragment.newInstance()
  }
  private val couponFragment: CouponFragment by lazy {
    CouponFragment(isTab = true)
  }
  private val profileFragment: ProfileFragment by lazy {
    ProfileFragment(isTab = true)
  }

  private var pageMode = TabMode.MODE_DELIVERY

  val mainViewModel: MainViewModel by viewModels()

  val menuMapper: MenuMapper by lazy {
    MenuMapper()
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    // attention the BaseActivity ImmersionBar
    super.onCreate(savedInstanceState)
    binding = ActivityMainBinding.inflate(layoutInflater)
    setContentView(binding.root)

    isSplash = intent.getBooleanExtra(FeaturePageConst.PARAM_LAUNCH_ENTRANCE, true)
    savedInstanceState?.getBoolean(SPLASH_FLAG)?.let {
      isSplash = it
    }

    initSplashAnimation(binding)

    setupListener()
    handleIntent()

    initTabState(
      binding = binding.inRestaurantTab,
      mainViewModel = mainViewModel
    )

    DisplayUtil.logDensity(this)
  }

  override fun onStart() {
    super.onStart()
    initBranch()
  }

  private fun initBranch() {
    try {
      if (BuildConfig.DEBUG) {
        IntegrationValidator.validate(this)
      }
      // initializing session on user's behalf (onActivityResumed called but SESSION_STATE = UNINITIALISED)
      // set the disable onResume of BranchActivityLifecycleObserver
      Branch.expectDelayedSessionInitialization(true)

      // solved the intent.get is null
      val intent = intent ?: return

      intent.putExtra("branch_force_new_session", true)

      Branch.sessionBuilder(this).withCallback(branchReferralInitListener)
        .withData(intent?.data).init()
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    if (ThemeUtil.changeThemeDark(newConfig)) {
      recreate()
    }
  }

  override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
    super.onSaveInstanceState(outState, outPersistentState)
    outState.putBoolean(SPLASH_FLAG, isSplash)
  }

  /**
   * init the splash screen showing
   */
  private fun initSplashAnimation(binding: ActivityMainBinding) {
    binding.flMotionLaunch.isVisible = isSplash
    if (isSplash) {
      val originVisibility = this.window.decorView.systemUiVisibility
      hideSystemUIAndNavigation(this)
      binding.motionLaunch.setTransitionListener(object : MotionLayout.TransitionListener {
        override fun onTransitionTrigger(
          motionLayout: MotionLayout?,
          triggerId: Int,
          positive: Boolean,
          progress: Float
        ) {}

        override fun onTransitionStarted(
          motionLayout: MotionLayout?,
          startedId: Int,
          endId: Int
        ) {}

        override fun onTransitionChange(
          motionLayout: MotionLayout?,
          startId: Int,
          endId: Int,
          progress: Float
        ) {}

        override fun onTransitionCompleted(motionLayout: MotionLayout?, currentId: Int) {
          if (currentId == R.id.launch_end) {
            binding.flMotionLaunch.isVisible = false
            isSplash = false
          }
          <EMAIL> = originVisibility
          loadRestaurantFragment()
        }
      })
    }
  }

  private fun startSplashAnimation() {
    if (isSplash) {
      binding.motionLaunch.startLayoutAnimation()
    } else {
      loadRestaurantFragment()
    }
  }

  override fun onWindowFocusChanged(hasFocus: Boolean) {
    super.onWindowFocusChanged(hasFocus)
    if (hasFocus && isSplash) {
      hideSystemUIAndNavigation(this)
    }
  }

  private fun hideSystemUIAndNavigation(activity: Activity) {
    val decorView: View = activity.window.decorView
    decorView.systemUiVisibility =
      (
        View.SYSTEM_UI_FLAG_IMMERSIVE
          // Set the content to appear under the system bars so that the
          // content doesn't resize when the system bars hide and show.
          or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
          or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN // Hide the nav bar and status bar
          or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    // singleTask must store the new intent unless getIntent() will return the old one
    setIntent(intent)
    handleIntent()

    try {
      // if activity is in foreground (or in backstack but partially visible) launching the same
      // activity will skip onStart, handle this case with sessionBuilder()...reInit()
      // will re-initialize only if ""branch_force_new_session=true"" intent extra is set
      Branch.sessionBuilder(this).withCallback(branchReferralInitListener).reInit()
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private val branchReferralInitListener =
    Branch.BranchReferralInitListener { referringParams, error ->
      Log.i("thom", "branch params $referringParams")
      if (error == null) {
        handleBranchParams(referringParams)
      } else {
        Log.e("thom", "branch error ${error.message}")
      }
    }

  private fun handleBranchParams(referringParams: JSONObject?) {
    try {
      if (referringParams?.has("referCode") == true) {
        // profile always get the last params and change firstReferringParamsSync
        referringParams.getString("referCode")?.let {
          Branch.getInstance().setIdentity(BaseApplication.mDeviceId)
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun handleIntent() {
    val isNotification = intent?.getBooleanExtra(
      FeaturePageConst.PARAM_HOME_NOTIFICATION, false
    )
    Log.i("thom", "MainActivity intent ${intent?.toString()}")
    if (isNotification == true) {
      lifecycleScope.launch {
        // group order interception
        val groupOrder = GroupOrderCache.getOrderSuspend()
        if (groupOrder != null) {
          FeaturePageRouter.navigateMenu(
            Restaurant(groupOrder.restaurant.id),
            groupId = groupOrder.groupId
          )
          return@launch
        }
        val params = intent.getSerializableExtra(FeaturePageConst.PARAM_HOME_NOTIFICATION_DATA)
        try {
          handleNotificationPage(params as HashMap<String, String>)
        } catch (e: Exception) {
          e.printStackTrace()
          loadPage()
        }
      }
    } else {
      listenDynamicLink(intent)
      loadPage()
    }
  }

  private fun handleNotificationPage(params: HashMap<String, String>) {
    val intent = FeaturePageRouter.handleNotificationIntent(this, params)
    startActivity(intent)
  }

  /**
   * listen the firebase dynamic link
   */
  private fun listenDynamicLink(intent: Intent?) {
    intent?.let {
      Firebase.dynamicLinks
        .getDynamicLink(intent)
        .addOnSuccessListener { pendingDynamicLinkData ->
          var deepLink: Uri? = null
          Log.i("thom", "deepLinkData = ${pendingDynamicLinkData?.link}")
          if (pendingDynamicLinkData?.link != null) {
            deepLink = pendingDynamicLinkData.link
            handleDynamicLink(deepLink)
            // clear intent when change dark will invoke onNewIntent
            setIntent(null)
          }
        }
        .addOnFailureListener { e ->
          Log.w("thom", "getDynamicLink:onFailure", e)
        }
    }
  }

  private fun loadPage() {
    startSplashAnimation()
  }

  private fun loadRestaurantFragment() {
    val lang = LocaleUtil.getLanguage()
    if (lang.isNullOrEmpty()) {
      FeaturePageRouter.navigateLanguage(true)
      <EMAIL>()
      return
    }
    //  Can not perform this action after onSaveInstanceState
    if (isFinishing) return

    // default delivery for restaurant tab
    initTab()

    // refer install from play store first
    handleBranchParams(Branch.getInstance().firstReferringParams)
  }

  /**
   * firebase dynamic link handler
   */
  private fun handleDynamicLink(deepLink: Uri?) {
    lifecycleScope.launch {
      // stop navigate if link uri is empty
      val deepLink = deepLink ?: return@launch
      // stop navigate if language exists
      val lang = LocaleUtil.getLanguage()
      if (lang.isNullOrEmpty()) {
        return@launch
      }
      // group order interception
      val groupOrder = GroupOrderCache.getOrderSuspend()
      if (groupOrder != null) {
        FeaturePageRouter.navigateMenu(
          Restaurant(groupOrder.restaurant.id),
          groupId = groupOrder.groupId
        )
        return@launch
      }
      // stop navigate if user address is empty
      val address = withContext(Dispatchers.IO) {
        AddressCache.getAddressSuspend()
      }
      if (address != null) else return@launch
      val dlPage = deepLink.getQueryParameter("page")

      // menu group order
      if (dlPage == "groupOrder") {
        val groupId = deepLink.getQueryParameter("groupId")
        val restId = deepLink.getQueryParameter("restId")

        val restaurant = Restaurant(id = restId)
        FeaturePageRouter.navigateMenu(restaurant, groupId = groupId)
      }

      // menu
      if (dlPage == "menu") {
        val restId = deepLink.getQueryParameter("restId")

        val restaurant = Restaurant(id = restId)
        FeaturePageRouter.navigateMenu(restaurant)
      }

      // chat
      if (dlPage == "chat") {
        navigateSupportChat()
      }
    }
  }

  private fun navigateSupportChat() {
    FeaturePageRouter.navigateOrderSupportChat(this@MainActivity)
  }

  override fun onBackPressed() {
    IntentUtils.backHome(this)
    // logger to save
    Logger.close()
  }

  private fun setupListener() {
    // switch fragment
    binding.ivSearch.clickWithTrigger {
      if (pageMode == TabMode.MODE_PICKUP) {
        restaurantPickupFragment.navigateSearch()
      } else {
        restaurantFragment.navigateSearch()
      }
    }

    binding.tvAddress.touchWithTrigger { v, event ->
      if (event?.action == MotionEvent.ACTION_UP) {
        FeaturePageRouter.navigateAddress(this)
      }
      true
    }

    binding.ivProfile.clickWithTrigger {
      FeaturePageRouter.navigateProfile()
    }
  }

  override fun setTitle(title: String?) {
    binding.tvAddress.text = title ?: ResourcesUtil.getString(com.ricepo.style.R.string.address)
  }

  fun showSearchView(visibility: Int) {
    if (visibility == View.GONE) {
      binding.ivSearch.visibility = View.INVISIBLE
    } else {
      binding.ivSearch.visibility = visibility
    }
  }

  override fun isSearchVisible(): Boolean {
    return binding.ivSearch.visibility == View.VISIBLE
  }

  @Deprecated("Deprecated in Java")
  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (restaurantPickupFragment.isAdded) {
      restaurantPickupFragment.onActivityResult(requestCode, resultCode, data)
    }
    // todo why jump to chat after login ?
//    if (resultCode == Activity.RESULT_OK) {
//      if (requestCode == FeaturePageConst.REQUEST_CODE_LOGIN) {
//        FeaturePageRouter.navigateOrderSupportChat(this)
//      }
//    }
  }

  private fun showFragment(
    fragment: TabFragment,
    type: String? = null,
    refresh: Boolean = true,
    init: Boolean = false
  ) {
    with(supportFragmentManager) {
      val ft = beginTransaction()
      hideFragment(ft)
      if (
        !fragments.contains(fragment) &&
        !fragment.isAdded
      ) {
        if (init) {
          ft.replace(R.id.fragment_restaurant_container, fragment)
        } else {
          ft.add(R.id.fragment_restaurant_container, fragment)
        }
      } else {
        if (refresh) {
          fragment.refreshTab(type)
        }
      }
      ft.show(fragment)
      ft.commitAllowingStateLoss()
      executePendingTransactions()
      fitStatusBar()
    }
  }

  private fun initTab() {
    pageMode = TabMode.MODE_DELIVERY
    showFragment(restaurantFragment, TabMode.MODE_DELIVERY, init = true)
  }

  /**
   * show the menu cart
   */
  private fun showCart(isShow: Boolean) {
    val params = binding.inMenuCart.clMenuCart.layoutParams
    if (params is LinearLayout.LayoutParams) {
      if (isShow) {
        binding.inMenuCart.tvDelivery.isVisible = false
        params.height = LinearLayout.LayoutParams.WRAP_CONTENT
        // hide the cart bar
        binding.inMenuCart.clMenuCart.isVisible = false
      } else {
        params.height = 0
        binding.inMenuCart.clMenuCart.isVisible = false
      }
      binding.inMenuCart.clMenuCart.layoutParams = params
    }
  }

  /**
   * check the restaurant cart
   */
  fun checkMenuCart() {
    lifecycleScope.launchWhenStarted {
      // the newest cart
      val cart = RestaurantCartCache.getRestaurantCartSuspend(null, true)
      val restaurant = cart?.restaurant
      if (restaurant != null && cart != null && cart.cartList?.isNotEmpty() == true) {
        val allCarts = GroupOrderCache.getAllCartsSuspend(cart.cartList, restaurant)
        showCart(true)
        val triple = menuMapper.mapCartUiModel(allCarts, cart)
        val uiModels = mutableListOf<CartUiModel>()
        restaurant.name?.let {
          val name = SpannableStringBuilder(it.localize())
          uiModels.add(CartUiModel.CartMenuInfoUiModel(name))
        }
        uiModels.addAll(triple.first)
        val cartLayoutManager = LinearLayoutManager(this@MainActivity)
        cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
        val cartAdapter = CartAdapter(uiModels)
        binding.inMenuCart.rcvMenuCart.apply {
          layoutManager = cartLayoutManager
          adapter = cartAdapter
        }
        // simulation of external click events
        binding.inMenuCart.rcvMenuCart.simulateClickListener {
          binding.inMenuCart.clMenuCart.performClick()
        }
        binding.inMenuCart.clMenuCart.clickWithTrigger { _ ->
          FeaturePageRouter.navigateCheckoutAfterSubscription(this@MainActivity, restaurant)
        }

        // show cart new bar
        val cartRestPair = menuMapper.mapCartCount(allCarts, cart)
        if (cartRestPair.first.isNullOrEmpty()) {
          binding.inRestaurantTab.layRestaurantCart.isVisible = false
        } else {
          binding.inRestaurantTab.root.isVisible = true
          binding.inRestaurantTab.layRestaurantCart.isVisible = true
          binding.inRestaurantTab.tvCartName.text = cartRestPair.first
          binding.inRestaurantTab.tvCartCount.text = cartRestPair.second.toString()
          binding.inRestaurantTab.layRestaurantCart.clickWithTrigger { _ ->
            FeaturePageRouter.navigateCheckoutAfterSubscription(
              this@MainActivity,
              restaurant
            )
          }
        }
      } else {
        showCart(false)
        binding.inRestaurantTab.layRestaurantCart.isVisible = false
      }
    }
  }

  override fun refreshTab(type: String?) {
    if (type == RestaurantTab.TYPE_PICKUP) {
      showFragment(restaurantPickupFragment, type = type)
    } else {
      showFragment(restaurantFragment, type = type)
    }
  }

  override fun refreshTab(tab: HomeTab) {
    when (tab) {
      is HomeTab.Home -> {
        showFragment(
          fragment = if (
            tab.innerSelectedType == RestaurantTab.TYPE_PICKUP
          ) {
            restaurantPickupFragment
          } else {
            restaurantFragment
          },
          refresh = false,
          type = tab.innerSelectedType
        )
        if (tab.innerSelectedType == RestaurantTab.TYPE_PICKUP) {
          showFragment(restaurantPickupFragment, refresh = false)
        } else {
          showFragment(restaurantFragment, refresh = false)
        }
        binding.homeBar.show()
      }
      is HomeTab.Order -> {
        showFragment(profileFragment)
        binding.homeBar.gone()
      }
      is HomeTab.Wallet -> {
        showFragment(couponFragment)
        binding.homeBar.gone()
      }
    }
  }

  private fun hideFragment(ft: FragmentTransaction) {
    ft.hide(restaurantFragment)
    ft.hide(restaurantPickupFragment)
    ft.hide(profileFragment)
    ft.hide(couponFragment)
  }
}
